import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, test, vi } from 'vitest';

import { DepositAccountRegisterView } from '@/views/deposit/ui/DepositAccountRegisterView';

// verifyWithdrawAccount 타입 정의
type VerifyWithdrawAccountParams = {
  bankCode: string;
  accountNumber: string;
  depositorName: string;
};

type VerifyWithdrawAccountResponse = {
  result: boolean;
  data?: {
    depositorName: string;
  };
};

// ResizeObserver 모킹
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// verifyWithdrawAccount API 모킹
vi.mock('@/entities/deposit/api/verifyWithdrawAccount', () => {
  const mockVerifyWithdrawAccount = vi
    .fn()
    .mockImplementation(
      async (params: VerifyWithdrawAccountParams): Promise<VerifyWithdrawAccountResponse> => {
        return {
          result: true,
        };
      },
    );

  return {
    verifyWithdrawAccount: mockVerifyWithdrawAccount,
  };
});

vi.mock('@/entities/deposit/api/createVirtualAccount', () => {
  const mockCreateVirtualAccount = vi.fn().mockImplementation(() => {
    return {
      bankCode: '011',
      accountNumber: '**********',
      depositorName: '홍길동',
    };
  });

  return {
    createVirtualAccount: mockCreateVirtualAccount,
  };
});

// 필요한 모킹 추가
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}));

vi.mock('next-auth/react', () => ({
  useSession: vi.fn(() => ({
    data: {
      user: {
        accessToken: 'qweqqeqwqwe',
        accessTokenExpiredAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
        refreshToken: 'qweqqeqwqwe',
        refreshTokenExpiredAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
        userId: '20f51aa3-6be3-46a9-8d11-6d21ddcafe5d',
      },
    },
    status: 'authenticated',
  })),
  signIn: vi.fn(),
  signOut: vi.fn(),
}));

vi.mock('@/features/users/api/fetchUser', () => ({
  fetchUser: () => ({
    id: '20f51aa3-6be3-46a9-8d11-6d21ddcafe5d',
    kycStatus: 'NON_KYC',
    account: '<EMAIL>',
    role: 2,
    userProfile: {
      name: '홍길동',
    },
  }),
}));

describe('Account Register', () => {
  beforeEach(() => {
    render(<DepositAccountRegisterView />);
  });

  const user = userEvent.setup();

  test('1.예치금 계좌 개설 발급 동의', async () => {
    expect(screen.getByText('예치금 계좌 개설 발급 동의')).toBeDefined();

    const checkbox = screen.getByTestId('deposit-account-register-terms-checkbox');

    expect(checkbox).toBeDefined();

    // 초기에는 버튼이 disabled 상태여야 함
    const nextButton = screen.getByTestId('deposit-account-register-terms-next-button');

    expect(nextButton.getAttribute('disabled')).toBe('');

    await user.click(checkbox);

    // 체크박스 클릭 후 버튼이 enabled 상태여야 함
    expect(nextButton.getAttribute('disabled')).toBeNull();

    await user.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('출금계좌 등록')).toBeDefined();
    });
  });

  test('2.출금계좌 등록', async () => {
    expect(screen.getByText('출금계좌 등록')).toBeDefined();

    const bankCodeSelect = screen.getByTestId('withdraw-account-register-bank-code-select');

    expect(bankCodeSelect).toBeDefined();

    await user.click(bankCodeSelect);

    const bankCodeOption = screen.getByTestId(
      'withdraw-account-register-bank-code-select-option-011',
    );

    expect(bankCodeOption).toBeDefined();

    await user.click(bankCodeOption);

    const accountNumberInput = screen.getByTestId('withdraw-account-register-account-number-input');
    expect(accountNumberInput).toBeDefined();

    await user.type(accountNumberInput, '**********');

    const nextButton = screen.getByTestId('withdraw-account-register-next-button');
    expect(nextButton.getAttribute('disabled')).toBeNull();

    await user.click(nextButton);

    // API 응답 후 UI 업데이트 확인
    await waitFor(() => {
      expect(screen.getByText('출금계좌 인증 완료')).toBeDefined();
    });
  });

  test('3.출금계좌 인증 확인 및 가상계좌 발급', async () => {
    expect(screen.getByText('출금계좌 인증 완료')).toBeDefined();

    const accountInfoInput = screen.getByTestId('withdraw-account-confirm-account-info');

    expect(accountInfoInput).toBeDefined();

    const nextButton = screen.getByTestId('withdraw-account-confirm-next-button');

    expect(nextButton.getAttribute('disabled')).toBeNull();

    await user.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('예치금 계좌 개설 발급 완료')).toBeDefined();
    });
  });
});
