import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, test, vi } from 'vitest';

import { OcrView } from '@/views/ocr/ui/OcrView';

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}));

vi.mock('@/features/ocr/lib/maskPolygonAreas', () => ({
  maskPolygonAreas: async (imageUrl: string) => imageUrl,
}));

global.fetch = vi.fn();
const mockFetch = global.fetch as any;

describe('OCR View', () => {
  beforeEach(() => {
    mockFetch.mockReset();
    global.URL.createObjectURL = vi.fn(() => 'https://www.numit.kr/logo/naver.png');
  });

  render(<OcrView />);

  const user = userEvent.setup();

  test('1. 신분증 업로드 및 신분증 인식', async () => {
    // Mock API Response
    mockFetch.mockImplementationOnce(() =>
      Promise.resolve({
        json: () =>
          Promise.resolve({
            images: [
              {
                name: '주민등록증.jpeg',
                inferDetailType: 'IC',
                idCard: {
                  result: {
                    ic: {
                      personalNum: [
                        {
                          text: '123456-1234567',
                          maskingPolys: [
                            {
                              vertices: [],
                            },
                          ],
                        },
                      ],
                      name: [{ text: '홍길동' }],
                      issueDate: [{ text: '2020.01.01' }],
                    },
                  },
                },
              },
            ],
          }),
      }),
    );

    // 초기 화면 확인
    expect(screen.getByTestId('ocr-title')).toBeDefined();
    expect(screen.getByTestId('ocr-upload-button')).toBeDefined();

    // 파일 업로드 버튼 클릭
    await user.click(screen.getByTestId('ocr-upload-button'));

    // 파일 선택
    const fileInput = screen.getByTestId('ocr-file-input');
    const testFile = new File(['dummy content'], '주민등록증.jpeg', { type: 'image/jpeg' });
    await user.upload(fileInput, testFile);

    // API 호출 확인
    expect(mockFetch).toHaveBeenCalledWith('/api/ocr/document', {
      method: 'POST',
      body: expect.any(FormData),
    });

    // 렌더링 업데이트를 기다림
    await waitFor(() => {
      expect(screen.getByDisplayValue('홍길동')).toBeDefined();
      expect(screen.getByDisplayValue('123456-1******')).toBeDefined();
      expect(screen.getByTestId('ocr-verify-button')).toBeDefined();
    });
  });

  test('2. 신분증 인증 확인', async () => {
    mockFetch.mockImplementationOnce(() =>
      Promise.resolve({
        json: () =>
          Promise.resolve({
            verifyResult: {
              result: 'SUCCESS',
            },
          }),
      }),
    );

    // 인증 버튼 클릭
    const verifyButton = screen.getByTestId('ocr-verify-button');
    await user.click(verifyButton);

    // 인증 완료 확인
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/ocr/verify', {
        method: 'POST',
        body: expect.any(String),
      });
    });

    await waitFor(() => {
      expect(screen.getByText('본인 실명인증 완료')).toBeDefined();
    });
  });
});
