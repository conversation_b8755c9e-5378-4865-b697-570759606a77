'use client';

import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/solid';

import { formatAccountNumber } from '@/features/deposit/lib/formatAccountNumber';
import { getBankName } from '@/features/deposit/lib/getBankName';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { WithdrawCardItem } from '@/entities/assets/ui/WithdrawCardItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Checkbox } from '@/shared/ui/shadcn/checkbox';
import { Form } from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';
import { Separator } from '@/shared/ui/shadcn/separator';

import { useDepositWithdraw } from '../model/useDepositWithdraw';

export const DepositWithdrawView = () => {
  const {
    form,
    verifyMobileCode,
    isEmailVerificationVisible,
    verificationTimerText,
    verificationTimer,
    account,
    allWithdraw,
    verifyContact,
    disabled,
    handleWithdraw,
    isLoading,
    handleIdentityVerification,
    isVerify,
    toggleVerify,
    isNotWithdrawableAmount,
  } = useDepositWithdraw();

  const { CASHCOMMA } = utilFormats();

  const { username } = useFetchUser();

  const { watch, getFieldState, register } = form;

  const notWithdrawableAmount =
    (account?.depositAmountWithin24h || 0) +
    (account?.subscriptionMarginSummary?.totalLockedMargin || 0);

  return (
    <section className="mx-auto my-8 max-w-screen-test pb-20 sm:my-[100px] sm:space-y-14 sm:pb-0">
      <h2 className="text-28 hidden sm:block">예치금 출금하기</h2>
      <div className="space-y-6 sm:space-y-12">
        <div className="all-padding space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="sm:text-20 font-semibold">출금계좌</h4>
            <button
              onClick={toggleVerify}
              className="text-xs font-semibold text-gray-500 underline"
            >
              출금계좌 변경
            </button>
          </div>
          <div className="flex flex-col items-center gap-1 rounded-lg border border-gray-300 bg-gray-50 py-6 sm:gap-2 sm:py-8">
            <p className="text-sm font-semibold text-gray-700 sm:text-base">
              {getBankName(account?.masterAccountInstCode)} {username}
            </p>
            <p className="sm:text-18 text-base font-semibold text-primary-500">
              {formatAccountNumber(account?.masterAccountNumberCode || '')}
            </p>
          </div>
        </div>

        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        <div className="all-padding space-y-3">
          <h4 className="sm:text-20 font-semibold">출금하기</h4>
          <div className="rounded-lg border-gray-300 sm:border">
            <div className="space-y-3 sm:space-y-6 sm:p-8">
              <WithdrawCardItem
                label="내 예치금"
                value={`${CASHCOMMA(account?.currentBalance || 0)}원`}
              />
              <WithdrawCardItem
                label="출금 불가 금액"
                value={`${CASHCOMMA(notWithdrawableAmount)}원`}
              />
              <WithdrawCardItem label="출금 수수료" value="0원" />
              <Separator className="bg-gray-200" />
              <WithdrawCardItem
                label="출금 가능 금액"
                value={`${CASHCOMMA(account?.currentNetBalance || 0)}원`}
              />
            </div>

            <div className="mt-6 space-y-3 rounded-b-lg sm:mt-0 sm:bg-gray-50 sm:p-6">
              <div className="relative">
                <Input
                  max={account?.currentBalance || 0}
                  placeholder="출금 금액을 입력해 주세요."
                  {...register('withdrawAmount')}
                  className="pr-12"
                  type="number"
                />
                <div className="absolute right-4 top-0 flex h-full items-center gap-4">
                  <Separator orientation="vertical" className="h-6 bg-gray-200" />
                  <p className="text-gray-700">원</p>
                </div>
              </div>
              <div
                className={`flex ${isNotWithdrawableAmount ? 'justify-between' : 'justify-end'} items-center`}
              >
                {isNotWithdrawableAmount && (
                  <p className="flex items-center gap-1 text-xs font-semibold text-red-500">
                    <ExclamationCircleIcon className="h-4 w-4" /> 출금 가능 금액을 다시
                    확인해주세요.
                  </p>
                )}

                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={watch('withdrawAmount') === account?.currentBalance}
                    onCheckedChange={(checked) => allWithdraw(checked as boolean)}
                    className="h-5 w-5 bg-white shadow-none"
                  />
                  <label htmlFor="withdraw" className="text-gray-700">
                    전액 출금
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="all-padding hidden sm:block">
          <Separator className="bg-gray-200" orientation="horizontal" />
        </div>
        <Separator className="h-3 bg-blue-gray-00 sm:hidden" orientation="horizontal" />
        <div className="all-padding space-y-8">
          <p className="text-center text-lg font-semibold">
            출금을 위해 휴대폰 인증을 진행해 주세요.
          </p>
          <Form {...form}>
            <form className="space-y-3">
              <div className="flex gap-2">
                <InputField
                  disabled={watch('isMobileVerified')}
                  form={form}
                  name="mobileNumber"
                  placeholder="휴대전화번호"
                  type="tel"
                />
                <SecondaryButton
                  onClick={() => verifyContact(watch('mobileNumber'))}
                  type="button"
                  disabled={
                    !(watch('mobileNumber') && !getFieldState('mobileNumber').invalid) ||
                    watch('isMobileVerified')
                  }
                  text={isEmailVerificationVisible ? '재요청' : '인증하기'}
                  className="h-[44px] min-w-[85px] sm:h-12"
                />
              </div>
              {isEmailVerificationVisible && (
                <div className="flex gap-2">
                  <div className="relative w-full">
                    <InputField
                      form={form}
                      disabled={watch('isMobileVerified')}
                      name="mobileVerifiedCode"
                      placeholder="인증번호 입력"
                      type="text"
                    />
                    <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                      {verificationTimerText}
                    </span>
                  </div>
                  <SecondaryButton
                    type="button"
                    text="확인"
                    disabled={getFieldState('mobileVerifiedCode').invalid || !verificationTimer}
                    className="h-[44px] min-w-[85px] sm:h-12"
                    onClick={verifyMobileCode}
                  />
                </div>
              )}

              {watch('isMobileVerified') && (
                <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                  <CheckCircleIcon className="h-4 w-4" color="green" />
                  휴대폰 인증 완료
                </div>
              )}
            </form>
          </Form>
        </div>
      </div>
      <div className="all-padding fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static">
        <PrimaryButton
          disabled={disabled() || isLoading}
          onClick={handleWithdraw}
          text="출금하기"
          className="h-12 w-full text-base"
        />
      </div>
      {isVerify && (
        <ConfirmDialog
          isOpen={isVerify}
          handleOpen={toggleVerify}
          title="출금계좌 변경"
          isCancelButton
          text="확인"
          description={`출금계좌 변경을 위해 휴대폰 본인인증이 필요합니다. \n 인증하시겠습니까?`}
          handleAction={handleIdentityVerification}
        />
      )}
    </section>
  );
};
