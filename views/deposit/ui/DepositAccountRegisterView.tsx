'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';

import { useAccountRegister } from '@/features/deposit/model/useAccountRegister';
import { AccountRegisterComplete } from '@/features/deposit/ui/AccountRegisterComplete';
import { DepositAccountRegisterTerms } from '@/features/deposit/ui/DepositAccountRegisterTerms';
import { WithdrawAccountConfirm } from '@/features/deposit/ui/WithdrawAccountConfirm';
import { WithdrawAccountRegisterForm } from '@/features/deposit/ui/WithdrawAccountRegisterForm';

import { DepositSteps } from '@/entities/assets/types';

import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';

export const DepositAccountRegisterView = () => {
  const {
    handleStep,
    isStep,
    form,
    handleCreateVirtualAccount,
    virtualAccount,
    goToCallbackUrl,
    isAgree,
    toggleAgree,
    isLoading,
    isIdentityVerification,
    toggleIdentityVerification,
    handleIdentityVerification,
  } = useAccountRegister();

  const getHeaderConfig = () => {
    if (isStep(DepositSteps.FORM)) {
      return { mode: HeaderMode.LIST, subTitle: '출금 계좌 등록' };
    } else if (isStep(DepositSteps.CONFIRM)) {
      return { mode: HeaderMode.LIST, subTitle: '출금 계좌 인증 완료' };
    } else if (isStep(DepositSteps.COMPLETE)) {
      return {
        mode: HeaderMode.DETAIL,
        rightComponent: <XMarkIcon className="h-6 w-6" onClick={goToCallbackUrl} />,
      };
    }
    return { mode: HeaderMode.LIST, subTitle: '예치금 가상계좌 개설 발급 동의' };
  };

  usePageHeader(getHeaderConfig());

  return (
    <section className="px-6 sm:px-8 sm:pb-40 ml:px-0">
      {isStep(DepositSteps.TERMS) && (
        <DepositAccountRegisterTerms
          handleStep={() => handleStep(DepositSteps.FORM)}
          isAgree={isAgree}
          toggleAgree={toggleAgree}
        />
      )}
      {isStep(DepositSteps.FORM) && (
        <WithdrawAccountRegisterForm
          form={form}
          handleVerifyAccount={toggleIdentityVerification}
          isLoading={isLoading}
        />
      )}
      {isStep(DepositSteps.CONFIRM) && (
        <WithdrawAccountConfirm
          form={form}
          handleCreateVirtualAccount={handleCreateVirtualAccount}
          isLoading={isLoading}
        />
      )}
      {isStep(DepositSteps.COMPLETE) && (
        <AccountRegisterComplete
          virtualAccount={virtualAccount}
          goToCallbackUrl={goToCallbackUrl}
        />
      )}
      <ConfirmDialog
        isOpen={isIdentityVerification}
        handleAction={handleIdentityVerification}
        handleOpen={toggleIdentityVerification}
        title="휴대폰 본인인증 안내"
        isCancelButton
        description={`출금 계좌 등록을 위해\n휴대폰 본인인증을 진행해 주세요.`}
      />
    </section>
  );
};
