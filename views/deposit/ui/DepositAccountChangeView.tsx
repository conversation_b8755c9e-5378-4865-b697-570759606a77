'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';

import { useAccountChange } from '@/features/deposit/model/useAccountChange';
import { AccountRegisterComplete } from '@/features/deposit/ui/AccountRegisterComplete';
import { WithdrawAccountRegisterForm } from '@/features/deposit/ui/WithdrawAccountRegisterForm';

import { DepositChangeSteps } from '@/entities/assets/types';

import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';

export const DepositAccountChangeView = () => {
  const { form, handleChangeAccount, withdrawAccount, step, isStep, goToCallbackUrl, isLoading } =
    useAccountChange();

  usePageHeader({
    mode: isStep(DepositChangeSteps.FORM) ? HeaderMode.LIST : HeaderMode.DETAIL,
    subTitle: '출금 계좌 변경',
    rightComponent: isStep(DepositChangeSteps.COMPLETE) && (
      <XMarkIcon className="h-6 w-6" onClick={goToCallbackUrl} />
    ),
  });

  return (
    <section className="px-6 sm:px-8 sm:pb-40 ml:px-0">
      {isStep(DepositChangeSteps.FORM) && (
        <WithdrawAccountRegisterForm
          isLoading={isLoading}
          isChange
          form={form}
          handleVerifyAccount={handleChangeAccount}
        />
      )}

      {isStep(DepositChangeSteps.COMPLETE) && (
        <AccountRegisterComplete
          virtualAccount={withdrawAccount}
          goToCallbackUrl={goToCallbackUrl}
          isChange
        />
      )}
    </section>
  );
};
