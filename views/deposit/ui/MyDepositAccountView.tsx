'use client';

import { ArrowDownTrayIcon } from '@heroicons/react/24/solid';

import { DepositNotice } from '@/widgets/deposit/ui/DepositNotice';
import { MyDepositHistoryTable } from '@/widgets/deposit/ui/MyDepositHistoryTable';

import { MobileUserAccount } from '@/features/deposit/ui/MobileUserAccount';
import { MyDepositDIsplay } from '@/features/deposit/ui/MyDepositDIsplay';
import { MyDepositDownloadDialog } from '@/features/deposit/ui/MyDepositDownloadDialog';
import { MyDepositHistorySearchForm } from '@/features/deposit/ui/MyDepositHistorySearchForm';
import { MyDepositColumn } from '@/features/notices/lib/MyDepositColumn';

import { MyDepositHistory } from '@/entities/assets/types';
import { MobileDepositHistoryCard } from '@/entities/assets/ui/MobileDepositHistoryCard';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { EmptyList } from '@/shared/ui/EmptyList';
import { Button } from '@/shared/ui/shadcn/button';
import { Separator } from '@/shared/ui/shadcn/separator';

import { useMyDepositAccount } from '../model/useMyDepositAccount';

export const MyDepositAccountView = () => {
  const {
    account,
    myDepositHistory,
    handleWithdraw,
    isLoadingMyAccount,
    refetchMyAccount,
    downloadVisible,
    donwloadToggle,
    isExistAccount,
    isVisibleAccountDialog,
    goToAccountRegister,
    routerBack,
    ref,
    myDepositHistoryList,
  } = useMyDepositAccount();

  return (
    <div className="flex-1 sm:space-y-10">
      <ContentsTitle title="나의 예치금 가상계좌" className="text-28 mb-10 hidden ml:block" />
      <section>
        <MyDepositDIsplay
          myAccount={account}
          refetch={refetchMyAccount}
          isLoading={isLoadingMyAccount}
          handleWithdraw={handleWithdraw}
          isExistAccount={isExistAccount}
        />
        <MobileUserAccount className="all-padding sm:hidden" />
        <DepositNotice />
        <div className="all-padding hidden sm:block">
          <Separator className="my-10 h-3 bg-blue-gray-00 sm:my-[60px] sm:h-[1px] sm:bg-gray-300" />
        </div>
        <Separator className="my-10 h-3 bg-blue-gray-00" />
        <div className="all-padding space-y-3 sm:space-y-10">
          <div className="flex items-center justify-between">
            <ContentsTitle title="예치금 거래 내역" className="sm:text-28 text-16" />
            {/* Todo: 예치금 거래내역 엑셀다운로드 */}
            <Button
              onClick={donwloadToggle}
              className="h-11 w-11 border border-gray-300 font-semibold sm:h-12 sm:w-[124px]"
            >
              <ArrowDownTrayIcon className="h-5 w-5" />{' '}
              <span className="hidden sm:block">다운로드</span>
            </Button>
          </div>
          <div className="space-y-3 sm:space-y-8">
            <MyDepositHistorySearchForm />
            <div className="hidden sm:block">
              {myDepositHistory && myDepositHistory.list.length > 0 ? (
                <MyDepositHistoryTable<MyDepositHistory, string>
                  columns={MyDepositColumn}
                  data={myDepositHistory}
                  className="!p-6"
                  pagination
                />
              ) : (
                <EmptyList title="입출금 내역이 없습니다." className="sm:mb-60 sm:pt-[136px]" />
              )}
            </div>
            <div className="sm:hidden">
              {myDepositHistoryList?.pages?.map((page) =>
                page.data.map((depositHistory, index) => (
                  <MobileDepositHistoryCard
                    key={depositHistory.depositHistoryId}
                    depositHistory={depositHistory}
                    isLast={index === page.data.length - 1}
                  />
                )),
              )}
              <div ref={ref} />
            </div>
          </div>
        </div>
      </section>
      <MyDepositDownloadDialog
        isOpen={downloadVisible}
        handleOpen={donwloadToggle}
        handleAction={() => {}}
      />
      <ConfirmDialog
        isOpen={isVisibleAccountDialog}
        handleOpen={routerBack}
        isCancelButton={false}
        title="출금 계좌 등록"
        description={`출금 계좌 등록을 위해 예치금 가상계좌 개설이 필요합니다. \n 개설하시겠습니까?`}
        handleAction={goToAccountRegister}
      />
    </div>
  );
};
