import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

import { fetchCorporateMyDepositHistory } from '@/features/assets/api/fetchCorporateMyDepositHistory';
import { fetchInfiniteCorporateMyDepositHistory } from '@/features/assets/api/fetchInfiniteCorporateMyDepositHistory';
import { fetchInfiniteMyDepositHistory } from '@/features/assets/api/fetchInfiniteMyDepositHistory';
import { fetchMyDepositHistory } from '@/features/assets/api/fetchMyDepositHistory';
import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';

export const useMyDepositAccount = () => {
  const { isVisible: downloadVisible, toggleVisibility: donwloadToggle } = useVisibility();
  const { isExistAccount, isGeneral, isCorporate } = useFetchUser();
  const {
    isVisible: isVisibleAccountDialog,
    toggleVisibility: toggleVisibilityAccountDialog,
    closeToggle: closeToggleAccountDialog,
    openToggle: openToggleAccountDialog,
  } = useVisibility();
  const { account, refetch, isLoading, isFetching } = useFetchAccount();
  const searchParams = useSearchParams();
  const searchBeginAt = searchParams.get('searchBeginAt') || '';
  const searchEndAt = searchParams.get('searchEndAt') || '';
  const { screenSize } = useScreenStore();
  const { routerPush, routerBack } = useWebViewRouter();
  const [isInitialized, setIsInitialized] = useState(false);
  const pathname = usePathname();

  const { data: myDepositHistory } = fetchMyDepositHistory(
    {
      page: 0,
      perPage: 10,
      searchBeginAt: searchBeginAt ? new Date(searchBeginAt) : undefined,
      searchEndAt: searchEndAt ? new Date(searchEndAt) : undefined,
    },
    !!isExistAccount && screenSize !== ScreenSize.MOBILE && isGeneral,
  );

  const { data: corporateDepositHistory } = fetchCorporateMyDepositHistory(
    {
      page: 0,
      perPage: 10,
      searchBeginAt: searchBeginAt ? new Date(searchBeginAt) : undefined,
      searchEndAt: searchEndAt ? new Date(searchEndAt) : undefined,
    },
    !!isExistAccount && screenSize !== ScreenSize.MOBILE && isCorporate,
  );

  const {
    data: myDepositHistoryList,
    fetchNextPage,
    hasNextPage,
    isLoading: isLoadingMyDepositHistory,
  } = fetchInfiniteMyDepositHistory(
    {
      page: 0,
      perPage: 10,
      searchBeginAt: searchBeginAt ? new Date(searchBeginAt) : undefined,
      searchEndAt: searchEndAt ? new Date(searchEndAt) : undefined,
    },
    !!isExistAccount && screenSize === ScreenSize.MOBILE && isGeneral,
  );

  const {
    data: corporateDepositHistoryList,
    fetchNextPage: fetchNextPageCorporateDepositHistory,
    hasNextPage: hasNextPageCorporateDepositHistory,
    isLoading: isLoadingCorporateDepositHistory,
  } = fetchInfiniteCorporateMyDepositHistory(
    {
      page: 0,
      perPage: 10,
      searchBeginAt: searchBeginAt ? new Date(searchBeginAt) : undefined,
      searchEndAt: searchEndAt ? new Date(searchEndAt) : undefined,
    },
    !!isExistAccount && screenSize === ScreenSize.MOBILE && isCorporate,
  );

  const [ref, inView] = useInView({
    delay: 100,
    threshold: 0.5,
  });

  useEffect(() => {
    if (isGeneral) {
      if (inView && hasNextPage) {
        fetchNextPage();
      }
    } else if (isCorporate) {
      if (inView && hasNextPageCorporateDepositHistory) {
        fetchNextPageCorporateDepositHistory();
      }
    }
  }, [inView, hasNextPage, hasNextPageCorporateDepositHistory]);

  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true);
      return;
    }

    if (screenSize === ScreenSize.MOBILE) {
      return;
    }

    if (!isExistAccount) {
      openToggleAccountDialog();
    } else if (isExistAccount) {
      closeToggleAccountDialog();
    }
  }, [isExistAccount, screenSize, isInitialized]);

  const handleWithdraw = () => {
    routerPush(`/deposit/withdraw?callbackUrl=${pathname}`);
  };

  const goToAccountRegister = () => {
    routerPush(`/deposit/account-register?callbackUrl=${pathname}`);
  };

  return {
    isExistAccount,
    downloadVisible,
    donwloadToggle,
    account,
    myDepositHistory: isGeneral ? myDepositHistory : corporateDepositHistory,
    handleWithdraw,
    isLoadingMyAccount: isLoading || isFetching || isLoadingCorporateDepositHistory,
    refetchMyAccount: refetch,
    goToAccountRegister,
    isVisibleAccountDialog,
    routerBack,
    ref,
    isLoadingMyDepositHistory: isLoadingMyDepositHistory || isLoadingCorporateDepositHistory,
    myDepositHistoryList: isGeneral ? myDepositHistoryList : corporateDepositHistoryList,
  };
};
