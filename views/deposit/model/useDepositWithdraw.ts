import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useVerification } from '@/features/auth/model/useVerification';
import { WithdrawFormData, withdrawFormSchema } from '@/features/deposit/lib/withdrawFormSchema';
import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { withdrawCorporateDeposit } from '@/entities/assets/api/withdrawCorporateDeposit';
import { withdrawUserDeposit } from '@/entities/assets/api/withdrawUserDeposit';
import { VerificationType } from '@/entities/verifications/types';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useDepositWithdraw = () => {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { successToast, errorToast } = useToast();
  const queryClient = useQueryClient();
  const pathname = usePathname();
  const { isVisible: isVerify, toggleVisibility: toggleVerify } = useVisibility();

  const { verifyIdentity } = useIdentityVerification();

  const {
    verifyCode,
    verifyContact,
    isEmailVerificationVisible,
    verificationTimerText,
    verificationTimer,
  } = useVerification(VerificationType.SMS);

  const form = useForm<WithdrawFormData>({
    resolver: zodResolver(withdrawFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      isMobileVerified: false,
      mobileVerifiedCode: '',
      mobileNumber: '',
      withdrawAmount: 0,
    },
  });

  const { isGeneral, userId } = useFetchUser();
  const { routerPush } = useWebViewRouter();
  const { account } = useFetchAccount();

  const { watch, setValue } = form;

  const handleWithdraw = async () => {
    setIsLoading(true);
    try {
      if (isGeneral) {
        await withdrawUserDeposit({
          userId: userId as string,
          depositAmount: form.getValues('withdrawAmount'),
        });
      } else {
        await withdrawCorporateDeposit({
          userId: userId as string,
          depositAmount: form.getValues('withdrawAmount'),
        });
      }

      successToast({
        title: '출금이 완료되었습니다.',
      });
      await queryClient.invalidateQueries({ queryKey: queries.assets._def });
      router.replace(callbackUrl ?? '/');
    } catch (error) {
      console.error(error);
      errorToast({
        title: '출금에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const verifyMobileCode = async () => {
    const data = await verifyCode(watch('mobileNumber'), watch('mobileVerifiedCode'));

    if (data.statusCode === 201) {
      setValue('isMobileVerified', true);
    }
  };

  const allWithdraw = (checked: boolean) => {
    if (checked) {
      form.setValue('withdrawAmount', account?.currentNetBalance || 0);
    } else {
      form.setValue('withdrawAmount', 0);
    }
  };

  const disabled = () => {
    return !(
      form.watch('withdrawAmount') > 0 &&
      form.watch('withdrawAmount') <= (account?.currentNetBalance || 0) &&
      form.watch('isMobileVerified')
    );
  };

  const handleIdentityVerification = async () => {
    toggleVerify();

    try {
      await verifyIdentity('verify-account-change');

      sessionStorage.setItem('isVerifiedForAccountChange', 'true');

      router.replace(`/deposit/account-change?callbackUrl=${pathname}`);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부

    if (transactionType === 'IDENTITY_VERIFICATION') {
      sessionStorage.setItem('isVerifiedForAccountChange', 'true');
      routerPush(`/deposit/account-change?callbackUrl=${pathname}`);
    }
  }, [searchParams]);

  const isNotWithdrawableAmount = watch('withdrawAmount') > (account?.currentNetBalance || 0);

  return {
    form,
    verifyMobileCode,
    isEmailVerificationVisible,
    verificationTimerText,
    verificationTimer,
    account,
    verifyContact,
    allWithdraw,
    disabled,
    handleWithdraw,
    isLoading,
    handleIdentityVerification,
    isVerify,
    toggleVerify,
    isNotWithdrawableAmount,
  };
};
