import { fetchCurations } from '@/features/curations/api/fetchCurations';
import { fetchEvents } from '@/features/events/api/fetchEvents';
import { fetchNews } from '@/features/news/api/fetchNews';
import { fetchNotices } from '@/features/notices/api/fetchNotices';

import { Curation } from '@/entities/curations/types';
import { Event } from '@/entities/events/types';
import { News } from '@/entities/news/types';
import { Notice } from '@/entities/notices/types';

import { ListResponse } from '@/shared/interface';

interface MobileContentsProps {
  curations: ListResponse<Curation>;
  news: ListResponse<News>;
  notices: ListResponse<Notice>;
  events: ListResponse<Event>;
}
export const useMobileContents = ({ curations, news, notices, events }: MobileContentsProps) => {
  const { data: curationsData } = fetchCurations(
    {
      page: 1,
      perPage: 6,
    },
    curations,
  );

  const { data: newsData } = fetchNews(
    {
      page: 1,
      perPage: 6,
    },
    news,
  );

  const { data: noticesData } = fetchNotices(
    {
      page: 1,
      perPage: 3,
    },
    notices,
  );

  const { data: eventsData } = fetchEvents(
    {
      page: 1,
      perPage: 6,
    },
    events,
  );

  return {
    curationsData,
    newsData,
    noticesData,
    eventsData,
  };
};
