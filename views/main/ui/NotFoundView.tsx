'use client';

import * as Sentry from '@sentry/nextjs';
import { logClientError } from '@artbloc/next-js-logger/client';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

import env from '@/shared/lib/env.schema';
import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

export const NotFoundView = ({
  error,
  reset,
}: {
  error?: Error & { digest?: string };
  reset?: () => void;
}) => {
  const router = useRouter();

  usePageGNB({ isGNBVisible: false });

  useEffect(() => {
    if (error) {
      logClientError('error', error);
      if (env.NEXT_PUBLIC_NODE_ENV === 'production') {
        Sentry.captureException(error);
      }
    }
  }, [error]);

  return (
    <div className="flex h-dvh items-center justify-center px-6 py-20 sm:h-auto sm:min-h-screen sm:px-0 sm:py-0">
      <div className="flex h-full max-w-[472px] flex-col justify-between sm:items-center sm:justify-start">
        <div className="flex h-full flex-col items-center justify-center">
          <FallbackImage
            src="/images/img_error.png"
            alt="404"
            width={130}
            height={142}
            style={{ objectFit: 'contain' }}
            className="mb-8 h-[98px] w-[91px] sm:h-[142px] sm:w-[130px]"
          />
          <div className="space-y-5 sm:mb-16">
            <h3 className="text-20 sm:text-32 text-center">페이지를 찾을 수 없습니다.</h3>
            <h5 className="text-center text-[15px] sm:text-lg">
              요청하신 페이지를 처리하는 도중 예기치 못한 에러가 발생했습니다. <br /> 이용에 불편을
              드려 죄송합니다. <br /> 잠시 후 다시 시도해 주시기 바랍니다.
            </h5>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:flex sm:justify-center sm:px-0">
          <PrimaryButton
            text="메인으로 돌아가기"
            onClick={() => {
              if (reset) {
                reset();
                router.replace('/');
              } else {
                router.replace('/');
              }
            }}
            className="h-12 w-full text-base sm:w-[200px]"
          />
        </div>
      </div>
    </div>
  );
};
