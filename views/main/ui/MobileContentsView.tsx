'use client';

import React from 'react';

import { MobileContentsNoticeSwiper } from '@/widgets/notices/ui/MobileContentsNoticeSwiper';

import { Curation } from '@/entities/curations/types';
import { MobileContentsCurationCard } from '@/entities/curations/ui/MobileContentsCurationCard';
import { Event } from '@/entities/events/types';
import { MobileContentsEventCard } from '@/entities/events/ui/MobileContentsEventCard';
import { News } from '@/entities/news/types';
import { MobileContentsNewsCard } from '@/entities/news/ui/MobileContentsNewsCard';
import { Notice } from '@/entities/notices/types';

import { ListResponse } from '@/shared/interface';
import { MobileContentsSwiper } from '@/shared/ui/MobileContentsSwiper';

import { useMobileContents } from '../model/useMobileContents';

interface MobileContentsViewProps {
  events: ListResponse<Event>;
  notices: ListResponse<Notice>;
  news: ListResponse<News>;
  curations: ListResponse<Curation>;
}

export const MobileContentsView = ({
  events,
  notices,
  news,
  curations,
}: MobileContentsViewProps) => {
  const { curationsData, newsData, noticesData, eventsData } = useMobileContents({
    curations,
    news,
    notices,
    events,
  });

  return (
    <section className="mx-auto max-w-screen-md space-y-[54px] px-6 pb-[100px] pt-10">
      <MobileContentsSwiper
        title="콘텐츠"
        href="/contents/curations"
        items={curationsData?.data || []}
        renderItem={MobileContentsCurationCard}
      />
      <MobileContentsNoticeSwiper noticesData={noticesData} />
      <MobileContentsSwiper
        title="언론자료"
        href="/contents/news"
        items={newsData?.data || []}
        renderItem={MobileContentsNewsCard}
      />
      <MobileContentsSwiper
        title="이벤트"
        href="/contents/events"
        items={eventsData?.data || []}
        renderItem={MobileContentsEventCard}
      />
    </section>
  );
};
