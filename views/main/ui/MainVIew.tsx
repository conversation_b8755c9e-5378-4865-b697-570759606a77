'use client';

import { CompanyIntro } from '@/widgets/main/ui/CompanyIntro';
import { ContentsIntro } from '@/widgets/main/ui/ContentsIntro';
import { FloatingActions } from '@/widgets/main/ui/FloatingActions';
import { HeroBanner } from '@/widgets/main/ui/HeroBanner';
import { MobileSubscriptionPromo } from '@/widgets/main/ui/MobileSubscriptionPromo';
import { SlideContents } from '@/widgets/main/ui/SlideContents';
import { MainSubscriptions } from '@/widgets/subscriptions/ui/MainSubscriptions';

import { Banner } from '@/entities/banners/types';
import { Curation } from '@/entities/curations/types';
import { SubscriptionsResponse } from '@/entities/subscriptions/interface';

import { ListResponse } from '@/shared/interface';

interface MainViewProps {
  banners: ListResponse<Banner>;
  curations: ListResponse<Curation>;
  subscriptions: SubscriptionsResponse;
}

export const MainView = ({ banners, curations, subscriptions }: MainViewProps) => {
  return (
    <div className="mt-2 sm:mt-0">
      <HeroBanner banners={banners} />
      <MainSubscriptions subscriptions={subscriptions} />
      <ContentsIntro curations={curations} />
      <CompanyIntro />
      <SlideContents />
      <MobileSubscriptionPromo />
      {/* <FloatingActions /> */}
    </div>
  );
};
