import { useEffect } from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { checkPasswordExist } from '@/entities/users/api/checkPasswordExist';

import { useVisibility } from '@/shared/model/useVisibility';

export const useProfileView = () => {
  const { isVisible: isPasswordNotExist, toggleVisibility: togglePasswordNotExist } =
    useVisibility(false);
  const { isGeneral } = useFetchUser();

  useEffect(() => {
    checkPasswordExist().then((res) => {
      if (!res.data) {
        togglePasswordNotExist();
      }
    });
  }, []);

  return {
    isPasswordNotExist,
    togglePasswordNotExist,
    isGeneral,
  };
};
