import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { DIALOG_CONFIGS } from '../config';

export const useMySetting = () => {
  const pathname = usePathname();
  const { routerPush, routerBack } = useWebViewRouter();
  const { isVisible: isVerify, toggleVisibility: toggleVerify } = useVisibility();
  const { isVisible: isApplyInvestorType, toggleVisibility: toggleApplyInvestorType } =
    useVisibility();
  const searchParams = useSearchParams();
  const { isVisible: isOcr, toggleVisibility: toggleOcr } = useVisibility();
  const { user, isExistAccount, isCorporate } = useFetchUser();
  const { account } = useFetchAccount();
  const { verifyIdentity } = useIdentityVerification();

  // 현재 활성화된 다이얼로그 ID 상태
  const [activeDialogId, setActiveDialogId] = useState<string | null>(null);

  // 다이얼로그 열기 함수
  const openDialog = (dialogId: string) => {
    setActiveDialogId(dialogId);
  };

  // 다이얼로그 닫기 함수
  const closeDialog = () => {
    setActiveDialogId(null);
  };

  // 다이얼로그 설정 가져오기
  const getDialogConfig = (dialogId: string) => {
    return DIALOG_CONFIGS.find((config) => config.id === dialogId);
  };

  // 현재 활성화된 다이얼로그 설정
  const activeDialog = activeDialogId ? getDialogConfig(activeDialogId) : null;

  const handleIdentityVerification = async () => {
    toggleVerify();

    try {
      await verifyIdentity('verify-account-change');

      sessionStorage.setItem('isVerifiedForAccountChange', 'true');

      routerPush(`/deposit/account-change?callbackUrl=${pathname}`);
    } catch (error) {
      console.error(error);
    }
  };

  const handleRouter = (href: string) => {
    routerPush(`${href}?callbackUrl=${pathname}`);
    closeDialog();
  };

  useEffect(() => {
    if (!user) return;

    if (!user?.userAuthOCR?.id) {
      toggleOcr();
    }
  }, [user?.userAuthOCR]);

  const handleOcr = () => {
    routerPush(`/ocr?callbackUrl=${pathname}`);
  };

  useEffect(() => {
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부

    if (transactionType === 'IDENTITY_VERIFICATION') {
      sessionStorage.setItem('isVerifiedForAccountChange', 'true');
      routerPush(`/deposit/account-change?callbackUrl=${pathname}`);
    }
  }, [searchParams]);

  return {
    isApplyInvestorType,
    toggleApplyInvestorType,
    activeDialog,
    openDialog,
    closeDialog,
    handleRouter,
    isOcr,
    toggleOcr,
    routerBack,
    handleOcr,
    user,
    isExistAccount,
    account,
    isVerify,
    toggleVerify,
    handleIdentityVerification,
    isCorporate,
  };
};
