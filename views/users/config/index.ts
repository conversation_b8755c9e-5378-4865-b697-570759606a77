// 다이얼로그 설정 타입 정의
interface DialogConfig {
  id: string;
  title: string;
  description: string;
  path: string;
}

// 다이얼로그 설정 객체
export const DIALOG_CONFIGS: DialogConfig[] = [
  {
    id: 'suitability',
    title: '투자 적합성 테스트',
    description: '투자 적합성 테스트를\n 진행하시겠습니까?',
    path: '/investor/suitability',
  },
  {
    id: 'propensity',
    title: '투자 성향 진단',
    description: '투자 성향 진단을 \n 진행하시겠습니까?',
    path: '/investor/propensity',
  },
  {
    id: 'deposit-register',
    title: '출금 계좌 등록',
    description: `출금 계좌 등록을 위해 예치금 가상계좌 개설이 필요합니다. \n 개설하시겠습니까?`,
    path: '/deposit/account-register',
  },
  {
    id: 'deposit-change',
    title: '출금계좌 변경',
    description: '출금계좌 변경을 위해 휴대폰 본인인증이 필요합니다. \n 인증하시겠습니까?',
    path: '/deposit/account-change',
  },
];
