'use client';

import { useFetchUser } from '@/features/users/model/useFetchUser';
import { MobileCorporateAddressForm } from '@/features/users/ui/MobileCorporateAddressForm';
import { MobileUserAddressForm } from '@/features/users/ui/MobileUserAddressForm';

export const MobileProfileAddressView = () => {
  const { isGeneral } = useFetchUser();

  return (
    <section className="p-6">
      {isGeneral ? <MobileUserAddressForm /> : <MobileCorporateAddressForm />}
    </section>
  );
};
