'use client';

import { formatAccountNumber } from '@/features/deposit/lib/formatAccountNumber';
import { getBankName } from '@/features/deposit/lib/getBankName';
import { ApplyInvestorTypeForm } from '@/features/investor/ui/ApplyInvestorTypeForm';

import { InvestorQualificationStatus } from '@/entities/investor/interface';
import {
  InvestorQualificationType,
  InvestorQualificationTypeValues,
} from '@/entities/investor/types';
import { KycStatus } from '@/entities/users/types';
import { UserSettingSuccess } from '@/entities/users/ui/UserSettingSuccess';

import { utilFormats } from '@/shared/lib/utilformats';
import { CommonTooltip } from '@/shared/ui/CommonTooltip';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Separator } from '@/shared/ui/shadcn/separator';

import { useMySetting } from '../model/useMySetting';

export const MySettingView = () => {
  const { YYYYMMDD } = utilFormats();

  const {
    isApplyInvestorType,
    toggleApplyInvestorType,
    activeDialog,
    openDialog,
    closeDialog,
    handleRouter,
    isOcr,
    handleOcr,
    routerBack,
    user,
    isExistAccount,
    account,
    isVerify,
    handleIdentityVerification,
    toggleVerify,
    isCorporate,
  } = useMySetting();

  const isQualificationApplying =
    user?.investorQualification?.applying?.qualificationStatus ===
    InvestorQualificationStatus.PENDING;

  const isProfessional =
    user?.investorQualification?.current?.qualificationType ===
    InvestorQualificationType.PROFESSIONAL;

  return (
    <section className="flex-1 space-y-12 px-6 sm:px-8 ml:px-0">
      <ContentsTitle title="투자 정보 설정" className="text-28 mb-10" />
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-3">
          <p className="font-semibold">이름</p>
          <div className="flex items-center gap-3">
            <p className="text-lg">{user?.userProfile?.name}</p>
            {user?.kycStatus === KycStatus.KYC && (
              <span className="rounded bg-green-50 px-2 py-[3px] text-xs font-semibold text-green-500">
                본인 실명인증 완료
              </span>
            )}
          </div>
        </div>
        <CommonTooltip
          title="본인 실명인증 정보가 변경되셨나요?"
          description="본인 실명인증 정보가 변경된 경우"
        />
      </div>
      <Separator orientation="horizontal" className="my-10 bg-gray-300" />
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-3">
          <p className="font-semibold">투자자 유형</p>
          <p className="text-lg">
            {user?.investorQualification?.current?.qualificationType
              ? InvestorQualificationTypeValues[
                  user?.investorQualification?.current
                    ?.qualificationType as InvestorQualificationType
                ]
              : '투자자 유형 변경'}
          </p>
        </div>
        {!isProfessional && (
          <SecondaryButton
            text="유형 변경 신청하기"
            disabled={isProfessional}
            className="w-40 border-gray-300"
            onClick={toggleApplyInvestorType}
          />
        )}
      </div>
      {isApplyInvestorType ? (
        <ApplyInvestorTypeForm isQualificationApplying={isQualificationApplying} />
      ) : (
        <Separator orientation="horizontal" className="my-10 bg-gray-300" />
      )}
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-3">
          <p className="font-semibold">출금 계좌</p>
          {isExistAccount ? (
            <p className="text-lg">
              {getBankName(account?.masterAccountInstCode)}{' '}
              {formatAccountNumber(account?.masterAccountNumberCode || '')}
            </p>
          ) : (
            <p className="text-lg text-gray-400">등록된 출금 계좌가 없습니다.</p>
          )}
        </div>

        <SecondaryButton
          onClick={() => {
            if (isExistAccount) {
              toggleVerify();
            } else {
              openDialog('deposit-register');
            }
          }}
          text={isExistAccount ? '변경' : '등록'}
          className="w-[84px] border-gray-300"
        />
      </div>
      <Separator orientation="horizontal" className="my-10 bg-gray-300" />
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-3">
          <p className="font-semibold">투자 적합성 테스트</p>
          {user?.investorSuitabilityTest && user?.investorSuitabilityTest ? (
            <UserSettingSuccess date={YYYYMMDD(user?.investorSuitabilityTest?.createdAt) || ''} />
          ) : (
            <p className="text-lg text-gray-400">투자 적합성 테스트를 진행해 주세요.</p>
          )}
        </div>
        {!user?.investorSuitabilityTest && (
          <SecondaryButton
            onClick={() => openDialog('suitability')}
            text="테스트 진행하기"
            className="w-[140px] border-gray-300"
          />
        )}
      </div>
      {/* <Separator orientation="horizontal" className="my-10 bg-gray-300" />
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-3">
          <p className="font-semibold">투자 성향 진단</p>
          {user?.investorPropensity && user?.investorPropensity.length > 0 ? (
            <UserSettingSuccess date={YYYYMMDD(user?.investorPropensity[0].createdAt) || ''} />
          ) : (
            <p className="text-lg text-gray-400">투자 성향 진단을 진행해주세요.</p>
          )}
        </div>
        <SecondaryButton
          onClick={() => openDialog('propensity')}
          text="진단하기"
          className="w-[96px] border-gray-300"
        />
      </div> */}
      {/* Todo: 실명 확인 서류 */}
      {isCorporate && (
        <>
          <Separator orientation="horizontal" className="my-10 bg-gray-300" />
          <div className="flex items-center justify-between">
            <div className="flex flex-col gap-3">
              <p className="font-semibold">실명 확인 서류</p>
              {user?.userCorporateProfile?.updatedAt && (
                <UserSettingSuccess date={YYYYMMDD(user?.userCorporateProfile?.updatedAt) || ''} />
              )}
            </div>
            <CommonTooltip
              title="사업자정보 변경"
              side="top"
              description="법인 출금 계좌 및 정보 변경이 필요한 경우"
            />
          </div>
        </>
      )}
      {activeDialog && (
        <ConfirmDialog
          isOpen={!!activeDialog}
          handleOpen={closeDialog}
          title={activeDialog.title}
          description={activeDialog.description}
          handleAction={() => handleRouter(activeDialog.path)}
        />
      )}
      {isOcr && (
        <ConfirmDialog
          isOpen={isOcr}
          handleOpen={() => routerBack()}
          title="본인 실명인증 안내"
          description={`투자 서비스 이용을 위해 본인 실명인증이 필요합니다. \n 진행하시겠습니까?`}
          handleAction={handleOcr}
        />
      )}
      {isVerify && (
        <ConfirmDialog
          isOpen={isVerify}
          handleOpen={toggleVerify}
          title="출금 계좌 변경"
          isCancelButton
          text="확인"
          description={`출금 계좌 변경을 위해 휴대폰 본인인증이 필요합니다. \n 인증하시겠습니까?`}
          handleAction={handleIdentityVerification}
        />
      )}
    </section>
  );
};
