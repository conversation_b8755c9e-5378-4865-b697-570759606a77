'use client';

import { ChevronRightIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { formatBusinessRegistrationNumber } from '@/features/users/lib/formatBusinessRegistrationNumber';
import { formatCorporateRegistrationNumber } from '@/features/users/lib/formatCorporateRegistrationNumber';
import { useFetchUser } from '@/features/users/model/useFetchUser';
import { MobileProfileItem } from '@/features/users/ui/MobileProfileItem';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const MobileUserProfileView = () => {
  const { user, isGeneral } = useFetchUser();

  const { routerPush } = useWebViewRouter();

  return (
    <section className="flex h-mobile-non-gnb flex-col justify-between p-6">
      {isGeneral ? (
        <div>
          <MobileProfileItem label="이름" value={user?.userProfile?.name} showInfoIcon />
          <MobileProfileItem label="이메일 주소" value={user?.userProfile?.email} />
          <MobileProfileItem
            label="휴대폰 번호"
            value={user?.userProfile?.mobileNumber}
            showChevron
            onClick={() => {
              /* 핸들러 추가 */
            }}
          />
          <MobileProfileItem
            label="비밀번호 변경"
            showChevron
            onClick={() => {
              routerPush('/mobile/user/my/profile/password');
            }}
          />
          <MobileProfileItem
            label="주소"
            value={
              user?.userProfile?.address1 &&
              `${user?.userProfile?.address1} ${user?.userProfile?.address2}`
            }
            placeholder="주소 입력"
            showChevron
            onClick={() => {
              routerPush('/mobile/user/my/profile/address');
            }}
          />
        </div>
      ) : (
        <div>
          <MobileProfileItem
            label="법인명"
            value={user?.userCorporateProfile?.companyName}
            showInfoIcon
          />
          <MobileProfileItem
            label="법인등록번호"
            value={formatCorporateRegistrationNumber(user?.userCorporateProfile?.brn)}
            showInfoIcon
          />
          <MobileProfileItem
            label="사업자등록번호"
            value={formatBusinessRegistrationNumber(user?.userCorporateProfile?.brc?.[0])}
            showInfoIcon
          />
          <MobileProfileItem label="이메일 주소" value={user?.userCorporateProfile?.managerEmail} />
          <MobileProfileItem
            label="휴대폰 번호"
            value={user?.userCorporateProfile?.managerMobileNumber}
            showChevron
            onClick={() => {
              /* 핸들러 추가 */
            }}
          />
          <MobileProfileItem
            label="비밀번호 변경"
            showChevron
            onClick={() => {
              routerPush('/mobile/user/my/profile/password');
            }}
          />
          <MobileProfileItem
            label="주소"
            placeholder={
              user?.userCorporateProfile?.address1
                ? `${user?.userCorporateProfile?.address1} ${user?.userCorporateProfile?.address2}`
                : '주소 입력'
            }
            showChevron
            onClick={() => {
              routerPush('/mobile/user/my/profile/address');
            }}
          />
        </div>
      )}
      <div className="flex justify-center">
        <button
          onClick={() => routerPush('/mobile/user/my/withdrawal')}
          className="flex items-center text-xs font-semibold leading-[150%] text-gray-500 underline"
        >
          회원 탈퇴 <ChevronRightIcon className="h-4 w-4" />{' '}
        </button>
      </div>
    </section>
  );
};
