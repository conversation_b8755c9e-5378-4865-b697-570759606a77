'use client';

import { Separator } from '@radix-ui/react-separator';
import { usePathname } from 'next/navigation';
import React from 'react';

import { MobileUserHeader } from '@/widgets/users/ui/MobileUserHeader';
import { MobileUserNavContainer } from '@/widgets/users/ui/MobileUserNavContainer';

import { GoToDepositButton } from '@/features/deposit/ui/GoToDepositButton';
import { MobileUserAccount } from '@/features/deposit/ui/MobileUserAccount';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Button } from '@/shared/ui/shadcn/button';

export const MobileUserView = () => {
  const { user, isExistAccount } = useFetchUser();
  const pathname = usePathname();
  const { routerPush } = useWebViewRouter();

  usePageGNB({ isGNBVisible: true });

  return (
    <main className="mx-auto w-full max-w-screen-md pb-20">
      <MobileUserHeader />
      {user && isExistAccount ? (
        <div className="my-3">
          <MobileUserAccount className="px-6" />
          <div className="px-6">
            <GoToDepositButton className="mt-3" />
          </div>
          <Separator orientation="horizontal" className="mb-5 mt-9 h-3 bg-blue-gray-00" />
        </div>
      ) : (
        user &&
        !isExistAccount && (
          <div className="my-3">
            <div className="px-6">
              <div className="flex flex-col items-center gap-6 rounded-lg border border-gray-300 px-5 pb-5 pt-8">
                <div className="flex flex-col items-center">
                  <FallbackImage
                    src="/images/account.png"
                    alt="account"
                    width={60}
                    height={60}
                    className="mb-4"
                  />
                  <p className="mb-1 text-lg font-bold">나의 예치금 가상계좌 발급하기</p>
                  <p>예치금 가상계좌를 발급하세요.</p>
                </div>
                <Button
                  onClick={() => routerPush(`/deposit/account-register?callbackUrl=${pathname}`)}
                  className="h-12 w-full bg-primary-500 text-base font-semibold text-white"
                >
                  계좌 발급하기
                </Button>
              </div>
            </div>
            <Separator orientation="horizontal" className="mb-5 mt-9 h-3 bg-blue-gray-00" />
          </div>
        )
      )}
      <MobileUserNavContainer
        title="고객센터"
        items={[
          { title: 'FAQ', href: '/supports/faqs' },
          { title: '문의하기', href: '/mobile/user/support/inquiry-history', auth: true },
          { title: '투자 의뢰 신청', href: '/subscriptions/request' },
        ]}
        className={`${!user && 'mt-7'}`}
        isLast={false}
      />
      <Separator orientation="horizontal" className="mx-5 my-3 h-[1px] bg-gray-100" />
      <MobileUserNavContainer
        title="앱 설정"
        items={[
          { title: '알림 설정', href: '/mobile/user/my/notification', auth: true },
          { title: '앱버전', value: 'V 1.0.0' },
          { title: '약관, 정책 및 알림 동의', href: '/terms/behavior-collection' },
        ]}
        isLast
      />
    </main>
  );
};
