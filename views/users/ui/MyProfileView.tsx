'use client';

import { PasswordChangeForm } from '@/features/users/ui/PasswordChangeForm';
import { PasswordCreateForm } from '@/features/users/ui/PasswordCreateForm';
import { ProfileCorporateForm } from '@/features/users/ui/ProfileCorporateForm';
import { ProfileForm } from '@/features/users/ui/ProfileForm';

import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

import { useProfileView } from '../model/useProfileView';

export const MyProfileView = () => {
  const { isPasswordNotExist, togglePasswordNotExist, isGeneral } = useProfileView();

  return (
    <section className="flex-1 px-6 sm:px-8 ml:px-0">
      <ContentsTitle title="기본 정보 설정" className="text-28 mb-10 hidden md:block" />
      <Tabs defaultValue="profile">
        <TabsList className="mb-12 hidden w-full grid-cols-2 text-gray-300 ml:grid">
          <TabsTrigger
            value="profile"
            className="h-[50px] rounded-none border-b border-gray-300 text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
          >
            기본 정보
          </TabsTrigger>
          <TabsTrigger
            value="password"
            className="h-[50px] rounded-none border-b border-gray-300 text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
          >
            {isPasswordNotExist ? '비밀번호 등록' : '비밀번호 변경'}
          </TabsTrigger>
        </TabsList>
        <TabsList className="mb-10 grid h-12 w-full grid-cols-2 rounded-lg border border-gray-300 ml:hidden">
          <TabsTrigger
            value="profile"
            className="h-full rounded-r-none text-sm font-semibold data-[state=active]:bg-gray-900 data-[state=active]:text-white"
          >
            기본 정보
          </TabsTrigger>
          <TabsTrigger
            value="password"
            className="h-full rounded-l-none text-sm font-semibold data-[state=active]:bg-gray-900 data-[state=active]:text-white"
          >
            {isPasswordNotExist ? '비밀번호 등록' : '비밀번호 변경'}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          {isGeneral ? <ProfileForm /> : <ProfileCorporateForm />}
        </TabsContent>
        <TabsContent value="password">
          {isPasswordNotExist ? (
            <PasswordCreateForm handlePasswordNotExist={togglePasswordNotExist} />
          ) : (
            <PasswordChangeForm />
          )}
        </TabsContent>
      </Tabs>
    </section>
  );
};
