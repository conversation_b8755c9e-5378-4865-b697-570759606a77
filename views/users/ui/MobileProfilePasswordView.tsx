'use client';

import React from 'react';

import { MobilePasswordChangeForm } from '@/features/users/ui/MobilePasswordChangeForm';
import { MobilePasswordCreateForm } from '@/features/users/ui/MobilePasswordCreateForm';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';

import { useProfileView } from '../model/useProfileView';

export const MobileProfilePasswordView = () => {
  const { isPasswordNotExist } = useProfileView();

  usePageHeader({
    mode: HeaderMode.DETAIL,
    subTitle: isPasswordNotExist ? '비밀번호 등록' : '비밀번호 변경',
  });
  usePageGNB({ isGNBVisible: false });

  return (
    <section className="p-6">
      {isPasswordNotExist ? <MobilePasswordCreateForm /> : <MobilePasswordChangeForm />}
    </section>
  );
};
