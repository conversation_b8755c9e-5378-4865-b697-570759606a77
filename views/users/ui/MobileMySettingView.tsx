'use client';

import { usePathname } from 'next/navigation';

import { formatAccountNumber } from '@/features/deposit/lib/formatAccountNumber';
import { getBankName } from '@/features/deposit/lib/getBankName';
import { MobileProfileItem } from '@/features/users/ui/MobileProfileItem';

import {
  InvestorQualificationType,
  InvestorQualificationTypeValues,
} from '@/entities/investor/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';

import { useMySetting } from '../model/useMySetting';

export const MobileMySettingView = () => {
  const {
    isOcr,
    handleOcr,
    routerBack,
    user,
    account,
    isVerify,
    handleIdentityVerification,
    toggleVerify,
  } = useMySetting();

  const { routerPush } = useWebViewRouter();
  const pathname = usePathname();

  const isProfessional =
    user?.investorQualification?.current?.qualificationType ===
    InvestorQualificationType.PROFESSIONAL;

  const { YYYYMMDD } = utilFormats();

  return (
    <section className="flex h-mobile-non-gnb flex-col justify-between p-6">
      <div>
        <MobileProfileItem
          label="본인 실명인증"
          value={user?.userAuthOCR ? '인증완료' : '인증하기'}
          showChevron
          onClick={() => routerPush(`/ocr?callbackUrl=${pathname}`)}
          showInfoIcon
        />
        <MobileProfileItem
          label="투자자 유형"
          value={
            user?.investorQualification?.current?.qualificationType
              ? InvestorQualificationTypeValues[
                  user?.investorQualification?.current
                    ?.qualificationType as InvestorQualificationType
                ]
              : '투자자 유형 변경'
          }
          disabled={isProfessional}
          showChevron={!isProfessional}
          onClick={() => {
            if (isProfessional) {
              return;
            }
            routerPush(`/mobile/user/apply-investor?callbackUrl=${pathname}`);
          }}
        />
        <MobileProfileItem
          label="출금 계좌"
          value={`${account ? ` ${getBankName(account.masterAccountInstCode)} ${formatAccountNumber(account.masterAccountNumberCode || '')}` : '등록'}`}
          showChevron
          onClick={() => {
            if (account) {
              toggleVerify();
            } else {
              routerPush(`/deposit/account-register?callbackUrl=${pathname}`);
            }
          }}
        />
        <MobileProfileItem
          label="투자 적합성 테스트"
          value={
            user?.investorSuitabilityTest &&
            `완료 (${YYYYMMDD(user?.investorSuitabilityTest?.consentAt)})`
          }
          placeholder="테스트 진행하기"
          showChevron={!user?.investorSuitabilityTest}
          disabled={!!user?.investorSuitabilityTest}
          onClick={() => {
            if (user?.investorSuitabilityTest) {
              return;
            }
            routerPush(`/investor/suitability?callbackUrl=${pathname}`);
          }}
        />
        {/* <MobileProfileItem
          label="투자 성향 진단"
          placeholder={
            user?.investorPropensity && user?.investorPropensity?.length > 0
              ? `${PropensityLevelEnumValues[user?.investorPropensity[0].grade]} (${YYYYMMDD(user?.investorPropensity[0].consentAt)})`
              : '진단하기'
          }
          showChevron
          onClick={() => {
            routerPush(`/investor/propensity?callbackUrl=${pathname}`);
          }}
        /> */}
      </div>
      {isOcr && (
        <ConfirmDialog
          isOpen={isOcr}
          handleOpen={() => routerBack()}
          title="본인 실명인증 안내"
          description={`투자 서비스 이용을 위해 본인 실명인증이 필요합니다. \n 진행하시겠습니까?`}
          handleAction={handleOcr}
        />
      )}
      {isVerify && (
        <ConfirmDialog
          isOpen={isVerify}
          handleOpen={toggleVerify}
          title="출금 계좌 변경"
          isCancelButton
          text="확인"
          description={`출금 계좌 변경을 위해 휴대폰 본인인증이 필요합니다. \n 인증하시겠습니까?`}
          handleAction={handleIdentityVerification}
        />
      )}
    </section>
  );
};
