'use client';

import Cookies from 'js-cookie';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { appleUserCheck } from '@/entities/auth/api/appleUserCheck';
import { kakaoUserCheck } from '@/entities/auth/api/kakaoUserCheck';
import { naverUserCheck } from '@/entities/auth/api/naverUserCheck';
import { SignState } from '@/entities/auth/types';

import env from '@/shared/lib/env.schema';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

export const useAuthCallback = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const callbackUrl = Cookies.get('callback-url') || '/';
  const { errorToast } = useToast();
  const { isVisible: isAlertVisible, toggleVisibility: toggleAlertVisibility } = useVisibility();
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const hashParams = new URLSearchParams(window.location.hash.substring(1));
    const state = searchParams.get('state') || hashParams.get('state');
    const code = searchParams.get('code');
    const error = searchParams.get('error');
    const device = searchParams.get('device');
    const accessToken = searchParams.get('access_token') || hashParams.get('access_token');

    if (error) {
      router.replace('/sign-in');
    }

    // 네이버, 카카오 로그인
    if (state === SignState.NAVER_SIGNIN) {
      if (device === 'app') {
        fetch('/api/naver', {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        })
          .then((res) => res.json())
          .then(async ({ response }) => {
            const { data } = await naverUserCheck({
              socialId: response.id,
              mobileNumber: response.mobile,
            });

            if (data.isUser) {
              handleSocialSignIn({
                socialId: response.id,
                mobileNumber: response.mobile,
                type: 'naver',
              });
            } else {
              toggleAlertVisibility();
              setAlertMessage('회원가입 후 로그인을 진행해 주세요.');
            }
          });
      } else {
        const NaverIdLogin = (window as any).naver;
        const naverLogin = new NaverIdLogin.LoginWithNaverId({
          clientId: env.NEXT_PUBLIC_NAVER_CLIENT_ID,
          callbackUrl: env.NEXT_PUBLIC_REDIRECT_URL,
          isPopup: false,
        });

        checkNaverLogin(naverLogin);
      }
    } else if (state === SignState.KAKAO_SIGNIN) {
      if (device === 'app' && accessToken) {
        fetch(`https://kapi.kakao.com/v2/user/me`, {
          method: 'GET',
          headers: { Authorization: `Bearer ${accessToken}` },
        })
          .then((res) => res.json())
          .then(async (userData) => {
            const { data } = await kakaoUserCheck({
              socialId: userData.id,
              mobileNumber: userData.kakao_account.phone_number,
            });
            if (data.isUser) {
              handleSocialSignIn({
                socialId: userData.id,
                mobileNumber: userData.kakao_account.phone_number,
                type: 'kakao',
              });
            } else {
              toggleAlertVisibility();
              setAlertMessage('회원가입 후 로그인을 진행해 주세요.');
            }
          });
      }
      if (code) {
        checkKakaoLogin(code);
      }
    } else if (state === SignState.NAVER_SIGNUP) {
      fetch('/api/naver', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })
        .then((res) => res.json())
        .then(async ({ response }) => {
          const { data } = await naverUserCheck({
            socialId: response.id,
            mobileNumber: response.mobile,
          });

          if (data.isUser) {
            toggleAlertVisibility();
            setAlertMessage('이미 가입된 회원입니다. 로그인을 진행해 주세요.');
          } else {
            router.replace(
              `/sign-up/social?type=naver&access_token=${encodeURIComponent(accessToken || '')}`,
            );
          }
        });
    } else if (state === SignState.KAKAO_SIGNUP) {
      if (device === 'app' && accessToken) {
        fetch(`https://kapi.kakao.com/v2/user/me`, {
          method: 'GET',
          headers: { Authorization: `Bearer ${accessToken}` },
        })
          .then((res) => res.json())
          .then(async (userData) => {
            const { data } = await kakaoUserCheck({
              socialId: userData.id,
              mobileNumber: userData.kakao_account.phone_number,
            });
            if (data.isUser) {
              toggleAlertVisibility();
              setAlertMessage('이미 가입된 회원입니다. 로그인을 진행해 주세요.');
            } else {
              router.replace(`/sign-up/social?type=kakao&id=${accessToken}`);
            }
          });
      }
      if (code) {
        fetch(
          `https://kauth.kakao.com/oauth/token?grant_type=authorization_code&client_id=${env.NEXT_PUBLIC_KAKAO_API_KEY}&redirect_uri=${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback&code=${code}`,
          {
            method: 'POST',
          },
        )
          .then((res) => res.json())
          .then(async (res) => {
            const response = await fetch(`https://kapi.kakao.com/v2/user/me`, {
              method: 'GET',
              headers: { Authorization: `Bearer ${res.access_token}` },
            });

            const userData = await response.json();

            const { data } = await kakaoUserCheck({
              socialId: userData.id,
              mobileNumber: userData.kakao_account.phone_number,
            });

            if (data.isUser) {
              toggleAlertVisibility();
              setAlertMessage('이미 가입된 회원입니다. 로그인을 진행해 주세요.');
            } else {
              router.replace(`/sign-up/social?type=kakao&id=${res.access_token}`);
            }
          });
      }
    } else if (state === SignState.APPLE_SIGNIN) {
      fetch('/api/apple', {
        method: 'POST',
        body: JSON.stringify({ code, type: device === 'app' ? 'app' : undefined }),
      })
        .then((res) => res.json())
        .then(async ({ response }) => {
          const { data } = await appleUserCheck({
            socialId: response.sub,
            email: response.email,
          });
          if (data.isUser) {
            handleSocialSignIn({
              socialId: response.sub,
              email: response.email,
              type: 'apple',
            });
          } else {
            toggleAlertVisibility();
            setAlertMessage('회원가입 후 로그인을 진행해 주세요.');
          }
        });
    } else if (state === SignState.APPLE_SIGNUP) {
      fetch('/api/apple', {
        method: 'POST',
        body: JSON.stringify({ code, type: device === 'app' ? 'app' : undefined }),
      })
        .then((res) => res.json())
        .then(async ({ response }) => {
          const { data } = await appleUserCheck({
            socialId: response.sub,
            email: response.email,
          });

          if (data.isUser) {
            toggleAlertVisibility();
            setAlertMessage('이미 가입된 회원입니다. 로그인을 진행해 주세요.');
          } else {
            router.replace(
              `/sign-up/social?type=apple&socialId=${response.sub}&email=${response.email}`,
            );
          }
        });
    }
  }, [searchParams]);

  const handleSocialSignIn = async ({
    socialId,
    mobileNumber,
    type,
    email,
  }: {
    socialId: string;
    mobileNumber?: string;
    email?: string;
    type: 'kakao' | 'naver' | 'apple';
  }) => {
    if (type === 'apple') {
      const result = await signIn('sns-signin', {
        socialId,
        email,
        type,
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error || '로그인에 실패했습니다.');
      }
    } else {
      const result = await signIn('sns-signin', {
        socialId,
        mobileNumber,
        type,
        redirect: false,
      });
      if (result?.error) {
        throw new Error(result.error || '로그인에 실패했습니다.');
      }
    }

    Cookies.remove('callback-url');
    router.replace(callbackUrl);
  };

  const checkKakaoLogin = async (code: string) => {
    const Kakao = (window as any).Kakao;

    if (Kakao && !Kakao.isInitialized()) {
      Kakao.init(env.NEXT_PUBLIC_KAKAO_API_KEY);
    }

    try {
      const res = await fetch(
        `https://kauth.kakao.com/oauth/token?grant_type=authorization_code&client_id=${env.NEXT_PUBLIC_KAKAO_API_KEY}&redirect_uri=${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback&code=${code}`,
        {
          method: 'POST',
        },
      );
      const result = await res.json();

      const response = await fetch(`https://kapi.kakao.com/v2/user/me`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${result.access_token}` },
      });

      const userData = await response.json();

      const { data } = await kakaoUserCheck({
        socialId: userData.id,
        mobileNumber: userData.kakao_account.phone_number,
      });

      if (data.isUser) {
        handleSocialSignIn({
          socialId: userData.id,
          mobileNumber: userData.kakao_account.phone_number,
          type: 'kakao',
        });
      } else {
        setAlertMessage('회원가입 후 로그인을 진행해 주세요.');
        toggleAlertVisibility();
      }
    } catch (e: any) {
      console.error('kakao login error', e);
      errorToast({
        title: '로그인에 실패했습니다.',
        description: '아이디 또는 비밀번호를 확인해 주세요.',
      });
    }
  };

  const checkNaverLogin = (naverLogin: any) => {
    naverLogin.init();

    naverLogin.getLoginStatus(async function (status: boolean) {
      if (status) {
        try {
          const res = await fetch('/api/naver', {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${naverLogin.loginStatus.accessToken.accessToken}`,
            },
          });
          const { response } = await res.json();

          const { data } = await naverUserCheck({
            socialId: response.id,
            mobileNumber: response.mobile,
          });

          if (data.isUser) {
            handleSocialSignIn({
              socialId: response.id,
              mobileNumber: response.mobile,
              type: 'naver',
            });
          } else {
            setAlertMessage('회원가입 후 로그인을 진행해 주세요.');
            toggleAlertVisibility();
          }
        } catch (e: any) {
          console.error('Naver login error', e);
        }
      }
    });
  };

  const handleAlertAction = () => {
    router.replace('/sign-up');
  };

  return {
    isAlertVisible,
    toggleAlertVisibility,
    alertMessage,
    handleAlertAction,
  };
};
