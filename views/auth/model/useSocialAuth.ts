import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { isAdult } from '@/features/auth/lib/isAdult';
import { useSocialSignUp } from '@/features/auth/model/useSocialSignUp';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';

import { SocialSignUpSteps } from '@/entities/auth/types';

import env from '@/shared/lib/env.schema';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useSocialAuth = () => {
  const searchParams = useSearchParams();
  const type = searchParams.get('type') as 'naver' | 'kakao' | 'apple';
  const accessToken = searchParams.get('access_token');
  const id = searchParams.get('id');
  const { verifyIdentity, isLoading: isIdentityLoading } = useIdentityVerification();
  const { isVisible, toggleVisibility } = useVisibility();
  const { routerPush } = useWebViewRouter();

  const [isVerifyLoading, setIsVerifyLoading] = useState(false);
  const { form, step, onSubmit, isLoading, handleStep, onClick, isStep } = useSocialSignUp();
  const identityVerificationId = searchParams.get('identityVerificationId'); // 포트원 인증 고유번호
  const identityVerificationTxId = searchParams.get('identityVerificationTxId'); // 요청 시 설정한 유니크 ID
  const transactionType = searchParams.get('transactionType'); // 인증 성공 여부
  const {
    isVisible: isMinorRestrictionDialogVisible,
    toggleVisibility: toggleMinorRestrictionDialogVisibility,
  } = useVisibility();

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const hashParams = new URLSearchParams(window.location.hash.substring(1));
    const accessToken = searchParams.get('access_token') || hashParams.get('access_token');

    if (type === 'naver' && accessToken) {
      const NaverIdLogin = (window as any).naver;

      const naverLogin = new NaverIdLogin.LoginWithNaverId({
        clientId: env.NEXT_PUBLIC_NAVER_CLIENT_ID,
        callbackUrl: env.NEXT_PUBLIC_REDIRECT_URL,
        isPopup: false,
      });

      naverLogin.init();

      fetch('/api/naver', {
        method: 'GET',

        headers: {
          Authorization: `Bearer ${decodeURIComponent(accessToken)}`,
        },
      })
        .then((res) => res.json())
        .then(({ response }) => {
          form.setValue('email', response.email);
          form.setValue('socialId', response.id);
        });
    } else if (type === 'kakao' && id) {
      fetch(`https://kapi.kakao.com/v2/user/me`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${id}` },
      })
        .then((res) => res.json())
        .then((response) => {
          form.setValue('email', response.kakao_account.email);
          form.setValue('socialId', response.id);
        })
        .catch((e) => console.error(e));
    } else if (type === 'apple') {
      const socialId = searchParams.get('socialId');
      const email = searchParams.get('email');
      if (socialId && email) {
        form.setValue('socialId', socialId);
        form.setValue('email', email);
      }
    }
  }, [searchParams]);

  const handleIdentityVerification = async () => {
    try {
      setIsVerifyLoading(true);
      // 폼 데이터를 로컬 스토리지에 저장
      const formData = form.getValues();
      sessionStorage.setItem('socialSignupFormData', JSON.stringify(formData));

      toggleVisibility();
      const verifiedResult = await verifyIdentity();

      const { verifiedCustomer } = verifiedResult;

      if (verifiedResult) {
        sessionStorage.setItem(
          'verifiedCustomer',
          JSON.stringify({
            ci: verifiedCustomer.ci,
            result: verifiedResult.status,
            name: verifiedCustomer.name,
            birthDate: verifiedCustomer.birthDate,
          }),
        );
      }

      const adult = isAdult(verifiedCustomer.birthDate);

      if (!adult) {
        toggleMinorRestrictionDialogVisibility();
        return;
      }

      if (verifiedCustomer) {
        form.setValue('gender', verifiedCustomer.gender);
        form.setValue('birthDate', verifiedCustomer.birthDate);
        form.setValue('name', verifiedCustomer.name);
        form.setValue('mobileNumber', verifiedCustomer.phoneNumber);

        handleStep(SocialSignUpSteps.PREVIEW);
      }
    } catch (error) {
      console.error('본인인증 중 오류 발생:', error);
    } finally {
      setIsVerifyLoading(false);
    }
  };

  useEffect(() => {
    if (!identityVerificationId || !identityVerificationTxId || !transactionType) return;

    setIsVerifyLoading(true);

    fetch('/api/identity/verify', {
      method: 'POST',
      body: JSON.stringify({ identityVerificationId, identityVerificationTxId, transactionType }),
    })
      .then((res) => res.json())
      .then(({ data }) => {
        if (data.verifiedCustomer) {
          // 저장된 폼 데이터 불러오기
          const savedFormData = sessionStorage.getItem('socialSignupFormData');

          const { verifiedCustomer } = data;

          if (verifiedCustomer) {
            sessionStorage.setItem(
              'verifiedCustomer',
              JSON.stringify({
                ci: verifiedCustomer.ci,
                result: data.status,
                name: verifiedCustomer.name,
                birthDate: verifiedCustomer.birthDate,
              }),
            );
          }

          const adult = isAdult(data.verifiedCustomer.birthDate);

          if (!adult) {
            toggleMinorRestrictionDialogVisibility();
            return;
          }

          if (savedFormData) {
            const parsedFormData = JSON.parse(savedFormData);
            // 본인인증 데이터로 덮어쓰기

            form.setValue('gender', data.verifiedCustomer.gender);
            form.setValue('birthDate', data.verifiedCustomer.birthDate);
            form.setValue('name', data.verifiedCustomer.name);
            form.setValue('mobileNumber', data.verifiedCustomer.phoneNumber);

            // 나머지 필드는 저장된 데이터로 복원
            Object.keys(parsedFormData).forEach((key: any) => {
              if (!['gender', 'birthDate', 'name', 'mobileNumber'].includes(key)) {
                form.setValue(key, parsedFormData[key]);
              }
            });

            // 사용 후 삭제
            sessionStorage.removeItem('socialSignupFormData');
          } else {
            // 저장된 데이터가 없는 경우 기본 설정
            form.setValue('gender', data.verifiedCustomer.gender);
            form.setValue('birthDate', data.verifiedCustomer.birthDate);
            form.setValue('name', data.verifiedCustomer.name);
            form.setValue('mobileNumber', data.verifiedCustomer.phoneNumber);
          }

          handleStep(SocialSignUpSteps.PREVIEW);
        }
      })
      .finally(() => {
        setIsVerifyLoading(false);
      });
  }, [identityVerificationId, identityVerificationTxId, transactionType]);

  return {
    form,
    onSubmit,
    isLoading,
    handleStep,
    isStep,
    onClick,
    step,
    isVisible,
    toggleVisibility,
    handleIdentityVerification,
    isIdentityLoading,
    isVerifyLoading,
    isMinorRestrictionDialogVisible,
    toggleMinorRestrictionDialogVisibility,
  };
};
