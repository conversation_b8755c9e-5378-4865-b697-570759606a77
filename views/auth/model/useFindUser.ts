import Link from 'next/link';
import React, { useState } from 'react';

import { FindUserSteps } from '@/entities/auth/types';

import { useStep } from '@/shared/model/useStep';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useFindUser = () => {
  const { step, handleStep, isStep } = useStep<FindUserSteps>(FindUserSteps.FORM);

  const [tab, setTab] = useState<'account' | 'password'>('account');

  const handleTabChange = (value: 'account' | 'password') => setTab(value);

  const { routerPush } = useWebViewRouter();

  return {
    step,
    handleStep,
    isStep,
    tab,
    handleTabChange,
    routerPush,
  };
};
