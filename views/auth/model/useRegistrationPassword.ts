import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import {
  RegistrationPasswordFormData,
  registrationPasswordSchema,
} from '@/features/auth/lib/registrationPasswordSchema';

import { checkCorporateResetToken } from '@/entities/auth/api/checkCorporateResetToken';
import { resendCorporateResetLink } from '@/entities/auth/api/resendCorporateResetLink';
import { resendGeneralResetLink } from '@/entities/auth/api/resendGeneralResetLink';
import { setCorporatePassword } from '@/entities/auth/api/setCorporatePassword';
import { setGeneralPassword } from '@/entities/auth/api/setGeneralPassword';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { TwoStep, UserCat } from '@/shared/types';

export const useRegistrationPassword = ({
  type,
}: {
  type: UserCat.GENERAL | UserCat.CORPORATE;
}) => {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const { step, isStep, handleStep } = useStep<TwoStep>(TwoStep.FIRST);
  const { successToast, errorToast } = useToast();
  const { routerPush } = useWebViewRouter();
  const { isVisible: isExpired, toggleVisibility: toggleExpired } = useVisibility();
  const { isVisible: isResendEmail, toggleVisibility: toggleResendEmail } = useVisibility();

  const form = useForm<RegistrationPasswordFormData>({
    resolver: zodResolver(registrationPasswordSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      password: '',
      rePassword: '',
    },
  });

  const onSubmit = async (formData: RegistrationPasswordFormData) => {
    if (!token) return;

    try {
      if (type === UserCat.CORPORATE) {
        await setCorporatePassword({
          token,
          password: formData.password,
        });
      } else {
        await setGeneralPassword({
          token,
          password: formData.password,
        });
      }
      successToast({
        title: '비밀번호 재설정 성공',
      });
      handleStep(TwoStep.SECOND);
      return true;
    } catch (error) {
      errorToast({
        title: '비밀번호 재설정 실패',
      });
      return false;
    }
  };

  const checkToken = async () => {
    if (!token) return;

    try {
      const { verified } = await checkCorporateResetToken(token);

      if (!verified) {
        toggleExpired();
        return;
      }
    } catch (error) {
      toggleExpired();
    }
  };

  useEffect(() => {
    if (token) {
      checkToken();
    }
  }, [token]);

  const resendEmail = async () => {
    if (!token) return;

    try {
      if (type === UserCat.CORPORATE) {
        await resendCorporateResetLink(token);
      } else {
        await resendGeneralResetLink(token);
      }

      toggleResendEmail();
    } catch {
      errorToast({
        title: '비밀번호 재설정 링크 재발송 실패',
      });
    }
  };

  const goToCallbackUrl = () => routerPush('/sign-in');

  return {
    form,
    onSubmit,
    isStep,
    goToCallbackUrl,
    resendEmail,
    isExpired,
    routerPush,
    isResendEmail,
    toggleResendEmail,
  };
};
