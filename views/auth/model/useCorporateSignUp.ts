import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';

import { useCorporateSignUpForm } from '@/features/auth/model/useCorporateSignUpForm';

import { applyForCorporateAccount } from '@/entities/auth/api/applyForCorporateAccount';
import { CorporateSignUpPayload } from '@/entities/auth/interface';
import { CorporateSignUpSteps } from '@/entities/auth/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';

import { BusinessSignUpFormData } from '../../../features/auth/lib/businessSignUpFormSchema';

export const useCorporateSignUp = () => {
  const { step, handleStep, isStep } = useStep<CorporateSignUpSteps>(CorporateSignUpSteps.TERMS);
  const ref = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { errorToast, successToast } = useToast();

  const router = useRouter();

  const { form, handleDeleteFile, handleUploadedFiles } = useCorporateSignUpForm();

  const handleFileUpload = () => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const onSubmit = async (data: BusinessSignUpFormData) => {
    try {
      setIsLoading(true);

      const payload: CorporateSignUpPayload = {
        email: data.email,
        companyName: data.companyName,
        crn: data.crn,
        brn: data.brn,
        representativeName: data.representativeName,
        managerName: data.managerName,
        managerMobileNumber: data.managerMobileNumber,
        brc: data.brc,
      };

      await applyForCorporateAccount(payload);

      successToast({
        title: '법인 회원가입 신청이 완료되었습니다.',
      });
      handleStep(CorporateSignUpSteps.COMPLETE);
    } catch (error) {
      errorToast({
        title: '법인 회원가입 신청에 실패했습니다.',
        duration: 4000,
      });
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = () => {
    router.replace('/');
  };

  return {
    step,
    handleStep,
    isStep,
    form,
    handleFileUpload,
    handleDeleteFile,
    handleUploadedFiles,
    onSubmit,
    isLoading,
    ref,
    handleComplete,
  };
};
