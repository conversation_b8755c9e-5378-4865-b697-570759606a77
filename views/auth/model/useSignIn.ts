import { signOut } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export const useSignIn = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const invalid = searchParams.get('invalid');

  useEffect(() => {
    if (invalid === 'true') {
      signOut({ redirectTo: '/sign-in' });
    }
  }, []);

  return {
    router,
    searchParams,
    invalid,
  };
};
