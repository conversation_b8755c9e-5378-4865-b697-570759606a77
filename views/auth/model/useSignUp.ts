'use client';

import { signIn } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { isAdult } from '@/features/auth/lib/isAdult';
import { useSignUpForm } from '@/features/auth/model/useSignUpForm';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';

import { checkMobileNumberDuplication } from '@/entities/auth/api/checkMobileNumberDuplication';
import { registerKyc } from '@/entities/auth/api/registerKyc';
import { SignUpSteps } from '@/entities/auth/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { SignUpFormData } from '../../../features/auth/lib/signUpFormSchema';

export const useSignUp = () => {
  const { successToast, errorToast } = useToast();
  const { step, handleStep, isStep } = useStep<SignUpSteps>(SignUpSteps.TYPE);
  const { isVisible, toggleVisibility } = useVisibility();
  const [isLoading, setIsLoading] = useState(false);
  const { verifyIdentity, isLoading: isIdentityLoading } = useIdentityVerification();
  const { form } = useSignUpForm();
  const searchParams = useSearchParams();
  const [isDuplicate, setIsDuplicate] = useState({
    isOpen: false,
    signupPath: [],
  });
  const {
    isVisible: isMinorRestrictionDialogVisible,
    toggleVisibility: toggleMinorRestrictionDialogVisibility,
  } = useVisibility();

  const [isVerifyLoading, setIsVerifyLoading] = useState(false);
  const { routerPush } = useWebViewRouter();
  const onSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);
    try {
      const response = await signIn('email-signup', {
        email: data.email,
        password: data.password,
        name: data.name,
        mobileNumber: data.mobileNumber,
        isSmsAgree: data.isSmsAgree,
        isEmailAgree: data.isEmailAgree,
        redirect: false,
      });

      if (response?.error) {
        throw new Error(response?.error || '회원가입에 실패했습니다.');
      }

      const verifiedCustomer = JSON.parse(sessionStorage.getItem('verifiedCustomer') || '{}');

      if (verifiedCustomer) {
        await registerKyc({
          ci: verifiedCustomer.ci,
          result: verifiedCustomer.result,
          name: verifiedCustomer.name,
          birthDate: verifiedCustomer.birthDate,
        });

        sessionStorage.removeItem('verifiedCustomer');
      }

      handleStep(SignUpSteps.COMPLETE);

      successToast({
        title: '회원가입 성공',
      });
    } catch (error) {
      console.error(error);
      errorToast({
        title: '회원가입 실패',
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleIdentityVerification = async () => {
    // 폼 데이터를 로컬 스토리지에 저장
    const formData = form.getValues();
    sessionStorage.setItem('signupFormData', JSON.stringify(formData));

    toggleVisibility();
    const verifiedResult = await verifyIdentity();

    const { verifiedCustomer } = verifiedResult;

    if (verifiedResult) {
      sessionStorage.setItem(
        'verifiedCustomer',
        JSON.stringify({
          ci: verifiedCustomer.ci,
          result: verifiedResult.status,
          name: verifiedCustomer.name,
          birthDate: verifiedCustomer.birthDate,
        }),
      );
    }

    const { data } = await checkMobileNumberDuplication(verifiedCustomer.phoneNumber);

    if (data.isExist) {
      setIsDuplicate({
        isOpen: true,
        signupPath: data.signupPath,
      });
      return;
    }

    const adult = isAdult(verifiedCustomer.birthDate);

    if (!adult) {
      toggleMinorRestrictionDialogVisibility();
      return;
    }

    if (verifiedCustomer) {
      form.setValue('gender', verifiedCustomer.gender);
      form.setValue('birthDate', verifiedCustomer.birthDate);
      form.setValue('name', verifiedCustomer.name);
      form.setValue('mobileNumber', verifiedCustomer.phoneNumber);

      handleStep(SignUpSteps.FORM);
    }
  };

  useEffect(() => {
    const identityVerificationId = searchParams.get('identityVerificationId'); // 포트원 인증 고유번호
    const identityVerificationTxId = searchParams.get('identityVerificationTxId'); // 요청 시 설정한 유니크 ID
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부

    if (identityVerificationId && identityVerificationTxId) {
      // setIsVerifyLoading(true);

      fetch('/api/identity/verify', {
        method: 'POST',
        body: JSON.stringify({ identityVerificationId, identityVerificationTxId, transactionType }),
      })
        .then((res) => res.json())
        .then(async ({ data }) => {
          if (data.verifiedCustomer) {
            // 저장된 폼 데이터 불러오기
            const savedFormData = sessionStorage.getItem('signupFormData');

            const { verifiedCustomer } = data;

            if (verifiedCustomer) {
              sessionStorage.setItem(
                'verifiedCustomer',
                JSON.stringify({
                  ci: verifiedCustomer.ci,
                  result: data.status,
                  name: verifiedCustomer.name,
                  birthDate: verifiedCustomer.birthDate,
                }),
              );
            }

            const adult = isAdult(data.verifiedCustomer.birthDate);

            if (!adult) {
              toggleMinorRestrictionDialogVisibility();
              return;
            }

            if (savedFormData) {
              const parsedFormData = JSON.parse(savedFormData);
              // 본인인증 데이터로 덮어쓰기
              form.setValue('gender', data.verifiedCustomer.gender);
              form.setValue('birthDate', data.verifiedCustomer.birthDate);
              form.setValue('name', data.verifiedCustomer.name);
              form.setValue('mobileNumber', data.verifiedCustomer.phoneNumber);

              // 나머지 필드는 저장된 데이터로 복원
              Object.keys(parsedFormData).forEach((key: any) => {
                if (!['gender', 'birthDate', 'name', 'mobileNumber'].includes(key)) {
                  form.setValue(key, parsedFormData[key]);
                }
              });

              // 사용 후 삭제
              sessionStorage.removeItem('signupFormData');
            } else {
              // 저장된 데이터가 없는 경우 기본 설정
              form.setValue('gender', data.verifiedCustomer.gender);
              form.setValue('birthDate', data.verifiedCustomer.birthDate);
              form.setValue('name', data.verifiedCustomer.name);
              form.setValue('mobileNumber', data.verifiedCustomer.phoneNumber);
            }

            const result = await checkMobileNumberDuplication(form.getValues('mobileNumber'));

            if (result.data.isExist) {
              setIsDuplicate({
                isOpen: true,
                signupPath: result.data.signupPath,
              });
              return;
            }

            handleStep(SignUpSteps.FORM);
          }
        });
    }
  }, [searchParams]);

  return {
    form,
    onSubmit,
    isLoading,
    step,
    handleStep,
    isStep,
    isVisible,
    toggleVisibility,
    handleIdentityVerification,
    isIdentityLoading,
    isDuplicate,
    isVerifyLoading,
    isMinorRestrictionDialogVisible,
    toggleMinorRestrictionDialogVisibility,
  };
};
