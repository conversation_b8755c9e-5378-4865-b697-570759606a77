'use client';

import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useSignUp } from '@/views/auth/model/useSignUp';

import { SignUpType } from '@/widgets/auth/ui/SignUpType';

import { DuplicateConfirmDialog } from '@/features/auth/ui/DuplicateConfirmDialog';
import { SignUpForm } from '@/features/auth/ui/SignUpForm';
import { SignUpTerms } from '@/features/auth/ui/SignUpTerms';
import { IdentityDialog } from '@/features/identity/ui/IdentityDialog';

import { SignUpSteps } from '@/entities/auth/types';

import * as gtag from '@/shared/lib/gtag';
import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { AuthMobileBackButton } from '@/shared/ui/AuthMobileBackButton';
import { CompleteWidget } from '@/shared/ui/CompleteWidget';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

export const SignUpVIew = () => {
  const {
    form,
    onSubmit,
    isLoading,
    step,
    handleStep,
    isStep,
    toggleVisibility,
    isVisible,
    handleIdentityVerification,
    isDuplicate,
    isVerifyLoading,
    isMinorRestrictionDialogVisible,
  } = useSignUp();
  const callbackUrl = Cookies.get('callback-url') || '/';
  const router = useRouter();

  usePageGNB({ isGNBVisible: false });

  const handleStepChange = () => {
    if (isStep(SignUpSteps.TERMS)) {
      // 현재 단계 완료 이벤트
      gtag.event({
        action: 'signup_step_complete',
        category: 'conversion',
        label: `step_${step}`,
        value: 1,
      });
      toggleVisibility();

      return;
    }

    if (isStep(SignUpSteps.FORM)) {
      gtag.event({
        action: 'signup_identity_verification',
        category: 'conversion',
        label: 'email_signup',
        value: 1,
      });
      return;
    }

    if (isStep(SignUpSteps.COMPLETE)) {
      // 회원가입 완료 이벤트
      gtag.event({
        action: 'signup_complete',
        category: 'conversion',
        label: 'email_signup',
        value: 1,
      });
    }
  };

  // 최초 진입 이벤트
  useEffect(() => {
    if (isStep(SignUpSteps.TYPE)) {
      gtag.event({
        action: 'signup_start',
        category: 'conversion',
        label: 'email_signup',
        value: 1,
      });
    }
  }, []);

  if (isVerifyLoading) {
    return <Loading />;
  }

  const { routerPush } = useWebViewRouter();

  return (
    <section className="sm:py-[72px]">
      <AuthMobileBackButton isFirstStep={isStep(SignUpSteps.TYPE)} />
      <div className="flex flex-col items-center sm:min-h-dvh">
        <button onClick={() => routerPush('/')}>
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="mb-12 hidden sm:block"
          />
        </button>
        {isStep(SignUpSteps.TYPE) && (
          <SignUpType handleStep={() => handleStep(SignUpSteps.TERMS)} />
        )}
        {isStep(SignUpSteps.TERMS) && (
          <SignUpTerms handleNextStep={handleStepChange} form={form} disabled={isVerifyLoading} />
        )}
        {isStep(SignUpSteps.FORM) && (
          <SignUpForm onSubmit={onSubmit} form={form} isLoading={isLoading} />
        )}
        {isStep(SignUpSteps.COMPLETE) && (
          <CompleteWidget
            titleBefore="회원가입이"
            titleHighlight=" 완료"
            titleAfter="되었습니다."
            description="회원가입 절차가 모두 완료되었습니다."
            imageSrc="/icons/complete.png"
          >
            <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
              <PrimaryButton
                onClick={() => {
                  Cookies.remove('callback-url');
                  router.replace(callbackUrl);
                }}
                text="시작하기"
                className="!h-12 w-full text-base sm:mt-16"
              />
            </div>
          </CompleteWidget>
        )}
      </div>

      <IdentityDialog
        isOpen={isVisible}
        social={false}
        handleOpen={toggleVisibility}
        handleVerify={handleIdentityVerification}
      />
      <DuplicateConfirmDialog
        isOpen={isDuplicate.isOpen}
        handleOpen={() => routerPush('/sign-in')}
        signupPath={isDuplicate.signupPath}
      />
      <ConfirmDialog
        isOpen={isMinorRestrictionDialogVisible}
        handleOpen={() => routerPush('/sign-up')}
        handleAction={() => routerPush('/sign-up')}
        title="서비스 이용 불가 안내"
        description="미성년자는 뉴밋 서비스를 이용할 수 없습니다."
        isCancelButton={false}
        text="확인"
      />
    </section>
  );
};
