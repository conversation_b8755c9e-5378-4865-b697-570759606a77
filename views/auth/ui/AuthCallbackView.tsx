'use client';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { Loading } from '@/shared/ui/Loading';

import { useAuthCallback } from '../model/useAuthCallback';

export const AuthCallbackView = () => {
  const { isAlertVisible, alertMessage, toggleAlertVisibility, handleAlertAction } =
    useAuthCallback();

  if (isAlertVisible) {
    return (
      <ConfirmDialog
        isOpen={isAlertVisible}
        handleOpen={toggleAlertVisibility}
        title="회원가입 안내"
        description={alertMessage}
        handleAction={handleAlertAction}
        isCancelButton={false}
      />
    );
  }

  return <Loading />;
};
