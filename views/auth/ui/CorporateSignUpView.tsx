'use client';

import { ArrowLeftIcon } from '@heroicons/react/24/solid';

import { useCorporateSignUp } from '@/views/auth/model/useCorporateSignUp';

import { CorporateSignUpForm } from '@/features/auth/ui/CorporateSignUpForm';

import { CorporateSignUpSteps } from '@/entities/auth/types';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { CompleteWidget } from '@/shared/ui/CompleteWidget';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

export const CorporateSignUpView = () => {
  const {
    isStep,
    form,
    handleFileUpload,
    handleDeleteFile,
    handleUploadedFiles,
    onSubmit,
    isLoading,
    ref,
    handleComplete,
  } = useCorporateSignUp();

  const { routerPush } = useWebViewRouter();

  return (
    <section className={`${!isStep(CorporateSignUpSteps.COMPLETE) && 'sm:my-12'} `}>
      {isStep(CorporateSignUpSteps.TERMS) && (
        <div className="fixed top-0 z-10 flex h-[42px] w-full items-center justify-center bg-white sm:hidden">
          <button onClick={() => routerPush('/sign-in')} className="absolute left-4 top-[10px]">
            <ArrowLeftIcon className="h-6 w-6" strokeWidth={2} />
          </button>
          <h2 className="font-semibold leading-[150%]">법인 회원가입 신청</h2>
        </div>
      )}
      <div
        className={`flex flex-col items-center justify-center px-0 sm:px-8 ml:px-0 ${isStep(CorporateSignUpSteps.COMPLETE) && 'h-dvh'}`}
      >
        <button onClick={() => routerPush('/')}>
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="mb-12 hidden sm:block"
          />
        </button>
        {isStep(CorporateSignUpSteps.TERMS) && (
          <CorporateSignUpForm
            form={form}
            handleDeleteFile={handleDeleteFile}
            handleUploadedFiles={handleUploadedFiles}
            ref={ref}
            handleFileUpload={handleFileUpload}
            onSubmit={onSubmit}
            isLoading={isLoading}
          />
        )}
        {isStep(CorporateSignUpSteps.COMPLETE) && (
          <CompleteWidget
            titleBefore="법인 회원가입 신청이"
            titleHighlight=" 완료"
            titleAfter="되었습니다."
            imageSrc="/icons/complete.png"
            description={`신청 결과는 빠른 시일 내에 이메일로 안내해 드릴 예정이며 \n 필요한 경우 뉴밋 담당자가 직접 연락을 드릴 수 있습니다. \n 법인 회원가입 관련 문의 사항이 있으신 경우 \n 뉴밋 고객센터(1600-8625)로 문의해 주시기 바랍니다.`}
          >
            <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
              <PrimaryButton
                onClick={handleComplete}
                text="다음"
                className="!h-12 w-full text-base sm:mt-16"
              />
            </div>
          </CompleteWidget>
        )}
      </div>
    </section>
  );
};
