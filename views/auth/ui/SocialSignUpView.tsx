'use client';

import { useEffect } from 'react';

import { SocialInfoPreview } from '@/widgets/auth/ui/SocialInfoPreview';

import { SignUpMobileForm } from '@/features/auth/ui/SignUpMobileForm';
import { SignUpNameForm } from '@/features/auth/ui/SignUpNameForm';
import { SignUpTerms } from '@/features/auth/ui/SignUpTerms';
import { SocialSignUpEmailForm } from '@/features/auth/ui/SocialSignUpEmailForm';

import { SocialSignUpSteps } from '@/entities/auth/types';

import * as gtag from '@/shared/lib/gtag';
import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { AuthMobileBackButton } from '@/shared/ui/AuthMobileBackButton';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';

import { useSocialAuth } from '../model/useSocialAuth';

export const SocialSignUpView = () => {
  const {
    step,
    isStep,
    form,
    onClick,
    isLoading,
    isVisible,
    toggleVisibility,
    handleIdentityVerification,
    isVerifyLoading,
    isMinorRestrictionDialogVisible,
  } = useSocialAuth();

  usePageGNB({ isGNBVisible: false });

  const handleStepChange = () => {
    if (isStep(SocialSignUpSteps.TERMS)) {
      // 현재 단계 완료 이벤트
      gtag.event({
        action: 'signup_step_terms',
        category: 'conversion',
        label: `step_${step}_social`,
        value: 1,
      });
      toggleVisibility();

      return;
    }

    if (isStep(SocialSignUpSteps.PREVIEW)) {
      gtag.event({
        action: 'signup_step_preview',
        category: 'conversion',
        label: 'social_signup',
        value: 2,
      });

      return;
    }

    if (isStep(SocialSignUpSteps.COMPLETE)) {
      // 회원가입 완료 이벤트
      gtag.event({
        action: 'signup_complete',
        category: 'conversion',
        label: 'social_signup',
        value: 3,
      });
    }
  };

  const handleSocialInfoPreview = () => {
    // 현재 단계 완료 이벤트
    gtag.event({
      action: 'signup_step_complete',
      category: 'conversion',
      label: `step_${step}_social`,
      value: 2,
    });

    // 다음 단계 시작 이벤트
    gtag.event({
      action: 'signup_step_start',
      category: 'conversion',
      label: `step_${step + 1}_social`,
      value: 3,
    });

    onClick();
  };

  // 최초 진입 이벤트
  useEffect(() => {
    if (isStep(SocialSignUpSteps.TERMS)) {
      gtag.event({
        action: 'signup_start',
        category: 'conversion',
        label: 'social_signup',
        value: 1,
      });
    }
  }, []);

  const { routerPush } = useWebViewRouter();

  if (isVerifyLoading) {
    return <Loading />;
  }

  return (
    <section>
      <AuthMobileBackButton isFirstStep={isStep(SocialSignUpSteps.TERMS)} />
      <div className="flex flex-col items-center justify-center sm:h-dvh">
        <button onClick={() => routerPush('/')}>
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="mb-12 hidden sm:block"
          />
        </button>

        {isStep(SocialSignUpSteps.TERMS) && (
          <SignUpTerms handleNextStep={handleStepChange} form={form} disabled={isVerifyLoading} />
        )}
        {isStep(SocialSignUpSteps.PREVIEW) && (
          <SocialInfoPreview
            isLoading={isLoading}
            form={form}
            handleNextStep={handleSocialInfoPreview}
          />
        )}
        {isStep(SocialSignUpSteps.EMAIL) && (
          <SocialSignUpEmailForm handleNextStep={handleStepChange} form={form} />
        )}
        {isStep(SocialSignUpSteps.MOBILE) && (
          <SignUpMobileForm handleNextStep={handleStepChange} form={form} />
        )}
        {isStep(SocialSignUpSteps.NAME) && (
          <SignUpNameForm
            onSocialSignUp={handleSocialInfoPreview}
            isLoading={isLoading}
            form={form}
          />
        )}
      </div>
      <ConfirmDialog
        isOpen={isVisible}
        handleOpen={toggleVisibility}
        title="휴대폰 본인인증 안내"
        description={`소셜 회원가입 진행을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
        handleAction={handleIdentityVerification}
      />
      <ConfirmDialog
        isOpen={isMinorRestrictionDialogVisible}
        handleOpen={() => routerPush('/sign-up')}
        handleAction={() => routerPush('/sign-up')}
        title="서비스 이용 불가 안내"
        description="미성년자는 뉴밋 서비스를 이용할 수 없습니다."
        isCancelButton={false}
        text="확인"
      />
    </section>
  );
};
