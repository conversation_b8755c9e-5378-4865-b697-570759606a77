'use client';

import { motion } from 'framer-motion';

import { CorporatePassword } from '@/features/auth/ui/CorporatePassword';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { TwoStep, UserCat } from '@/shared/types';
import { AuthMobileBackButton } from '@/shared/ui/AuthMobileBackButton';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

import { useRegistrationPassword } from '../model/useRegistrationPassword';

export const GeneralPasswordView = () => {
  usePageGNB({ isGNBVisible: false });

  const {
    form,
    onSubmit,
    isStep,
    goToCallbackUrl,
    isExpired,
    routerPush,
    resendEmail,
    isResendEmail,
    toggleResendEmail,
  } = useRegistrationPassword({ type: UserCat.GENERAL });

  const { handleSubmit, formState } = form;

  return (
    <section className="mx-auto flex h-full w-full max-w-[640px] flex-col items-center justify-center sm:h-dvh">
      {isExpired ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-full">
            <div className="mb-12 hidden justify-center sm:flex">
              <button onClick={() => routerPush('/')} className="">
                <FallbackImage
                  src="/logo/numit_logo.png"
                  alt="numit logo"
                  width={148}
                  height={36}
                />
              </button>
            </div>
            <div className="flex h-[calc(100dvh-80px)] w-full flex-col justify-center rounded-[20px] border-gray-300 px-6 py-6 sm:h-auto sm:border sm:px-[100px] sm:py-20">
              <div className="flex flex-col items-center justify-center">
                <FallbackImage src="/icons/withdraw.png" alt="complete" width={80} height={80} />

                <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">
                  링크가 만료되었습니다.
                </h3>
                <h6 className="whitespace-pre-line text-center text-base sm:text-lg">
                  하단의 재발송 버튼을 누르면 <br className="sm:hidden" />
                  링크가 재발송 됩니다.
                </h6>
              </div>
              <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
                <PrimaryButton
                  text="링크 재발송"
                  className="w-full text-base sm:mt-16"
                  onClick={resendEmail}
                />
              </div>
            </div>
          </div>
        </motion.div>
      ) : isStep(TwoStep.FIRST) ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full"
        >
          <AuthMobileBackButton isFirstStep />
          <div className="mb-12 hidden justify-center sm:flex">
            <button onClick={() => routerPush('/')} className="">
              <FallbackImage src="/logo/numit_logo.png" alt="numit logo" width={148} height={36} />
            </button>
          </div>
          <div className="flex flex-col justify-between rounded-[20px] border-gray-300 px-6 py-6 sm:h-auto sm:justify-start sm:border sm:px-[100px] sm:py-20">
            <CorporatePassword form={form} />
            <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
              <PrimaryButton
                type="button"
                className={`mt-12 h-12 w-full text-base`}
                text="확인"
                onClick={handleSubmit(onSubmit)}
                disabled={!formState.isValid}
              />
            </div>
          </div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full"
        >
          <div className="mb-12 hidden justify-center sm:flex">
            <button onClick={() => routerPush('/')} className="">
              <FallbackImage src="/logo/numit_logo.png" alt="numit logo" width={148} height={36} />
            </button>
          </div>
          <div className="flex w-full flex-col justify-center rounded-[20px] border-gray-300 px-6 py-6 sm:h-auto sm:border sm:px-[100px] sm:py-20">
            <div className="flex flex-col items-center justify-center">
              <FallbackImage src="/icons/complete.png" alt="complete" width={80} height={80} />

              <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">
                회원가입이 완료되었습니다.
              </h3>
              <h6 className="whitespace-pre-line text-center text-base sm:text-lg">
                회원가입 절차가 모두 완료되었습니다.
              </h6>
            </div>
            <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
              <PrimaryButton
                text="시작하기"
                className="w-full text-base sm:mt-16"
                onClick={goToCallbackUrl}
              />
            </div>
          </div>
        </motion.div>
      )}
      <ConfirmDialog
        isOpen={isResendEmail}
        handleOpen={toggleResendEmail}
        handleAction={goToCallbackUrl}
        isCancelButton={false}
        title={`링크가 재발송 되었습니다. \n 발송된 이메일을 확인해 주세요.`}
      />
    </section>
  );
};
