'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';

import { AppleLoginButton } from '@/features/auth/ui/AppleLoginButton';
import { KakaoLoginButton } from '@/features/auth/ui/KakaoLoginButton';
import { NaverLoginButton } from '@/features/auth/ui/NaverLoginButton';
import { SignInForm } from '@/features/auth/ui/SignInForm';

import { SignType } from '@/entities/auth/types';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';
import { Separator } from '@/shared/ui/shadcn/separator';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

import { useSignIn } from '../model/useSignIn';

export const SignInView = () => {
  const { router, invalid } = useSignIn();

  usePageGNB({ isGNBVisible: false });

  const { routerPush } = useWebViewRouter();

  if (invalid) return <Loading />;

  return (
    <section>
      <div className="flex h-[50px] justify-end px-6 pt-2 sm:hidden">
        <button onClick={() => router.replace('/')}>
          <XMarkIcon className="h-6 w-6" strokeWidth={2} />
        </button>
      </div>
      <div className="flex flex-col items-center sm:min-h-dvh sm:py-12">
        <button onClick={() => router.replace('/')} className="hidden sm:block">
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="sm:mb-12"
          />
        </button>

        <motion.div
          className="w-full max-w-[640px] rounded-[20px] border-gray-300 px-6 pb-20 sm:border sm:px-[100px] sm:py-20 sm:pb-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Tabs defaultValue="email">
            <TabsList className="mb-10 grid h-[50px] w-full grid-cols-2 sm:mb-16">
              <TabsTrigger
                value="email"
                className="text-20 h-[50px] rounded-none border-b border-gray-300 text-gray-300 transition-all duration-300 data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900"
              >
                개인회원
              </TabsTrigger>
              <TabsTrigger
                value="coporate"
                className="text-20 h-[50px] rounded-none border-b border-gray-300 text-gray-300 transition-all duration-300 data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900"
              >
                법인회원
              </TabsTrigger>
            </TabsList>
            <TabsContent value="email">
              <SignInForm />
              <div className="my-6 flex items-center gap-2 sm:my-8">
                <Separator className="flex-1 bg-gray-150" />
                <p className="text-xs text-[#9D9995]">또는</p>
                <Separator className="flex-1 bg-gray-150" />
              </div>
              <div className="mt-6 space-y-3 sm:mt-8">
                <AppleLoginButton type={SignType.SIGNIN} text="Apple로 로그인" />
                <KakaoLoginButton type={SignType.SIGNIN} text="카카오로 로그인" />
                <NaverLoginButton type={SignType.SIGNIN} text="네이버로 로그인" />
              </div>
              <div className="my-10 flex items-center justify-center gap-6 text-xs font-semibold text-gray-700">
                <p>
                  <button onClick={() => routerPush('/find-user')}>아이디/비밀번호 찾기</button>
                </p>
                <Separator orientation="vertical" className="h-4 bg-gray-300" />
                <p>
                  <button onClick={() => routerPush('/sign-up')}>회원가입</button>
                </p>
              </div>
            </TabsContent>
            <TabsContent value="coporate">
              <SignInForm isCorporate />
              <div className="mb-20 mt-16 flex items-center justify-center gap-6 text-xs font-semibold text-gray-700">
                <p>
                  <button onClick={() => routerPush('/find-user')}>아이디/비밀번호 찾기</button>
                </p>
                <Separator orientation="vertical" className="h-4 bg-gray-300" />
                <p>
                  <button onClick={() => routerPush('/sign-up/corporate')}>
                    법인 회원가입 신청
                  </button>
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </section>
  );
};
