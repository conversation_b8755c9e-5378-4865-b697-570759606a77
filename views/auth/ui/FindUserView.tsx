'use client';

import { FindEmail } from '@/widgets/auth/ui/FindEmail';
import { FindPassword } from '@/widgets/auth/ui/FindPassword';

import { FindUserSteps } from '@/entities/auth/types';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { AuthMobileBackButton } from '@/shared/ui/AuthMobileBackButton';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

import { useFindUser } from '../model/useFindUser';

export const FindUserView = () => {
  const { step, handleStep, isStep, tab, handleTabChange, routerPush } = useFindUser();

  usePageGNB({ isGNBVisible: false });

  return (
    <section>
      <AuthMobileBackButton isFirstStep={false} />
      <div className="flex flex-col items-center justify-center sm:h-dvh">
        <button onClick={() => routerPush('/')}>
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="mb-12 hidden sm:block"
          />
        </button>
        <AuthFormContainer>
          <Tabs defaultValue={tab}>
            {!(tab === 'password' && isStep(FindUserSteps.COMPLETE)) && (
              <TabsList
                className={`grid w-full grid-cols-2 text-gray-300 ${!isStep(FindUserSteps.FORM) && 'hidden sm:grid'} `}
              >
                <TabsTrigger
                  disabled={!isStep(FindUserSteps.FORM)}
                  value="account"
                  onClick={() => handleTabChange('account')}
                  className="h-[50px] rounded-none text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
                >
                  아이디 찾기
                </TabsTrigger>
                <TabsTrigger
                  disabled={!isStep(FindUserSteps.FORM)}
                  value="password"
                  onClick={() => handleTabChange('password')}
                  className="h-[50px] rounded-none text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
                >
                  비밀번호 재설정
                </TabsTrigger>
              </TabsList>
            )}

            <TabsContent value="account">
              <FindEmail step={step} isStep={isStep} handleStep={handleStep} />
            </TabsContent>
            <TabsContent value="password">
              <FindPassword />
            </TabsContent>
          </Tabs>
        </AuthFormContainer>
      </div>
    </section>
  );
};
