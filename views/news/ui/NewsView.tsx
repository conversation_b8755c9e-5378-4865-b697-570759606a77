'use client';

import React, { Suspense } from 'react';

import { EventListSkeleton } from '@/widgets/events/ui/EventListSkeleton';
import { NewsList } from '@/widgets/news/ui/NewsList';

import { newsSearchOptions } from '@/features/news/config';

import { CmsCommonSearchForm } from '@/shared/ui/CmsCommonSearchForm';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';

export const NewsView = () => {
  return (
    <section className="container mt-10 pb-20 sm:mt-14 ml:mt-[100px]">
      <ContentsTitle
        title="언론자료"
        className="text-28 sm:text-32 ml:text-44 mb-10 sm:mb-12 ml:mb-20"
      />

      <CmsCommonSearchForm searchOptions={newsSearchOptions} />

      <Suspense fallback={<EventListSkeleton />}>
        <NewsList />
      </Suspense>
    </section>
  );
};
