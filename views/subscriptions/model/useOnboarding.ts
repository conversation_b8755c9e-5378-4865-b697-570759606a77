import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useAccountRegister } from '@/features/deposit/model/useAccountRegister';
import { useInvestorPropensity } from '@/features/investor/model/useInvestorPropensity';
import { useInvestorSuitability } from '@/features/investor/model/useInvestorSuitability';
import { useOcr } from '@/features/ocr/model/useOcr';
import {
  accountSideOptions,
  ocrSideOptions,
  propensitySideOptions,
  suitabiltySideOptions,
} from '@/features/subscription/config';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { InvestorSuitabilityTestStep, PropensitySteps } from '@/entities/investor/types';
import { SubscriptionOnboardingSteps } from '@/entities/subscriptions/types';

import { useStep } from '@/shared/model/useStep';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useOnboarding = () => {
  const { step, isStep, handleStep } = useStep<SubscriptionOnboardingSteps>(
    SubscriptionOnboardingSteps.SUITABILITY,
  );
  const searchParams = useSearchParams();
  const subscriptionId = searchParams.get('subscriptionId');
  const { routerPush } = useWebViewRouter();
  const [isInitialized, setIsInitialized] = useState(false);
  const { isVisible: isExitDialogVisible, toggleVisibility: toggleExitDialogVisibility } =
    useVisibility();
  const router = useRouter();

  const {
    isOcrLoading,
    isVerifiedLoading,
    form: ocrForm,
    handleFileUpload,
    fileUploaderRef,
    handleVerify,
    step: ocrStep,
    handleFileChange,
    goToCallbackUrl,
    isOcrStep,
    isErrorDialogVisible,
    toggleErrorDialog,
  } = useOcr();

  const {
    step: accountStep,
    handleStep: handleAccountNextStep,
    form: accountForm,
    handleVerifyAccount,
    handleCreateVirtualAccount,
    virtualAccount,
    isAgree,
    toggleAgree,
    isLoading,
    isIdentityVerification,
    toggleIdentityVerification,
    handleIdentityVerification,
  } = useAccountRegister();

  const {
    selectedType,
    terms,
    answers,
    handlePrev,
    handleSingleAnswer,
    handleMultiAnswer,
    handleMultiRatioAnswer,
    handleSelectedType,
    handleTerms,
    isDisabled: isPropensityDisabled,
    isStep: isPropensityStep,
    currentQuestion,
    step: propensityStep,
    handleNext: handlePropensityNext,
    handleStep: handlePropensityStep,
    propensityScore,
  } = useInvestorPropensity({ onboarding: true });

  const {
    selectedTest,
    isSelected,
    questions,
    testIndex,
    handleSelectTest,
    handlePreviousTest,
    handleRetakeTest,
    isWrongAnswerVisible,
    toggleWrongAnswerVisibility,
    handleNextTest,
    isDisabled: isSuitabilityDisabled,
    isEndTest: isSuitabilityEndTest,
    step: suitabilityStep,
    isStep: isSuitabilityStep,
    handleStep: handleSuitabilityStep,
  } = useInvestorSuitability();

  const isPropensityLastStep = isPropensityStep(PropensitySteps.RESULT);

  const isPropensityFirstStep = isPropensityStep(PropensitySteps.TYPE);

  const { user, isExistAccount } = useFetchUser();

  useEffect(() => {
    if (isInitialized) return;
    if (!user) return;

    if (!user.userAuthOCR) {
      handleStep(SubscriptionOnboardingSteps.OCR);
      setIsInitialized(true);
      return;
    }

    if (!isExistAccount) {
      handleStep(SubscriptionOnboardingSteps.ACCOUNT);
      setIsInitialized(true);
      return;
    }

    if (!user.investorSuitabilityTest) {
      handleStep(SubscriptionOnboardingSteps.SUITABILITY);
      setIsInitialized(true);
      return;
    }

    setIsInitialized(true);
  }, [user, isExistAccount]);

  const sideStep = (step: SubscriptionOnboardingSteps) => {
    switch (step) {
      case SubscriptionOnboardingSteps.OCR:
        return ocrStep;
      case SubscriptionOnboardingSteps.ACCOUNT:
        return accountStep;
      case SubscriptionOnboardingSteps.SUITABILITY:
        if (isSuitabilityStep(InvestorSuitabilityTestStep.NOTICE)) return 1;
        if (isSuitabilityStep(InvestorSuitabilityTestStep.QUESTION)) return 2;
        if (isSuitabilityStep(InvestorSuitabilityTestStep.RESULT)) return 3;
        return 4;
      default:
        return 3;
    }
  };

  const sideOptions = () => {
    switch (step) {
      case SubscriptionOnboardingSteps.OCR:
        return ocrSideOptions;
      case SubscriptionOnboardingSteps.ACCOUNT:
        return accountSideOptions;
      case SubscriptionOnboardingSteps.SUITABILITY:
        return suitabiltySideOptions;
      default:
        return [];
    }
  };

  const handleNextStep = () => {
    if (isStep(SubscriptionOnboardingSteps.OCR)) {
      if (!isExistAccount) {
        handleStep(SubscriptionOnboardingSteps.ACCOUNT);
        return;
      }
      if (!user?.investorSuitabilityTest) {
        handleStep(SubscriptionOnboardingSteps.SUITABILITY);
        return;
      }
    } else if (isStep(SubscriptionOnboardingSteps.ACCOUNT)) {
      if (!user?.investorSuitabilityTest) {
        handleStep(SubscriptionOnboardingSteps.SUITABILITY);
        return;
      }
    }

    routerPush(`/subscriptions/apply/${subscriptionId}`);
  };

  const handleExit = () => {
    toggleExitDialogVisibility();
    if (subscriptionId) {
      router.replace(`/subscriptions/${subscriptionId}`);
    } else {
      router.replace('/subscriptions');
    }
  };

  return {
    step,
    isStep,
    handleStep,
    ocr: {
      isOcrLoading,
      isVerifiedLoading,
      form: ocrForm,
      handleFileUpload,
      fileUploaderRef,
      handleVerify,
      step: ocrStep,
      handleFileChange,
      goToCallbackUrl,
      isOcrStep,
      isErrorDialogVisible,
      toggleErrorDialog,
    },
    account: {
      step: accountStep,
      handleAccountNextStep,
      form: accountForm,
      handleVerifyAccount,
      handleCreateVirtualAccount,
      virtualAccount,
      isAgree,
      toggleAgree,
      isAccountLoading: isLoading,
      isIdentityVerification,
      toggleIdentityVerification,
      handleIdentityVerification,
    },
    propensity: {
      selectedType,
      terms,
      answers,
      handlePrev,
      handleSingleAnswer,
      handleMultiAnswer,
      handleMultiRatioAnswer,
      handleSelectedType,
      handleTerms,
      isPropensityDisabled,
      isStep: isPropensityStep,
      currentQuestion,
      step: propensityStep,
      handlePropensityNext,
      handleStep: handlePropensityStep,
      propensityScore,
    },
    suitability: {
      selectedTest,
      isSelected,
      questions,
      testIndex,
      handleSelectTest,
      handlePreviousTest,
      handleRetakeTest,
      isWrongAnswerVisible,
      toggleWrongAnswerVisibility,
      handleNextTest,
      isSuitabilityDisabled,
      isSuitabilityEndTest,
      suitabilityStep: suitabilityStep,
      handleSuitabilityStep: handleSuitabilityStep,
      isSuitabilityStep,
    },
    sideStep,
    sideOptions,
    handleNextStep,
    isExitDialogVisible,
    toggleExitDialogVisibility,
    handleExit,
  };
};
