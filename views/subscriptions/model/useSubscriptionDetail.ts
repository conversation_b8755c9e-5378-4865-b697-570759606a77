import { useQueryClient } from '@tanstack/react-query';
import { signOut } from 'next-auth/react';
import { useParams } from 'next/navigation';
import { useRef, useState } from 'react';

import { queries } from '@/features/lib/queries';
import { fetchSubscription } from '@/features/subscription/api/fetchSubscription';
import { fetchSubscriptionStatistics } from '@/features/subscription/api/fetchSubscriptionStatistics';
import { checkNightTime } from '@/features/subscription/lib/checkNightTime';
import { fetchUserNotification } from '@/features/users/api/fetchUserNotification';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { getSubscriptionApplyLimit } from '@/entities/subscriptions/api/getSubscriptionApplyLimit';
import { Subscription } from '@/entities/subscriptions/types';
import { updateUserNotification } from '@/entities/users/api/updateUserNotification';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { NotificationChannel, NotificationType, UserRole } from '@/shared/types';

export const useSubscriptionDetail = (subscription: Subscription) => {
  const { id } = useParams();
  const { user, isExistAccount, session } = useFetchUser();
  const { data: subscriptionDetail } = fetchSubscription(id as string, subscription);
  const { data: statistics } = fetchSubscriptionStatistics(
    subscription.subscriptionInfoId.toString(),
  );
  const { isVisible: isApplyCheckLoading, toggleVisibility: toggleApplyCheckLoading } =
    useVisibility();
  const { isVisible: isNoticeDialogVisible, toggleVisibility: toggleNoticeDialogVisibility } =
    useVisibility();

  const [applyLimitInfo, setApplyLimitInfo] = useState({
    isCountLimit: false,
    remainingTime: '',
  });

  // 다이얼로그 상태 관리
  const [dialogState, setDialogState] = useState<
    | 'idle'
    | 'nightTime'
    | 'onboarding'
    | 'notificationConsent'
    | 'notificationComplete'
    | 'notificationCancel'
    | 'notificationLogin'
  >('idle');

  // 다이얼로그 표시 함수들
  const showDialog = (state: typeof dialogState) => setDialogState(state);
  const hideDialog = () => setDialogState('idle');

  // 각 다이얼로그 상태
  const isNightTimeLimitVisible = dialogState === 'nightTime';
  const isOnboardingVisible = dialogState === 'onboarding';
  const isNotificationConsentVisible = dialogState === 'notificationConsent';
  const isNotificationCompleteVisible = dialogState === 'notificationComplete';
  const isNotificationCancelVisible = dialogState === 'notificationCancel';
  const isNotificationLoginVisible = dialogState === 'notificationLogin';

  const queryClient = useQueryClient();
  const { errorToast } = useToast();

  const { applyStatistics, infoStatistics } = statistics ?? {};

  const attachFilesRef = useRef<HTMLDivElement>(null);

  const goToAttachFiles = () => {
    attachFilesRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const { routerPush, isApp } = useWebViewRouter();

  const { data: notificationData } = fetchUserNotification(!!user);

  const notification = {
    email: notificationData?.channel?.includes(NotificationChannel.EMAIL) || false,
    sms: notificationData?.channel?.includes(NotificationChannel.SMS) || false,
  };

  const isNewsletter = notificationData?.type?.includes(NotificationType.NEWSLETTER);

  const goToApply = async () => {
    toggleApplyCheckLoading();

    const isUserVerified =
      user?.userAuthOCR &&
      // user?.investorPropensity &&
      user?.investorSuitabilityTest &&
      isExistAccount;

    if (!session) {
      if (isApp) {
        await signOut();
        WebViewMessage('logout', {});
        routerPush('/sign-in');
        return;
      } else {
        await signOut({ redirectTo: '/sign-in' });
        return;
      }
    }

    if (checkNightTime()) {
      showDialog('nightTime');
      return;
    }

    if (!isUserVerified) {
      showDialog('onboarding');
      return;
    }

    const isAccess = await getSubscriptionApplyLimit(subscription.subscriptionInfoId.toString());

    if (isAccess.currentApplyCount >= 10) {
      setApplyLimitInfo({
        isCountLimit: true,
        remainingTime: isAccess.appliedListDuringPeriod[0].createdAt,
      });
      return;
    }

    toggleApplyCheckLoading();

    return routerPush(`/subscriptions/apply/${subscription?.subscriptionInfoId}`);
  };

  const goToOnboarding = () => {
    hideDialog();
    routerPush(`/subscriptions/onboarding?subscriptionId=${subscription?.subscriptionInfoId}`);
  };

  const handleNotification = async (value: NotificationType) => {
    if (!session) {
      showDialog('notificationLogin');
      return;
    }

    const currentTypes = [...(notificationData?.type || [NotificationType.SERVICE])];
    let updatedTypes: NotificationType[];

    if (currentTypes.includes(value)) {
      updatedTypes = currentTypes.filter((type) => type !== value);
    } else {
      updatedTypes = [...new Set([...currentTypes, value])];
    }

    try {
      await updateUserNotification({
        type: updatedTypes,
        channel: [...(notificationData?.channel || [NotificationChannel.EMAIL])],
      });

      queryClient.setQueryData(queries.user.notification().queryKey, {
        ...notificationData,
        type: updatedTypes,
        channel:
          currentTypes.length === 1
            ? [NotificationChannel.EMAIL]
            : [...(notificationData?.channel || [NotificationChannel.EMAIL])],
      });

      if (value) {
        showDialog('notificationComplete');
      } else {
        showDialog('notificationCancel');
      }
    } catch (error) {
      errorToast({
        title: '알림 설정에 실패하였습니다.',
        description: '다시 시도해주세요.',
      });
    }
  };

  const goToSignIn = () => {
    routerPush('/sign-in');
  };

  return {
    subscriptionDetail,
    goToApply,
    goToAttachFiles,
    attachFilesRef,
    applyStatistics,
    infoStatistics,
    isNoticeDialogVisible,
    toggleNoticeDialogVisibility,
    isNightTimeLimitVisible,
    routerPush,
    isApplyCheckLoading,
    applyLimitInfo,
    isOnboardingVisible,
    goToOnboarding,
    handleNotification,
    notification,
    isNewsletter,
    isNotificationConsentVisible,
    isNotificationCompleteVisible,
    isNotificationCancelVisible,
    isNotificationLoginVisible,
    showDialog,
    hideDialog,
    goToSignIn,
  };
};
