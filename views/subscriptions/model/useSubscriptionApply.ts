import { useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';
import { queries } from '@/features/lib/queries';
import { fetchSubscription } from '@/features/subscription/api/fetchSubscription';
import { checkNightTime } from '@/features/subscription/lib/checkNightTime';
import { useSubscriptionApplyForm } from '@/features/subscription/model/useSubscriptionApplyForm';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { applySubscription } from '@/entities/subscriptions/api/applySubscription';
import { SubscriptionApplySteps, SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useSubscriptionApply = () => {
  const searchParams = useSearchParams();
  const state = searchParams.get('state') as SubscriptionApplySteps;
  const { step, handleStep, isStep } = useStep<SubscriptionApplySteps>(
    state || SubscriptionApplySteps.QUANTITY,
  );
  const router = useRouter();
  const { id } = useParams();
  const { successToast, errorToast } = useToast();
  const { isVisible: isAgree, toggleVisibility: handleAgree } = useVisibility();
  const queryClient = useQueryClient();
  const { form, handleVerify } = useSubscriptionApplyForm();
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(false);
  const { data: subscription } = fetchSubscription(id as string);
  const { YYYYMMDD } = utilFormats();
  const { user, isExistAccount } = useFetchUser();
  const { routerPush } = useWebViewRouter();
  const { account, refetch, isLoading, isFetching } = useFetchAccount();
  const { isVisible: isNoticeDialogVisible, toggleVisibility: toggleNoticeDialogVisibility } =
    useVisibility();
  const { isVisible: isNightTimeLimitVisible, toggleVisibility: toggleNightTimeLimitVisibility } =
    useVisibility();

  useEffect(() => {
    if (state) {
      handleAgree();
    }
  }, [state]);

  const handleNext = async () => {
    if (isStep(SubscriptionApplySteps.COMPLETE)) {
      router.replace('/subscriptions');
      return;
    }
    if (isStep(SubscriptionApplySteps.CONFIRM)) {
      handleApplySubscription();
      return;
    }
    if (isStep(SubscriptionApplySteps.DEPOSIT)) {
      handleStep(SubscriptionApplySteps.CONFIRM);
      return;
    }
    if (isStep(SubscriptionApplySteps.QUANTITY)) {
      handleStep(SubscriptionApplySteps.DEPOSIT);
      return;
    }
  };

  const isDisabled = (step: SubscriptionApplySteps) => {
    switch (step) {
      case SubscriptionApplySteps.QUANTITY:
        return form.watch('quantity') === 0 || !!form.formState.errors.quantity;

      case SubscriptionApplySteps.DEPOSIT:
        if (
          (account?.currentNetBalance || 0) <
          form.getValues('quantity') * (subscription?.offeringPrice || 0)
        ) {
          return true;
        }

        return false;
      case SubscriptionApplySteps.CONFIRM:
        return !form.watch('isVerified') || isSubscriptionLoading;
      case SubscriptionApplySteps.COMPLETE:
        return false;
    }
  };

  const handleApplySubscription = async () => {
    setIsSubscriptionLoading(true);
    try {
      await applySubscription(id as string, {
        applyQuantity: form.getValues('quantity'),
        investmentAgreeAt: YYYYMMDD(new Date(), 'YYYY-MM-DD hh:mm:ss') as string,
        digitalSign: form.getValues('transactionId'),
      });
      successToast({
        title: '청약 신청 완료',
        description: '청약 신청 완료',
      });

      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queries.subscriptions._def }),
        queryClient.invalidateQueries({ queryKey: queries.assets._def }),
        queryClient.invalidateQueries({
          queryKey: queries.user._def,
        }),
        queryClient.invalidateQueries({
          queryKey: queries.investor._def,
        }),
      ]);

      handleStep(SubscriptionApplySteps.COMPLETE);
    } catch (error) {
      errorToast({
        title: '청약 신청 실패',
        description: '청약 신청 실패',
      });
    } finally {
      setIsSubscriptionLoading(false);
    }
  };

  const mobileTitle = () => {
    if (!isAgree) return '투자 위험 고지 안내';

    if (isStep(SubscriptionApplySteps.QUANTITY)) return '청약 수량 입력';

    if (isStep(SubscriptionApplySteps.DEPOSIT)) return '예치금 입금';

    if (isStep(SubscriptionApplySteps.CONFIRM)) return '청약 내역 확인';

    if (isStep(SubscriptionApplySteps.COMPLETE)) return '청약 완료';

    return '청약하기';
  };

  const mobileProgressStep = () => {
    if (!isAgree) return 1;
    if (isStep(SubscriptionApplySteps.QUANTITY)) return 1;
    if (isStep(SubscriptionApplySteps.DEPOSIT)) return 2;
    if (isStep(SubscriptionApplySteps.CONFIRM)) return 3;
    if (isStep(SubscriptionApplySteps.COMPLETE)) return 4;

    return 1;
  };

  // 청약 신청 전 필수 정보 입력 체크
  useEffect(() => {
    if (!user) return;

    const isUserVerified =
      user?.userAuthOCR &&
      // user?.investorPropensity &&
      user?.investorSuitabilityTest &&
      isExistAccount;

    if (!isUserVerified) {
      router.replace(`/subscriptions/onboarding?subscriptionId=${id}`);
    }
  }, [user, isExistAccount, id]);

  // 청약 진행중 체크
  useEffect(() => {
    if (!subscription) return;

    if (checkNightTime()) {
      toggleNightTimeLimitVisibility();
      return;
    }

    const isSubscriptionActive = subscription.bizStatus === SubscriptionBizStatus.SUB_WIP;

    if (!isSubscriptionActive) {
      toggleNoticeDialogVisibility();
    }
  }, [subscription, toggleNoticeDialogVisibility]);

  return {
    form,
    isStep,
    handleNext,
    handleVerify,
    isDisabled,
    step,
    isAgree,
    handleAgree,
    subscription,
    account,
    isLoadingMyAccount: isLoading || isFetching,
    refetchMyAccount: refetch,
    mobileTitle,
    mobileProgressStep,
    isNoticeDialogVisible,
    toggleNoticeDialogVisibility,
    routerPush,
    isNightTimeLimitVisible,
    toggleNightTimeLimitVisibility,
  };
};
