'use client';

import { OnboardingAccountRegister } from '@/widgets/subscriptions/ui/OnboardingAccountRegister';
import { OnboardingDesktopDetailProgress } from '@/widgets/subscriptions/ui/OnboardingDesktopDetailProgress';
import { OnboardingHeader } from '@/widgets/subscriptions/ui/OnboardingHeader';
import { OnboardingMobileDetailProgress } from '@/widgets/subscriptions/ui/OnboardingMobileDetailProgress';
import { OnboardingMobileHeader } from '@/widgets/subscriptions/ui/OnboardingMobileHeader';
import { OnboardingOcr } from '@/widgets/subscriptions/ui/OnboardingOcr';
import { OnboardingPropensity } from '@/widgets/subscriptions/ui/OnboardingPropensity';
import { OnboardingSuitability } from '@/widgets/subscriptions/ui/OnboardingSuitability';

import { SubscriptionOnboardingSteps } from '@/entities/subscriptions/types';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';

import { useOnboarding } from '../model/useOnboarding';

export const SubscriptionOnboardingView = () => {
  const {
    step,
    isStep,
    ocr: {
      isOcrLoading,
      isVerifiedLoading,
      form: ocrForm,
      handleFileUpload,
      fileUploaderRef,
      handleVerify,
      step: ocrStep,
      handleFileChange,
      isOcrStep,
      isErrorDialogVisible,
      toggleErrorDialog,
    },
    account: {
      step: accountStep,
      handleAccountNextStep,
      form: accountForm,
      handleVerifyAccount,
      handleCreateVirtualAccount,
      virtualAccount,
      isAgree,
      toggleAgree,
      isAccountLoading,
      isIdentityVerification,
      toggleIdentityVerification,
      handleIdentityVerification,
    },
    // propensity: {
    //   selectedType,
    //   terms,
    //   answers,
    //   handlePrev,
    //   handleSingleAnswer,
    //   handleMultiAnswer,
    //   handleMultiRatioAnswer,
    //   handleSelectedType,
    //   handleTerms,
    //   isPropensityDisabled,
    //   isStep: isPropensityStep,
    //   currentQuestion,
    //   step: propensityStep,
    //   handleStep: handlePropensityStep,
    //   propensityScore,
    // },
    suitability: {
      selectedTest,
      isSelected,
      questions,
      testIndex,
      handleSelectTest,
      handlePreviousTest,
      handleRetakeTest,
      isWrongAnswerVisible,
      toggleWrongAnswerVisibility,
      handleNextTest,
      isSuitabilityDisabled,
      suitabilityStep,
      handleSuitabilityStep,
      isSuitabilityStep,
    },
    sideStep,
    sideOptions,
    handleNextStep,
  } = useOnboarding();

  return (
    <section className="pt-16 sm:pt-0">
      <CommonMotionProvider>
        <OnboardingHeader step={step} />
        <OnboardingMobileHeader step={step} />
        <div className="h-full sm:bg-gray-50 sm:pb-[60px] sm:pt-12 ml:pt-10">
          <div className="mx-auto flex max-w-screen-lg flex-col gap-6 sm:px-8 ml:flex-row ml:gap-10 ml:px-0">
            <OnboardingDesktopDetailProgress steps={sideOptions()} currentStep={sideStep(step)} />
            <OnboardingMobileDetailProgress steps={sideOptions()} currentStep={sideStep(step)} />
            <div className="mx-auto flex w-full max-w-[950px] flex-col overflow-y-auto rounded-lg border-gray-300 bg-white px-6 sm:h-full sm:max-h-[1500px] sm:min-h-[1000px] sm:border sm:px-0 sm:pb-60">
              {isStep(SubscriptionOnboardingSteps.OCR) && (
                <OnboardingOcr
                  ocrStep={ocrStep}
                  isOcrStep={isOcrStep}
                  isOcrLoading={isOcrLoading}
                  ocrForm={ocrForm}
                  handleFileUpload={handleFileUpload}
                  handleFileChange={handleFileChange}
                  handleVerify={handleVerify}
                  isVerifiedLoading={isVerifiedLoading}
                  fileUploaderRef={fileUploaderRef}
                  handleNext={handleNextStep}
                  isErrorDialogVisible={isErrorDialogVisible}
                  toggleErrorDialog={toggleErrorDialog}
                />
              )}
              {isStep(SubscriptionOnboardingSteps.ACCOUNT) && (
                <OnboardingAccountRegister
                  accountStep={accountStep}
                  handleAccountNextStep={handleAccountNextStep}
                  accountForm={accountForm}
                  handleVerifyAccount={handleVerifyAccount}
                  handleCreateVirtualAccount={handleCreateVirtualAccount}
                  virtualAccount={virtualAccount}
                  handleNextStep={handleNextStep}
                  isAgree={isAgree}
                  toggleAgree={toggleAgree}
                  isLoading={isAccountLoading}
                  isIdentityVerification={isIdentityVerification}
                  toggleIdentityVerification={toggleIdentityVerification}
                  handleIdentityVerification={handleIdentityVerification}
                />
              )}
              {/* {isStep(SubscriptionOnboardingSteps.PROPENSITY) && (
                <OnboardingPropensity
                  propensityStep={propensityStep}
                  selectedType={selectedType}
                  onSelectedType={handleSelectedType}
                  terms={terms}
                  onTermsChange={handleTerms}
                  answers={answers}
                  currentQuestion={currentQuestion}
                  handleSingleAnswer={handleSingleAnswer}
                  handleMultiAnswer={handleMultiAnswer}
                  handleMultiRatioAnswer={handleMultiRatioAnswer}
                  handlePropensityStep={handlePropensityStep}
                  handlePrev={handlePrev}
                  handlePropensityNext={handleNextStep}
                  isPropensityDisabled={isPropensityDisabled}
                  propensityScore={propensityScore}
                  isPropensityStep={isPropensityStep}
                />
              )} */}
              {isStep(SubscriptionOnboardingSteps.SUITABILITY) && (
                <OnboardingSuitability
                  testIndex={testIndex}
                  questions={questions}
                  handleSelectTest={handleSelectTest}
                  isSelected={isSelected}
                  selectedTest={selectedTest}
                  handlePreviousTest={handlePreviousTest}
                  handleNextTest={handleNextTest}
                  isSuitabilityDisabled={isSuitabilityDisabled}
                  isWrongAnswerVisible={isWrongAnswerVisible}
                  toggleWrongAnswerVisibility={toggleWrongAnswerVisibility}
                  handleRetakeTest={handleRetakeTest}
                  isSuitabilityStep={isSuitabilityStep}
                  handleSuitabilityStep={handleSuitabilityStep}
                  goToCallbackUrl={handleNextStep}
                />
              )}
            </div>
          </div>
        </div>
      </CommonMotionProvider>
    </section>
  );
};
