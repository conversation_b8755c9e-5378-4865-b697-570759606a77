'use client';

import { SubscriptionRequestComplete } from '@/widgets/subscriptions/ui/SubscriptionRequestComplete';

import { useSubscriptionRequest } from '@/features/subscription/model/useSubscriptionRequest';
import { SubscriptionRequestForm } from '@/features/subscription/ui/SubscriptionRequestForm';

import { DepositChangeSteps } from '@/entities/assets/types';

import { ContentsTitle } from '@/shared/ui/ContentsTitle';

export const SubscriptionRequestView = () => {
  const {
    step,
    isStep,
    form,
    ref,
    onSubmit,
    handleFileUpload,
    handleUploadedFiles,
    handleDeleteFile,
    goToCallbackUrl,
  } = useSubscriptionRequest();

  return (
    <section className="mx-auto mb-20 mt-4 max-w-screen-md px-6 sm:mb-40 sm:mt-14 sm:px-8 ml:mt-[100px] ml:px-0">
      {isStep(DepositChangeSteps.FORM) && (
        <>
          <ContentsTitle
            title="투자 의뢰 신청하기"
            className="ml:text-44 sm:text-32 text-28 hidden sm:block"
          />
          <SubscriptionRequestForm
            form={form}
            ref={ref}
            onSubmit={onSubmit}
            handleFileUpload={handleFileUpload}
            handleUploadedFiles={handleUploadedFiles}
            handleDeleteFile={handleDeleteFile}
          />
        </>
      )}
      {isStep(DepositChangeSteps.COMPLETE) && (
        <SubscriptionRequestComplete goToCallbackUrl={goToCallbackUrl} />
      )}
    </section>
  );
};
