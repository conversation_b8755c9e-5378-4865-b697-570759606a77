'use client';

import { useSubscriptionApply } from '@/views/subscriptions/model/useSubscriptionApply';

import { SubscriptionApplyComplete } from '@/widgets/subscriptions/ui/SubscriptionApplyComplete';
import { SubscriptionApplyConfirm } from '@/widgets/subscriptions/ui/SubscriptionApplyConfirm';
import { SubscriptionApplyDeposit } from '@/widgets/subscriptions/ui/SubscriptionApplyDeposit';
import { SubscriptionApplyMobileProgress } from '@/widgets/subscriptions/ui/SubscriptionApplyMobileProgress';
import { SubscriptionApplyProgress } from '@/widgets/subscriptions/ui/SubscriptionApplyProgress';
import { SubscriptionApplyQuantity } from '@/widgets/subscriptions/ui/SubscriptionApplyQuantity';
import { SubscriptionRiskNotice } from '@/widgets/subscriptions/ui/SubscriptionRiskNotice';

import { SubscriptionApplySteps } from '@/entities/subscriptions/types';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

export const SubscriptionApplyView = () => {
  const {
    form,
    isStep,
    handleNext,
    handleVerify,
    isDisabled,
    step,
    isAgree,
    handleAgree,
    subscription,
    account,
    isLoadingMyAccount,
    refetchMyAccount,
    mobileProgressStep,
    isNoticeDialogVisible,
    isNightTimeLimitVisible,
    routerPush,
  } = useSubscriptionApply();

  if (!isAgree) {
    return <SubscriptionRiskNotice handleAgree={handleAgree} subscription={subscription} />;
  }

  return (
    <section>
      {/* 안내 widget 연결필요 */}
      {/* <SubscriptionApplyPropensityNotice /> */}

      <div className="mx-auto flex max-w-screen-contents flex-col justify-between sm:h-auto sm:max-w-screen-test sm:justify-start">
        <div>
          <h2 className="text-40 mt-[100px] hidden text-center sm:block">청약하기</h2>
          <SubscriptionApplyProgress currentStep={step} />
          <SubscriptionApplyMobileProgress currentStep={step} />
          {isStep(SubscriptionApplySteps.QUANTITY) && (
            <SubscriptionApplyQuantity
              form={form}
              subscription={subscription}
              mobileProgressStep={mobileProgressStep}
            />
          )}
          {isStep(SubscriptionApplySteps.DEPOSIT) && (
            <SubscriptionApplyDeposit
              form={form}
              subscription={subscription}
              account={account}
              isLoadingMyAccount={isLoadingMyAccount}
              refetchMyAccount={refetchMyAccount}
              mobileProgressStep={mobileProgressStep}
            />
          )}
          {isStep(SubscriptionApplySteps.CONFIRM) && (
            <SubscriptionApplyConfirm
              handleVerify={handleVerify}
              subscription={subscription}
              form={form}
              mobileProgressStep={mobileProgressStep}
            />
          )}
          {isStep(SubscriptionApplySteps.COMPLETE) && (
            <SubscriptionApplyComplete
              form={form}
              subscription={subscription}
              mobileProgressStep={mobileProgressStep}
            />
          )}
        </div>
        <div className="fixed bottom-0 left-0 right-0 bg-white px-6 py-2 sm:static sm:my-[100px] sm:px-8 sm:pb-0 sm:pt-0 ml:px-0">
          <PrimaryButton
            onClick={handleNext}
            disabled={isDisabled(step)}
            className="w-full sm:!h-[60px]"
            text={isStep(SubscriptionApplySteps.COMPLETE) ? '확인' : '다음'}
          />
        </div>
      </div>
      <ConfirmDialog
        isOpen={isNoticeDialogVisible}
        handleOpen={() => routerPush('/subscriptions')}
        title="진행중인 청약이 아닙니다."
        handleAction={() => routerPush('/subscriptions')}
        isCancelButton={false}
      />
      <ConfirmDialog
        isOpen={isNightTimeLimitVisible}
        isCancelButton={false}
        title={`현재는 청약 신청 가능 시간이 아닙니다. \n
          시간을 확인하여 다시 참여해 주세요. \n (청약 가능 시간: 04:30 ~ 23:30)`}
        handleOpen={() => routerPush('/subscriptions')}
        handleAction={() => routerPush('/subscriptions')}
      />
    </section>
  );
};
