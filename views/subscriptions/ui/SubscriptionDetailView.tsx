'use client';

import { BellIcon, ShareIcon } from '@heroicons/react/24/outline';

import { SubscriptionDetailCompany } from '@/widgets/subscriptions/ui/SubscriptionDetailCompany';
import { SubscriptionDetailCondition } from '@/widgets/subscriptions/ui/SubscriptionDetailCondition';
import SubscriptionDetailHeader from '@/widgets/subscriptions/ui/SubscriptionDetailHeader';
import { SubscriptionNotice } from '@/widgets/subscriptions/ui/SubscriptionNotice';
import { TabDisclousures } from '@/widgets/subscriptions/ui/TabDisclousures';
import { TabInquiry } from '@/widgets/subscriptions/ui/TabInquiry';
import { TabNews } from '@/widgets/subscriptions/ui/TabNews';
import { TabSubscriptionInfo } from '@/widgets/subscriptions/ui/TabSubscriptionInfo';

import { fetchSubscriptionCmsDetail } from '@/features/subscription/api/fetchSubscriptionCmsDetail';
import { SubscriptionApplyNoticeDialog } from '@/features/subscription/ui/SubscriptionApplyNoticeDialog';
import { SubscriptionDetailTabsList } from '@/features/subscription/ui/SubscriptionDetailTabsList';

import { calculateProgress } from '@/entities/subscriptions/lib/calculateProgress';
import { Subscription, SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { NotificationType } from '@/shared/types';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Separator } from '@/shared/ui/shadcn/separator';
import { Tabs } from '@/shared/ui/shadcn/tabs';

import { useSubscriptionDetail } from '../model/useSubscriptionDetail';

interface SubscriptionDetailViewProps {
  subscription: Subscription;
}

export const SubscriptionDetailView = ({ subscription }: SubscriptionDetailViewProps) => {
  const {
    subscriptionDetail,
    goToApply,
    applyStatistics,
    infoStatistics,
    goToAttachFiles,
    attachFilesRef,
    isNoticeDialogVisible,
    toggleNoticeDialogVisibility,
    isNightTimeLimitVisible,
    routerPush,
    isApplyCheckLoading,
    applyLimitInfo,
    isOnboardingVisible,
    toggleOnboardingVisibility,
    goToOnboarding,
    isNewsletter,
    handleNotification,
  } = useSubscriptionDetail(subscription);

  const { YYYYMMDD } = utilFormats();

  const progress = calculateProgress(
    infoStatistics?.offeringTotalAmount ?? 0,
    applyStatistics?.applyTotalMargin ?? 0,
  );

  const isEnded = !(
    subscription?.bizStatus === SubscriptionBizStatus.SUB_WAIT ||
    subscription?.bizStatus === SubscriptionBizStatus.SUB_WIP
  );

  const isWait = subscription?.bizStatus === SubscriptionBizStatus.SUB_WAIT;

  const { data: subscriptionCmsDetail, isLoading } = fetchSubscriptionCmsDetail(
    subscription.securities.securitiesId,
  );

  return (
    <section className="pb-12 sm:pb-0">
      <SubscriptionDetailHeader
        progress={progress}
        subscription={subscriptionDetail}
        goToApply={toggleNoticeDialogVisibility}
        applyTotalMargin={applyStatistics?.applyTotalMargin}
        isEnded={isEnded}
        isWait={isWait}
        isLoading={isApplyCheckLoading}
      />
      <Separator className="mt-6 h-3 bg-blue-gray-00 sm:hidden" orientation="horizontal" />
      <SubscriptionNotice className="mx-auto my-6 max-w-[800px] px-6 sm:my-8 sm:!mb-14 sm:!mt-10 sm:px-8 ml:max-w-screen-contents ml:px-0" />
      <SubscriptionDetailCompany issuer={subscription.issuer} />
      <Separator className="mb-7 mt-3 h-3 bg-blue-gray-00 sm:hidden" orientation="horizontal" />
      <SubscriptionDetailCondition subscription={subscriptionDetail} />
      <Tabs defaultValue="info">
        <SubscriptionDetailTabsList goToAttachFiles={goToAttachFiles} subscription={subscription} />

        <TabSubscriptionInfo
          securitiesId={subscription.securities.securitiesId}
          attachFilesRef={attachFilesRef}
          subscriptionCmsDetail={subscriptionCmsDetail}
          isLoading={isLoading}
          requiredDocs={subscription.securities.requiredDocsDetail}
        />

        <TabInquiry
          securitiesId={subscription.securities.securitiesId}
          subscription={subscriptionDetail}
        />
        <TabNews securitiesId={subscription.securities.securitiesId} />
        <TabDisclousures securitiesId={subscription.securities.securitiesId} />
      </Tabs>
      <div className="fixed bottom-0 left-0 z-10 flex w-full items-center gap-2 bg-white px-6 py-2 sm:hidden">
        {isWait ? (
          <Button
            className="!h-12 w-full bg-gray-900 text-base font-semibold text-white"
            onClick={() => handleNotification(NotificationType.NEWSLETTER)}
          >
            <BellIcon className="h-6 w-6 text-white" />{' '}
            {isNewsletter ? '알림 신청 완료' : '알림 신청하기'}
          </Button>
        ) : (
          <PrimaryButton
            onClick={toggleNoticeDialogVisibility}
            text={isEnded ? '투자종료' : '투자하기'}
            disabled={isEnded || isApplyCheckLoading}
            className="!h-12 w-full flex-1 text-base font-semibold"
          />
        )}

        <Button className="!h-12 w-12 rounded-lg border border-gray-300">
          <ShareIcon />
        </Button>
      </div>
      <SubscriptionApplyNoticeDialog
        isOpen={isNoticeDialogVisible}
        handleOpen={toggleNoticeDialogVisibility}
        handleAction={goToApply}
      />
      <ConfirmDialog
        isOpen={isNightTimeLimitVisible}
        isCancelButton={false}
        title={`현재는 청약 신청 가능 시간이 아닙니다. \n
          시간을 확인하여 다시 참여해 주세요. \n (청약 가능 시간: 04:30 ~ 23:30)`}
        handleOpen={() => routerPush('/subscriptions')}
        handleAction={() => routerPush('/subscriptions')}
      />
      <ConfirmDialog
        isOpen={applyLimitInfo.isCountLimit}
        isCancelButton={false}
        title={`24시간 기준 청약 신청 및 취소 횟수가 \n모두 소진되었습니다. \n ${YYYYMMDD(applyLimitInfo.remainingTime, 'YYYY년 MM월 DD일 HH:mm')} 이후에 진행해 주세요.`}
        handleOpen={() => routerPush('/subscriptions')}
        handleAction={() => routerPush('/subscriptions')}
      />
      <ConfirmDialog
        isOpen={isOnboardingVisible}
        isCancelButton
        title="투자 서비스 절차 진행"
        description={`투자 서비스 이용을 위한 절차 진행이 필요합니다. \n 진행하시겠습니까?`}
        handleOpen={toggleOnboardingVisibility}
        handleAction={goToOnboarding}
      />
    </section>
  );
};
