'use client';

import { SubscriptionHeroBanner } from '@/widgets/subscriptions/ui/SubscriptionHeroBanner';
import { SubscriptionList } from '@/widgets/subscriptions/ui/SubscriptionList';
import { SubscriptionNotice } from '@/widgets/subscriptions/ui/SubscriptionNotice';

import { SubscriptionSearchForm } from '@/features/subscription/ui/SubscriptionSearchForm';

import { Banner } from '@/entities/banners/types';
import { SubscriptionsResponse } from '@/entities/subscriptions/interface';
import { SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { ListResponse } from '@/shared/interface';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionViewProps {
  mainBanners: ListResponse<Banner>;
  subBanners: ListResponse<Banner>;
  subscriptionsWIP: SubscriptionsResponse;
  subscriptionsWait: SubscriptionsResponse;
  subscriptionsDone: SubscriptionsResponse;
}

export const SubscriptionView = ({
  mainBanners,
  subBanners,
  subscriptionsWIP,
  subscriptionsWait,
  subscriptionsDone,
}: SubscriptionViewProps) => {
  return (
    <section className="mb-40 sm:mb-40">
      <SubscriptionHeroBanner banners={mainBanners} />
      <SubscriptionNotice className="container" />
      <SubscriptionSearchForm />
      <SubscriptionList
        title="진행중인 투자"
        status={SubscriptionBizStatus.SUB_WIP}
        placeholder="현재 진행중인 상품이 없습니다. 조금만 기다려 주세요."
        subscriptions={subscriptionsWIP}
        isSort
      />
      <Separator className="mt-16 h-3 bg-blue-gray-00 sm:h-2" orientation="horizontal" />

      <div className="my-16 sm:my-[100px] sm:space-y-8">
        <SubscriptionList
          title="오픈 예정"
          status={SubscriptionBizStatus.SUB_WAIT}
          placeholder="현재 오픈 예정인 상품이 없습니다. 조금만 더 기다려 주세요."
          subscriptions={subscriptionsWait}
        />
      </div>

      <div className="mx-auto max-w-screen-lg px-0 sm:px-8 ml:px-10">
        <div className="relative mb-16 aspect-[17/4] sm:mb-[100px] ml:aspect-[15/2]">
          <FallbackImage
            src={subBanners?.data[0]?.banner_items[0].image.url}
            alt="subscription_banner"
            fill
            style={{ objectFit: 'cover' }}
            quality={100}
            className="sm:rounded-[20px]"
            priority
            sizes="100vw, (min-width: 1440px) 1200px"
          />
        </div>
      </div>
      {subscriptionsDone.totalCount > 0 && (
        <SubscriptionList
          title="종료된 투자"
          status={SubscriptionBizStatus.SUB_DONE}
          placeholder={`현재 종료된 상품이 없습니다. 조금만 더 기다려 주세요.`}
          subscriptions={subscriptionsDone}
          isMore
        />
      )}
    </section>
  );
};
