'use client';

import React from 'react';

import { useOcr } from '@/features/ocr/model/useOcr';
import { OcrSteps } from '@/features/ocr/type';
import { OcrForm } from '@/features/ocr/ui/OcrForm';
import { OcrMobileForm } from '@/features/ocr/ui/OcrMobileForm';
import { OcrMobileGuide } from '@/features/ocr/ui/OcrMobileGuide';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { TestComplete } from '@/shared/ui/TestComplete';

export const OcrView = () => {
  const {
    isOcrLoading,
    isVerifiedLoading,
    form,
    handleFileUpload,
    fileUploaderRef,
    handleVerify,
    handleFileChange,
    goToCallbackUrl,
    isOcrStep,
    isApp,
    isErrorDialogVisible,
    toggleErrorDialog,
    isAccountRegisterDialogVisible,
    goToAccountRegister,
    handleAccountRegisterDialog,
  } = useOcr();

  return (
    <>
      {isApp ? (
        <section className="sm:hidden sm:pb-40">
          {isOcrStep(OcrSteps.UPLOAD) && (
            <OcrMobileGuide
              isOcrLoading={isOcrLoading}
              form={form}
              handleFileUpload={handleFileUpload}
              handleFileChange={handleFileChange}
              handleVerify={handleVerify}
              isVerifiedLoading={isVerifiedLoading}
              fileUploaderRef={fileUploaderRef}
            />
          )}
          {isOcrStep(OcrSteps.VERIFY) && (
            <OcrMobileForm
              isOcrLoading={isOcrLoading}
              form={form}
              handleFileUpload={handleFileUpload}
              handleFileChange={handleFileChange}
              handleVerify={handleVerify}
              isVerifiedLoading={isVerifiedLoading}
              fileUploaderRef={fileUploaderRef}
            />
          )}
          {isOcrStep(OcrSteps.COMPLETE) && (
            <TestComplete
              title="본인 실명인증 완료"
              description="본인 실명인증이 완료되었습니다."
              goToCallbackUrl={handleAccountRegisterDialog}
            />
          )}
        </section>
      ) : (
        <>
          <section className="px-6 sm:px-8 ml:px-0">
            {(isOcrStep(OcrSteps.UPLOAD) || isOcrStep(OcrSteps.VERIFY)) && (
              <OcrForm
                isOcrLoading={isOcrLoading}
                form={form}
                handleFileUpload={handleFileUpload}
                handleFileChange={handleFileChange}
                handleVerify={handleVerify}
                isVerifiedLoading={isVerifiedLoading}
                fileUploaderRef={fileUploaderRef}
              />
            )}
            {isOcrStep(OcrSteps.COMPLETE) && (
              <TestComplete
                title="본인 실명인증 완료"
                description="본인 실명인증이 완료되었습니다."
                goToCallbackUrl={handleAccountRegisterDialog}
              />
            )}
          </section>
        </>
      )}
      <ConfirmDialog
        isOpen={isErrorDialogVisible}
        handleOpen={toggleErrorDialog}
        title={`유효한 신분증이 아닙니다. \n 이미지를 다시 확인해 주세요.`}
        handleAction={() => {
          toggleErrorDialog();
          form.reset();
        }}
        isCancelButton={false}
      />
      <ConfirmDialog
        isOpen={isAccountRegisterDialogVisible}
        handleOpen={goToCallbackUrl}
        title={`예치금 가상계좌 개설 안내`}
        description={`투자 서비스를 위한 예치금 가상계좌 개설이 필요합니다. \n 개설하시겠습니까?`}
        handleAction={goToAccountRegister}
        isCancelButton
      />
    </>
  );
};
