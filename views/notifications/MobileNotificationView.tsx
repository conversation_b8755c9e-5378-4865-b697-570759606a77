'use client';

import React, { useEffect } from 'react';

import { useNotification } from '@/features/notifications/model/useNotification';
import { NotificationList } from '@/features/notifications/ui/NotificationList';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { Loading } from '@/shared/ui/Loading';

export const MobileNotificationView = () => {
  const { user } = useFetchUser();
  const { notifications, ref, isLoading, unreadCount, handleReadAll } = useNotification(
    user?.id as string,
  );

  useEffect(() => {
    if (unreadCount > 0) {
      handleReadAll();
    }
  }, [unreadCount, handleReadAll]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <section>
      <NotificationList notifications={notifications} ref={ref} isMobile />
    </section>
  );
};
