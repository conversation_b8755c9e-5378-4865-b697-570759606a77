'use client';

import React from 'react';

import { InquiryMobileCard } from '@/widgets/inquiries/ui/InquiryMobileCard';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { EmptyList } from '@/shared/ui/EmptyList';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { ScrollArea } from '@/shared/ui/shadcn/scroll-area';

import { useMyInquiries } from '../model/useMyInquiries';

export const MobileInquiryHistoryView = () => {
  const {
    inquiryList,
    ref,
    hasNextPage,
    isDeleteVisible,
    toggleDeleteVisibility,
    handleDeleteInquiry,
    handleOpenDeleteDialog,
  } = useMyInquiries();

  const { routerPush } = useWebViewRouter();

  return (
    <section className="h-mobile-non-gnb px-6">
      {inquiryList?.pages && inquiryList?.pages[0].data?.length > 0 ? (
        <ScrollArea className="max-h-[calc(100dvh-200px)] overflow-y-auto py-8">
          {inquiryList?.pages.map((page) =>
            page.data.map((inquiry) => (
              <InquiryMobileCard
                key={inquiry.id}
                inquiry={inquiry}
                handleOpenDeleteDialog={handleOpenDeleteDialog}
              />
            )),
          )}
        </ScrollArea>
      ) : (
        <EmptyList
          title="내역이 없습니다."
          href="/mobile/user/support/inquiry"
          className="!h-[calc(100dvh-120px)]"
        />
      )}
      {hasNextPage && <div ref={ref} />}

      <ConfirmDialog
        isOpen={isDeleteVisible}
        handleAction={handleDeleteInquiry}
        handleOpen={toggleDeleteVisibility}
        title="해당 문의를 삭제하시겠습니까?"
        description="삭제하신 내용은 복구하실 수 없습니다."
      />

      <div className="fixed bottom-0 left-0 w-full px-6 py-2 sm:hidden">
        <PrimaryButton
          text="문의하기"
          className="test-base !h-12 w-full"
          onClick={() => {
            routerPush('/mobile/user/support/inquiry');
          }}
        />
      </div>
    </section>
  );
};
