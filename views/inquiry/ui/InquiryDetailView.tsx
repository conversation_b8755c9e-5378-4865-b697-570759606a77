'use client';

import { Separator } from '@radix-ui/react-separator';
import { useParams } from 'next/navigation';
import React from 'react';

import { InquiryDetailAnswer } from '@/widgets/inquiries/ui/InquiryDetailAnswer';
import { InquiryDetailHeader } from '@/widgets/inquiries/ui/InquiryDetailHeader';

import { fetchMyInquiryDetail } from '@/features/inquiries/api/fetchMyInquiryDetail';

import { AttachFileList } from '@/shared/ui/AttachFileList';
import { BackButton } from '@/shared/ui/BackButton';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';

export const InquiryDetailView = () => {
  const params = useParams();

  const { data: inquiryDetail } = fetchMyInquiryDetail(params.id as string);

  return (
    <div className="my-10 px-6 sm:mx-auto sm:my-14 sm:w-full sm:max-w-[800px] sm:px-8 ml:my-0 ml:max-w-full ml:flex-1 ml:px-0">
      <ContentsTitle title="문의 내역" className="text-28 mb-9 hidden ml:block" />
      <section>
        <InquiryDetailHeader inquiryDetail={inquiryDetail} />
        <Separator className="my-10 h-[1px] bg-gray-300 sm:my-12" />
        <p className="text-sm sm:text-base">{inquiryDetail?.content}</p>

        {inquiryDetail?.attachFile?.length > 0 && (
          <>
            <Separator className="my-10 h-[1px] bg-gray-300 sm:my-12" />
            <AttachFileList attachFiles={inquiryDetail?.attachFile} />
          </>
        )}

        {inquiryDetail?.answer && <InquiryDetailAnswer inquiryDetail={inquiryDetail} />}

        <div className="mt-20 flex justify-center">
          <BackButton href="/user/support/inquiry-history" className="hidden sm:block" />
          <BackButton href="/mobile/user/support/inquiry-history" className="block sm:hidden" />
        </div>
      </section>
    </div>
  );
};
