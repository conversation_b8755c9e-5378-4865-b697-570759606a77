'use client';

import { InquiryForm } from '@/features/inquiries/ui/InquiryForm';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';

import { useInquiryVerify } from '../model/useInquiryVerify';

export const InquiryView = () => {
  const { isConfirmDialog, goToSignIn, goToBack } = useInquiryVerify();

  return (
    <section className="mx-auto my-10 max-w-screen-md px-6 sm:my-14 sm:px-8 ml:my-[100px] ml:px-0">
      <div className="mb-[72px] space-y-6 sm:space-y-8 ml:mb-[120px] ml:space-y-10">
        <ContentsTitle title="문의하기" className="text-28 sm:text-32 ml:text-44" />
        <h4 className="text-base font-bold sm:text-xl">
          궁금한 사항이 있으신가요? <br />
          문의하실 내용을 남겨주세요. 가능한 신속히 답변 드리겠습니다.
        </h4>
      </div>
      <InquiryForm support />
      <ConfirmDialog
        isOpen={isConfirmDialog}
        handleOpen={goToBack}
        handleAction={goToSignIn}
        title={`로그인이 필요한 서비스 입니다. \n 로그인 하시겠습니까?`}
      />
    </section>
  );
};
