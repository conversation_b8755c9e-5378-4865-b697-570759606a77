'use client';

import { InquiryCard } from '@/widgets/inquiries/ui/InquiryCard';
import { InquiryListColumn } from '@/widgets/inquiries/ui/InquiryListColumn';
import { InquiryMobileCard } from '@/widgets/inquiries/ui/InquiryMobileCard';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { EmptyList } from '@/shared/ui/EmptyList';

import { useMyInquiries } from '../model/useMyInquiries';

export const MyInquiriesView = () => {
  const {
    inquiryList,
    ref,
    hasNextPage,
    isDeleteVisible,
    toggleDeleteVisibility,
    handleDeleteInquiry,
    handleOpenDeleteDialog,
  } = useMyInquiries();

  return (
    <div className="px-6 sm:mx-auto sm:w-full sm:max-w-[800px] sm:px-8 ml:max-w-full ml:flex-1 ml:px-0">
      <ContentsTitle title="문의 내역" className="text-28 mb-9 hidden ml:block" />
      <section className="hidden sm:block">
        <InquiryListColumn />
        {inquiryList?.pages && inquiryList?.pages[0].data?.length > 0 ? (
          inquiryList?.pages.map((page) =>
            page.data.map((inquiry) => (
              <InquiryCard
                key={inquiry.id}
                inquiry={inquiry}
                handleOpenDeleteDialog={handleOpenDeleteDialog}
              />
            )),
          )
        ) : (
          <EmptyList
            title="내역이 없습니다."
            buttonText="문의하기"
            className="mt-40"
            href="/user/support/inquiry"
          />
        )}
        {hasNextPage && <div ref={ref} />}
      </section>
      <section className="block sm:hidden">
        {inquiryList?.pages && inquiryList?.pages[0].data?.length > 0 ? (
          inquiryList?.pages.map((page) =>
            page.data.map((inquiry) => (
              <InquiryMobileCard
                key={inquiry.id}
                inquiry={inquiry}
                handleOpenDeleteDialog={handleOpenDeleteDialog}
              />
            )),
          )
        ) : (
          <EmptyList title="내역이 없습니다." buttonText="문의하기" />
        )}
        {hasNextPage && <div ref={ref} />}
      </section>
      <ConfirmDialog
        isOpen={isDeleteVisible}
        handleAction={handleDeleteInquiry}
        handleOpen={toggleDeleteVisibility}
        title="해당 문의를 삭제하시겠습니까?"
        description="삭제하신 내용은 복구하실 수 없습니다."
      />
    </div>
  );
};
