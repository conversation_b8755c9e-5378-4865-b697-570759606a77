'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

import { fetchInfiniteMyInquiries } from '@/features/inquiries/api/fetchInfiniteMyInquiries';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { deleteInquiry } from '@/entities/inquiries/api/deleteInquiry';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

export const useMyInquiries = () => {
  const { userId } = useFetchUser();
  const queryClient = useQueryClient();
  const { successToast, errorToast } = useToast();
  const [inquiryId, setInquiryId] = useState<string | null>(null);
  const { isVisible: isDeleteVisible, toggleVisibility: toggleDeleteVisibility } = useVisibility();

  const {
    data: inquiryList,
    fetchNextPage,
    hasNextPage,
  } = fetchInfiniteMyInquiries(
    {
      page: 1,
      perPage: 10,
    },
    userId as string,
  );

  const [ref, inView] = useInView({
    delay: 100,
    threshold: 0.5,
  });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage]);

  const handleDeleteInquiry = async () => {
    if (!inquiryId) return;

    try {
      await deleteInquiry(inquiryId);
      successToast({
        title: '문의 삭제 성공',
      });
      queryClient.invalidateQueries({ queryKey: queries.inquiries._def });
      setInquiryId(null);
      toggleDeleteVisibility();
    } catch (error) {
      errorToast({
        title: '문의 삭제 실패',
      });
    }
  };

  const handleOpenDeleteDialog = (inquiryId: string) => {
    setInquiryId(inquiryId);
    toggleDeleteVisibility();
  };

  return {
    inquiryList,
    ref,
    hasNextPage,
    isDeleteVisible,
    toggleDeleteVisibility,
    handleDeleteInquiry,
    handleOpenDeleteDialog,
  };
};
