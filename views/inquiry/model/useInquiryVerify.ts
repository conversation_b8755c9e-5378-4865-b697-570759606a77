'use client';

import Cookies from 'js-cookie';
import { signOut, useSession } from 'next-auth/react';
import { useEffect } from 'react';

import { getUserStatusCheck } from '@/entities/users/api/getUserStatusCheck';

import { useVisibility } from '@/shared/model/useVisibility';

export const useInquiryVerify = () => {
  const { data: session, status } = useSession();
  const { isVisible: isConfirmDialog, openToggle: openConfirmDialog } = useVisibility();

  const goToSignIn = async () => {
    const expires = new Date(new Date().getTime() + 5 * 60 * 1000);

    Cookies.set('callback-url', '/supports/inquiry', {
      path: '/',
      expires,
    });
    await signOut({ redirectTo: '/sign-in' });
  };

  const goToBack = async () => {
    await signOut({ redirectTo: '/' });
  };

  useEffect(() => {
    if (status === 'unauthenticated') {
      openConfirmDialog();

      return;
    }
    if (session?.user.accessToken) {
      getUserStatusCheck(session?.user.accessToken)
        .then((res) => {})
        .catch(() => {
          openConfirmDialog();
        });
    }
  }, [session]);

  return {
    isConfirmDialog,
    goToSignIn,
    goToBack,
  };
};
