import { useSession } from 'next-auth/react';

import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';

import { UserRole } from '@/shared/types';

const useMyAsset = () => {
  const { data: session } = useSession();
  const isInvestor = session && session?.user.role !== UserRole.USR;
  const { account } = useFetchAccount();

  return {
    isInvestor,
    account,
  };
};

export default useMyAsset;
