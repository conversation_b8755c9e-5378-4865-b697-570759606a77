'use client';

import { SubscriptionApplicationList } from '@/widgets/subscriptions/ui/SubscriptionApplicationList';

import { SubscriptionAssetCategoryTabs } from '@/features/subscription/config';

import { AssetDetailItem } from '@/entities/assets/ui/AssetDetailItem';
import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { Separator } from '@/shared/ui/shadcn/separator';

import useMyAsset from '../model/useMyAsset';

export const MyAssetView = () => {
  const { isInvestor, account } = useMyAsset();

  const { CASHCOMMA } = utilFormats();

  return (
    <div className="flex-1 px-6 sm:px-8">
      <ContentsTitle title="자산/수익" className="text-28 mb-10 hidden ml:block" />
      <div className="space-y-14">
        <section className="space-y-3 sm:space-y-5">
          <div className="w-full space-y-3 sm:space-y-5">
            <div className="flex flex-col justify-between space-y-3 sm:flex-row sm:items-center sm:space-y-0">
              <h4 className="sm:text-20 text-base font-semibold">내 보유자산</h4>
              {/* <CommonSelect
                placeholder="전체"
                value="crowd"
                onChange={() => {}}
                options={CategoryTabs}
              /> */}
            </div>
            <div className="flex w-full flex-col gap-[10px] rounded-[10px] border border-gray-300 bg-gray-50 p-5 sm:flex-row sm:gap-1 sm:p-8">
              <AssetDetailItem
                title="총 청약 금액"
                value={`${CASHCOMMA(account?.currentBalance || 0)} 원`}
              />
              <Separator orientation="vertical" className="mx-8 hidden h-16 bg-gray-200 sm:block" />
              <AssetDetailItem
                title="보유 예치금"
                value={`${CASHCOMMA(account?.currentNetBalance || 0)} 원`}
              />
              <Separator orientation="vertical" className="mx-8 hidden h-16 bg-gray-200 sm:block" />
              <AssetDetailItem
                title="청약 증거금"
                value={`${CASHCOMMA(account?.currentNetBalance || 0)} 원`}
              />
            </div>
          </div>

          <SubscriptionApplicationList
            status={[SubscriptionApplyStatusList.ALLOT]}
            isInvestor={!!isInvestor}
            isSelect
            isAsset
            categoryTabs={SubscriptionAssetCategoryTabs}
          />
        </section>
      </div>
    </div>
  );
};
