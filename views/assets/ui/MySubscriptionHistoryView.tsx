'use client';

import { useSession } from 'next-auth/react';

import { SubscriptionApplicationList } from '@/widgets/subscriptions/ui/SubscriptionApplicationList';

import {
  SubscriptionCurrentCategoryTabs,
  SubscriptionHistoryCategoryTabs,
} from '@/features/subscription/config';

import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';

import { UserRole } from '@/shared/types';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

export const MySubscriptionHistoryView = () => {
  const { data: session } = useSession();

  const isInvestor = session && session?.user.role !== UserRole.USR;

  return (
    <div className="flex-1 px-6 sm:px-8 ml:px-0">
      <ContentsTitle title="청약 신청 내역" className="text-28 mb-10 hidden ml:block" />
      <Tabs defaultValue="current">
        {/* tablet & desktop */}
        <TabsList className={`mb-12 hidden w-full grid-cols-2 text-gray-300 sm:grid`}>
          <TabsTrigger
            value="current"
            className="h-[50px] rounded-none border-b border-gray-300 text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
          >
            청약 신청 현황
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="h-[50px] rounded-none border-b border-gray-300 text-[17px] font-bold shadow-none data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:text-gray-900 sm:text-xl"
          >
            청약 이력
          </TabsTrigger>
        </TabsList>
        {/* mobile */}
        <TabsList
          className={`mb-6 grid h-[43px] w-full grid-cols-2 rounded-[26px] bg-gray-100 p-[3px] text-gray-400 sm:hidden`}
        >
          <TabsTrigger
            value="current"
            className="h-full rounded-[26px] text-sm font-semibold shadow-none data-[state=active]:bg-white data-[state=active]:text-gray-900"
          >
            청약 신청 현황
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="h-full rounded-[26px] text-sm font-semibold shadow-none data-[state=active]:bg-white data-[state=active]:text-gray-900"
          >
            청약 이력
          </TabsTrigger>
        </TabsList>
        <TabsContent value="current">
          <SubscriptionApplicationList
            isAsset={false}
            isSelect
            status={[
              SubscriptionApplyStatusList.APPLY,
              SubscriptionApplyStatusList.WAIT,
              SubscriptionApplyStatusList.ALLOT_TARGET,
            ]}
            isInvestor={!!isInvestor}
            categoryTabs={SubscriptionCurrentCategoryTabs}
          />
        </TabsContent>
        <TabsContent value="history">
          <SubscriptionApplicationList
            isAsset={false}
            isSelect
            status={[
              SubscriptionApplyStatusList.WITHDRAW,
              SubscriptionApplyStatusList.REFUND_WAIT,
              SubscriptionApplyStatusList.ALLOT,
              SubscriptionApplyStatusList.NOT_ALLOT,
            ]}
            categoryTabs={SubscriptionHistoryCategoryTabs}
            isInvestor={!!isInvestor}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
