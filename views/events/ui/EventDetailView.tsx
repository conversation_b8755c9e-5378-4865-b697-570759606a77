'use client';

import 'ckeditor5/ckeditor5.css';
import React from 'react';

import { CommentList } from '@/widgets/comments/ui/CommentList';
import { EventDetailHeader } from '@/widgets/events/ui/EventDetailHeader';

import { fetchEvent } from '@/features/events/api/fetchEvent';

import { Event, EventStatus } from '@/entities/events/types';

import { BackButton } from '@/shared/ui/BackButton';
import { Separator } from '@/shared/ui/shadcn/separator';

interface Props {
  event: Event;
}

export const EventDetailView = ({ event }: Props) => {
  const { data: eventDetail } = fetchEvent(event.id, event);

  return (
    <section className="container mt-10 pb-40 sm:mt-[100px]">
      <EventDetailHeader eventDetail={eventDetail} />
      <Separator className="mt-8 bg-gray-300 sm:mt-[72px]" />
      <div className="hidden sm:block">
        <div
          className="ck-content mb-[72px] mt-10 sm:mb-[88px] sm:mt-[88px]"
          dangerouslySetInnerHTML={{ __html: eventDetail?.content ?? '' }}
        />
      </div>
      <div className="block sm:hidden">
        <div
          className="ck-content mb-[72px] mt-10 sm:mb-[88px] sm:mt-[88px]"
          dangerouslySetInnerHTML={{ __html: eventDetail?.mobileContent ?? '' }}
        />
      </div>

      {eventDetail.enableComment && (
        <CommentList
          isDone={eventDetail.eventStatus === EventStatus.DONE}
          isEnableCommentCount={eventDetail.enableCommentCount}
          className="mb-[72px] space-y-14 sm:mb-[88px]"
        />
      )}

      <div className="flex justify-center">
        <BackButton />
      </div>
    </section>
  );
};
