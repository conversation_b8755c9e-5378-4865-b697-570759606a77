'use client';

import React, { Suspense } from 'react';

import { EventList } from '@/widgets/events/ui/EventList';
import { EventListSkeleton } from '@/widgets/events/ui/EventListSkeleton';

import { ContentsTitle } from '@/shared/ui/ContentsTitle';

const EventsView = () => {
  return (
    <section className="container mt-10 pb-20 sm:mt-14 ml:mt-[100px]">
      <ContentsTitle
        title="이벤트"
        className="text-28 sm:text-32 ml:text-44 mb-10 hidden sm:mb-12 sm:block ml:mb-20"
      />
      <Suspense fallback={<EventListSkeleton />}>
        <EventList />
      </Suspense>
    </section>
  );
};

export default EventsView;
