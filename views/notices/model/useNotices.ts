'use client';

import { useSearchParams } from 'next/navigation';

import { fetchNotices } from '@/features/notices/api/fetchNotices';

export const useNotices = () => {
  const searchParams = useSearchParams();
  const page = Number(searchParams.get('page')) || 1;
  const searchType = searchParams.get('searchType') || 'title';
  const keyword = searchParams.get('keyword') || '';
  const createdAt = searchParams.get('createdAt') || '';

  const { data } = fetchNotices({
    page,
    perPage: 10,
    title: searchType === 'title' ? keyword : undefined,
    content: searchType === 'content' ? keyword : undefined,
    createdAt: createdAt ? createdAt : undefined,
  });

  return { data, page };
};
