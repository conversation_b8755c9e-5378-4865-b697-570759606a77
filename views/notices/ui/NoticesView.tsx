'use client';

import { NoticeDesktopList } from '@/widgets/notices/ui/NoticeDesktopList';
import { NoticeMobileList } from '@/widgets/notices/ui/NoticeMobileList';

import { defaultSearchOptions } from '@/shared/config/searchOptions';
import { CmsCommonSearchForm } from '@/shared/ui/CmsCommonSearchForm';
import { ContentsTitle } from '@/shared/ui/ContentsTitle';

import { useNotices } from '../model/useNotices';

export const NoticesView = () => {
  const { data, page } = useNotices();

  return (
    <section className="container mt-10 pb-20 sm:mt-14 ml:mt-[100px]">
      <ContentsTitle
        title="공지사항"
        className="text-28 sm:text-32 ml:text-44 mb-10 sm:mb-12 ml:mb-20"
      />
      <CmsCommonSearchForm searchOptions={defaultSearchOptions} />
      <NoticeDesktopList data={data} />
      <NoticeMobileList data={data} page={Number(page)} />
    </section>
  );
};
