'use client';

import { Separator } from '@radix-ui/react-separator';
import 'ckeditor5/ckeditor5.css';
import React from 'react';

import { NoticeDetailHeader } from '@/widgets/notices/ui/NoticeDetailHeader';

import { fetchNoticeDetail } from '@/features/notices/api/fetchNoticeDetail';

import { Notice } from '@/entities/notices/types';

import { AttachFileList } from '@/shared/ui/AttachFileList';
import { BackButton } from '@/shared/ui/BackButton';

interface NoticeDetailViewProps {
  notice: Notice;
}

export const NoticeDetailView = ({ notice }: NoticeDetailViewProps) => {
  const { data: noticeDetail } = fetchNoticeDetail(notice.id, notice);

  return (
    <section className="container mt-10 pb-40 sm:mt-14 ml:mt-[100px]">
      <NoticeDetailHeader noticeDetail={noticeDetail} />
      <Separator className="mb-10 mt-10 h-[1px] bg-gray-300 ml:mb-0 ml:mt-[88px]" />
      <div
        className="ck-content ml:my-[88px]"
        dangerouslySetInnerHTML={{ __html: noticeDetail?.content }}
      />
      <Separator className="my-10 h-[1px] bg-gray-300 ml:my-[88px]" />
      {noticeDetail?.attachFile && (
        <>
          <AttachFileList attachFiles={noticeDetail?.attachFile} />
          <Separator className="mt-10 h-[1px] bg-gray-300" />
        </>
      )}

      <div className="mt-[72px] flex justify-center ml:mt-[88px]">
        <BackButton />
      </div>
    </section>
  );
};
