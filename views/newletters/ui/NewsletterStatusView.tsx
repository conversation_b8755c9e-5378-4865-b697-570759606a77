'use client';

import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import React from 'react';

import { withdrawNewsletter } from '@/entities/users/api/withdrawNewsletter';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { CompleteWidget } from '@/shared/ui/CompleteWidget';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

export const NewsletterStatusView = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const status = searchParams.get('status') || 'complete';
  const isComplete = status === 'complete';
  const { errorToast } = useToast();

  const { isVisible, toggleVisibility } = useVisibility();

  const titleBefore = isComplete ? '소식 구독이' : '소식 구독을';
  const titleHighlight = isComplete ? ' 완료' : '';
  const titleAfter = isComplete ? '되었습니다.' : ' 해지하시겠습니까?';
  const description = isComplete
    ? '소식 구독이 모두 완료되었습니다. \n 알차고 실속있는 서비스로 찾아뵙겠습니다.'
    : '구독 해지 버튼을 클릭하면 구독이 해지됩니다. \n 중요 공지는 수신 동의 여부와 상관없이 발송됩니다.';

  const imageSrc = isComplete ? '/icons/complete.png' : '/icons/withdraw.png';

  const handleWithdraw = async () => {
    if (!email) {
      throw new Error('이메일이 없습니다.');
    }

    try {
      await withdrawNewsletter(email);
      toggleVisibility();
    } catch (error) {
      console.error(error);
      errorToast({
        title: '구독해지에 실패했습니다.',
      });
    }
  };

  const { routerPush } = useWebViewRouter();

  return (
    <section>
      <div className="flex h-dvh flex-col items-center justify-center">
        <button onClick={() => routerPush('/')}>
          <FallbackImage
            src="/logo/numit_logo.png"
            alt="numit logo"
            width={144}
            height={34}
            className="mb-[50px] hidden sm:block"
          />
        </button>
        <CompleteWidget
          titleBefore={titleBefore}
          titleHighlight={titleHighlight}
          titleAfter={titleAfter}
          description={description}
          imageSrc={imageSrc}
        >
          {isComplete ? (
            <div className="mt-16 flex items-center gap-2">
              <SecondaryButton
                text="메인 화면으로"
                className="w-full"
                onClick={() => router.push('/')}
              />
              <PrimaryButton
                onClick={() => router.push('/sign-up')}
                text="회원가입하기"
                className="w-full"
              />
            </div>
          ) : (
            <div className="mt-16 flex items-center gap-2">
              <SecondaryButton text="취소" className="w-full" onClick={() => router.push('/')} />
              <PrimaryButton onClick={handleWithdraw} text="구독 해지" className="w-full" />
            </div>
          )}
        </CompleteWidget>
      </div>
      <ConfirmDialog
        isOpen={isVisible}
        handleAction={() => router.push('/')}
        handleOpen={toggleVisibility}
        title="구독해지가 완료되었습니다."
        isCancelButton={false}
      />
    </section>
  );
};
