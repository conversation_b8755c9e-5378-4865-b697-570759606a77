'use client';

import React from 'react';

import { CurationDetailContents } from '@/widgets/curations/ui/CurationDetailContents';
import { CurationDetailHeader } from '@/widgets/curations/ui/CurationDetailHeader';
import { DetailRecommendContents } from '@/widgets/curations/ui/DetailRecommendContents';

import { fetchCuration } from '@/features/curations/api/fetchCuration';

import { Curation } from '@/entities/curations/types';
import { CurationTagBadge } from '@/entities/curations/ui/CurationTagBadge';

import { Separator } from '@/shared/ui/shadcn/separator';

interface CurationDetailViewProps {
  curation: Curation;
}

export const CurationDetailView = ({ curation }: CurationDetailViewProps) => {
  const { data: curationDetail } = fetchCuration(curation.id, curation);

  return (
    <section className="container mt-10 space-y-[72px] pb-40 sm:mt-[100px]">
      <CurationDetailHeader curationDetail={curationDetail} />

      <CurationDetailContents curationDetail={curationDetail} />
      <div className="mt-20 flex gap-[6px]">
        {curation.tags?.split(',').map((tag) => <CurationTagBadge key={tag} tag={tag} />)}
      </div>
      {curation.relatedContents && (
        <>
          <Separator className="my-[100px] bg-gray-300 ml:my-[140px]" />
          <DetailRecommendContents curationDetail={curationDetail} />
        </>
      )}
    </section>
  );
};
