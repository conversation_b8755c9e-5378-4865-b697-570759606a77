'use client';

import { Suspense } from 'react';

import { CurationList } from '@/widgets/curations/ui/CurationList';
import { CurationListSkeleton } from '@/widgets/curations/ui/CurationListSkeleton';

import { CurationSearchForm } from '@/features/curations/ui/CurationSearchForm';

import { ContentsTitle } from '@/shared/ui/ContentsTitle';

export const CurationsView = () => {
  return (
    <section className="container mb-40 mt-10 sm:mb-40 sm:mt-14 ml:mt-[100px]">
      <div className="mb-10 flex items-center justify-between sm:mb-3 ml:mb-[84px]">
        <ContentsTitle title="콘텐츠" className="ml:text-44 sm:text-32 text-28 hidden sm:block" />
        <CurationSearchForm />
      </div>

      <Suspense fallback={<CurationListSkeleton />}>
        <CurationList />
      </Suspense>
    </section>
  );
};
