'use client';

import { useSearchParams } from 'next/navigation';
import React, { memo, useMemo } from 'react';

import { DisclosureDesktopList } from '@/widgets/disclosures/ui/DisclosureDesktopList';
import { DisclosureMobileList } from '@/widgets/disclosures/ui/DisclosureMobileList';

import { DisclosureSearchForm } from '@/features/disclosures/ui/DisclosureSearchForm';

import { ContentsTitle } from '@/shared/ui/ContentsTitle';

import { useDisclosures } from '../model/useDisclosures';

const DisclosuresViewComponent = () => {
  const searchParams = useSearchParams();
  const disclosureId = searchParams.get('disclosureId');

  // disclosureId가 변경될 때만 useDisclosures를 호출
  const { data, page } = useDisclosures();

  // 컴포넌트의 렌더링 결과를 메모이제이션
  const content = useMemo(
    () => (
      <section className="container mt-10 pb-20 sm:mt-14 md:mt-[100px]">
        <ContentsTitle
          title="공시"
          className="md:text-44 text-28 sm:text-32 mb-5 sm:mb-8 ml:mb-12"
        />
        <DisclosureSearchForm />
        <DisclosureDesktopList data={data} />
        <DisclosureMobileList data={data} page={Number(page)} />
      </section>
    ),
    [data, page],
  );

  return content;
};

// disclosureId가 변경될 때만 리렌더링되도록 memo의 비교 함수를 커스터마이즈
export const DisclosuresView = memo(DisclosuresViewComponent, () => {
  const searchParams = useSearchParams();
  const disclosureId = searchParams.get('disclosureId');

  // disclosureId가 변경될 때만 리렌더링
  return false; // 항상 리렌더링하도록 설정
});
