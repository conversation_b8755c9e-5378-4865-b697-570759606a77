'use client';

import { useSession } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';

import { fetchDisclosure } from '@/features/disclosures/api/fetchDisclosure';
import { fetchDisclosures } from '@/features/disclosures/api/fetchDisclosures';

import { DisclosureCategory } from '@/entities/disclosures/types';
import { SecuritiesType } from '@/entities/subscriptions/types';

export const useDisclosures = ({ securitiesId }: { securitiesId: string }) => {
  const searchParams = useSearchParams();
  const createdAt = searchParams.get('createdAt');
  const keyword = searchParams.get('keyword');
  const issuer = searchParams.get('issuer');
  const securitiesType = searchParams.get('securitiesType') as SecuritiesType | 'ALL';
  const category = searchParams.get('category') as DisclosureCategory | 'ALL';
  const page = searchParams.get('page') || 1;
  const disclosureId = searchParams.get('disclosureId');
  const { data: session } = useSession();

  const { data } = fetchDisclosures({
    page: Number(page),
    createdAt: createdAt || undefined,
    securitiesName: keyword || undefined,
    issuer: issuer || undefined,
    securitiesType: securitiesType === 'ALL' ? undefined : securitiesType,
    category: category === 'ALL' ? undefined : category,
    perPage: 10,
    securitiesId,
  });

  const { data: disclosure, isLoading: isDisclosureLoading } = fetchDisclosure(
    disclosureId || '',
    !!disclosureId,
  );

  return {
    data,
    page: Number(page),
    disclosure,
    disclosureId,
    isDisclosureLoading,
    session,
  };
};
