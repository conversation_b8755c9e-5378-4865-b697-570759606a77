'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';
import { useEffect } from 'react';

import { InvestorProgress } from '@/widgets/investor/ui/InvestorProgress';
import { InvestorPropensityNotice } from '@/widgets/investor/ui/InvestorPropensityNotice';
import { InvestorPropensityTerms } from '@/widgets/investor/ui/InvestorPropensityTerms';
import { PropensityQuestion } from '@/widgets/investor/ui/PropensityQuestion';
import { PropensityResult } from '@/widgets/investor/ui/PropensityResult';
import { PropensityResultHeader } from '@/widgets/investor/ui/PropensityResultHeader';
import { TestNotice } from '@/widgets/investor/ui/TestNotice';

import { propensityNotices } from '@/features/investor/config';
import { useInvestorPropensity } from '@/features/investor/model/useInvestorPropensity';
import { PropensityType } from '@/features/investor/ui/PropensityType';

import { PropensitySteps } from '@/entities/investor/types';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { HeaderMode, ScreenSize } from '@/shared/types';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

const InvestorPropensityView = () => {
  const {
    isDisabled,
    propensityScore,
    selectedType,
    handleSelectedType,
    isStep,
    handleNext,
    currentQuestion,
    terms,
    handleTerms,
    answers,
    handleSingleAnswer,
    handleMultiAnswer,
    handleMultiRatioAnswer,
    handlePrev,
    handleStep,
    isOnboarding,
  } = useInvestorPropensity({ onboarding: false });

  usePageHeader({ mode: HeaderMode.LIST });
  usePageGNB({ isGNBVisible: false });

  const isFirstStep = isStep(PropensitySteps.TYPE);
  const isLastStep = isStep(PropensitySteps.RESULT);
  const isQuestionStep = isStep(PropensitySteps.QUESTION);

  const { screenSize } = useScreenStore();

  useEffect(() => {
    if (screenSize !== ScreenSize.MOBILE || isOnboarding) return;

    const header = document.querySelector('header');

    if (header) {
      if (isStep(PropensitySteps.NOTICE)) {
        header.style.display = 'block';
      } else {
        header.style.display = 'none';
      }
    }
  }, [isStep]);

  if (isStep(PropensitySteps.NOTICE)) {
    return (
      <TestNotice
        title="투자 성향 진단"
        description={`자본시장법에 따라 투자 서비스 전 투자 성향 진단이 필요합니다. \n 절차에 따라 진행해 주세요.`}
        notices={propensityNotices}
        onStartTest={() => handleStep(PropensitySteps.TYPE)}
      />
    );
  }

  return (
    <section className="sm:min-h-auto flex min-h-mobile-non-gnb flex-col justify-between sm:justify-start sm:pb-0">
      <div>
        {!isOnboarding && (
          <div className="flex justify-end px-6 sm:hidden">
            <button onClick={() => handleStep(PropensitySteps.NOTICE)}>
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        )}
        {isLastStep ? (
          <PropensityResultHeader propensityScore={propensityScore} />
        ) : (
          <div className="mx-auto max-w-screen-test space-y-5 px-6 sm:mt-[100px] sm:px-8 ml:px-0">
            <h2 className="text-40 hidden sm:block">투자 성향 진단</h2>
            <InvestorProgress isStep={isStep} currentQuestion={currentQuestion} />
          </div>
        )}
        {isFirstStep && (
          <PropensityType selectedType={selectedType} onSelectedType={handleSelectedType} />
        )}
        {isStep(PropensitySteps.TERMS) && (
          <InvestorPropensityTerms terms={terms} onTermsChange={handleTerms} />
        )}

        {isQuestionStep && (
          <PropensityQuestion
            currentQuestion={currentQuestion}
            answers={answers}
            handleSingleAnswer={handleSingleAnswer}
            handleMultiRatioAnswer={handleMultiRatioAnswer}
            handleMultiAnswer={handleMultiAnswer}
          />
        )}

        {isStep(PropensitySteps.RESULT) && <PropensityResult answers={answers} terms={terms} />}

        <div className="mx-auto mb-20 mt-5 hidden max-w-screen-test gap-2 px-6 sm:flex sm:px-8 ml:px-0">
          <SecondaryButton
            onClick={handlePrev}
            text={isFirstStep ? '진단 그만하기' : isLastStep ? '재진단 하기' : '이전'}
            className="!h-[60px] w-full"
          />
          <PrimaryButton
            onClick={handleNext}
            disabled={isDisabled()}
            text={isFirstStep ? '진단 시작하기' : isLastStep ? '완료하기' : '다음'}
            className="!h-[60px] w-full"
          />
        </div>
        {isFirstStep && <InvestorPropensityNotice />}
      </div>

      <div
        className={`mt-5 flex w-full gap-2 px-6 sm:hidden ${(isFirstStep || isStep(PropensitySteps.RESULT)) && 'mb-20 sm:pb-0'}`}
      >
        <SecondaryButton
          onClick={handlePrev}
          text={isFirstStep ? '진단 그만하기' : isLastStep ? '재진단 하기' : '이전'}
          className="w-full sm:!h-[60px]"
        />
        <PrimaryButton
          onClick={handleNext}
          disabled={isDisabled()}
          text={isFirstStep ? '진단 시작하기' : isLastStep ? '완료하기' : '다음'}
          className="w-full sm:!h-[60px]"
        />
      </div>
    </section>
  );
};

export default InvestorPropensityView;
