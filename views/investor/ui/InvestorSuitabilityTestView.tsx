'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect } from 'react';

import { InvestorSuitabilityTestStatusBar } from '@/widgets/investor/ui/InvestorSuitabilityTestStatusBar';
import { TestNotice } from '@/widgets/investor/ui/TestNotice';

import { suitabilityNotices } from '@/features/investor/config';
import { useInvestorSuitability } from '@/features/investor/model/useInvestorSuitability';
import { SuitabilityTestForm } from '@/features/investor/ui/SuitabilityTestForm';

import { InvestorSuitabilityTestStep } from '@/entities/investor/types';

import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { Spinner } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { TestComplete } from '@/shared/ui/TestComplete';

export const InvestorSuitabilityTestView = () => {
  const {
    selectedTest,
    isSelected,
    questions,
    testIndex,
    handleSelectTest,
    handlePreviousTest,
    handleRetakeTest,
    isWrongAnswerVisible,
    toggleWrongAnswerVisibility,
    handleNextTest,
    isDisabled,
    isLoadingSuitabilityTest,
    isStep,
    handleStep,
    goToCallbackUrl,
    isOnboarding,
  } = useInvestorSuitability();

  const { screenSize } = useScreenStore();

  useEffect(() => {
    if (screenSize !== ScreenSize.MOBILE || isOnboarding) return;

    const header = document.querySelector('header');
    const main = document.querySelector('main');

    if (header) {
      if (isStep(InvestorSuitabilityTestStep.NOTICE)) {
        header.style.display = 'block';
      } else {
        header.style.display = 'none';
      }
    }

    if (main) {
      if (isStep(InvestorSuitabilityTestStep.NOTICE)) {
        main.style.marginTop = '50px';
      } else {
        main.style.marginTop = '0px';
      }
    }
  }, [isStep]);

  if (isStep(InvestorSuitabilityTestStep.NOTICE)) {
    return (
      <TestNotice
        title="투자 적합성 테스트"
        description={`자본시장법에 따라 투자 서비스 전 투자 적합성 테스트가 필요합니다. \n 절차에 따라 진행해 주세요.`}
        onStartTest={() => handleStep(InvestorSuitabilityTestStep.QUESTION)}
        notices={suitabilityNotices}
      />
    );
  }

  if (isStep(InvestorSuitabilityTestStep.RESULT)) {
    return (
      <TestComplete
        title="투자 적합성 테스트 완료"
        description={`투자 적합성 테스트를 완료 했습니다. \n 지금 바로 투자 서비스를 진행해 보세요.`}
        goToCallbackUrl={goToCallbackUrl}
      />
    );
  }

  return (
    // h-mobile-non-gnb
    <section className="flex flex-col justify-between px-6 sm:h-auto sm:justify-start sm:px-8 ml:px-0">
      <div>
        {!isOnboarding && (
          <div className="flex h-[50px] items-center justify-end pt-2 sm:hidden">
            <button onClick={() => handleStep(InvestorSuitabilityTestStep.NOTICE)}>
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        )}
        <h2 className="text-40 mx-auto mt-[100px] hidden w-full max-w-screen-test sm:block">
          투자 적합성 테스트
        </h2>
        <InvestorSuitabilityTestStatusBar testIndex={testIndex} totalQuestions={questions.length} />
        <AnimatePresence mode="wait">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="mx-auto w-full max-w-screen-test"
          >
            {isLoadingSuitabilityTest ? (
              <div className="flex h-full w-full items-center justify-center">
                <Spinner />
              </div>
            ) : (
              <SuitabilityTestForm
                question={questions[testIndex]?.questionText}
                options={questions[testIndex]?.options}
                onSelect={(answer) =>
                  handleSelectTest({
                    questionNumber: questions[testIndex]?.questionNumber,
                    answer,
                  })
                }
                isSelected={isSelected}
                selectedTest={selectedTest.find(
                  (test) => test.questionNumber === questions[testIndex]?.questionNumber,
                )}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
      <div className="mx-auto mt-5 hidden w-full max-w-screen-test gap-2 sm:mb-40 sm:flex">
        <SecondaryButton
          onClick={handlePreviousTest}
          text={testIndex === 0 ? '테스트 그만하기' : '이전 문제'}
          className="w-full sm:!h-[60px]"
        />
        <PrimaryButton
          onClick={handleNextTest}
          disabled={isDisabled}
          text={testIndex === questions.length - 1 ? '결과 확인하기' : '다음 문제'}
          className="w-full sm:!h-[60px]"
        />
      </div>
      <div className="fixed bottom-0 left-0 right-0 flex w-full max-w-screen-test gap-2 bg-white px-6 py-2 sm:hidden">
        <SecondaryButton
          onClick={handlePreviousTest}
          text={testIndex === 0 ? '테스트 그만하기' : '이전 문제'}
          className="h-12 w-full text-base sm:!h-[60px]"
        />
        <PrimaryButton
          onClick={handleNextTest}
          disabled={isDisabled}
          text={testIndex === questions.length - 1 ? '결과 확인하기' : '다음 문제'}
          className="h-12 w-full text-base sm:!h-[60px]"
        />
      </div>
      <ConfirmDialog
        isOpen={isWrongAnswerVisible}
        handleOpen={toggleWrongAnswerVisibility}
        title={`${questions.length}개의 오답이 있습니다. \n 정답을 다시 확인해 주세요.`}
        handleAction={handleRetakeTest}
        text="확인"
        isCancelButton={false}
      />
    </section>
  );
};
