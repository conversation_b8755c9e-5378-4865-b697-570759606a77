import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';

import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { KycStatus } from '@/entities/users/types';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { HeaderMode, UserRole } from '@/shared/types';

import { completeOcrVerification } from '../api/completeOcrVerification';
import { Vertex } from '../interface';
import { extractBirthDate } from '../lib/extractBirthDate';
import { maskPolygonAreas } from '../lib/maskPolygonAreas';
import { OcrFormData, ocrSchema } from '../lib/ocrSchema';
import { parseOcrResult } from '../lib/parseOcrResult';
import { OcrDocumentResult, OcrSteps } from '../type';

declare global {
  interface Window {
    ReactNativeWebView?: {
      postMessage: (message: string) => void;
    };
  }
}

export const useOcr = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isOcrLoading, setIsOcrLoading] = useState(false);
  const [isVerifiedLoading, setIsVerifiedLoading] = useState(false);
  const [documentResult, setDocumentResult] = useState<OcrDocumentResult | null>(null);
  const { successToast, errorToast } = useToast();
  const fileUploaderRef = useRef<HTMLInputElement>(null);
  const { step, handleStep, isStep } = useStep<OcrSteps>(OcrSteps.UPLOAD);
  const searchParams = useSearchParams();
  const router = useRouter();
  const callbackUrl = searchParams.get('callbackUrl');
  const { isApp } = useWebViewRouter();
  const queryClient = useQueryClient();
  const { user, isExistAccount } = useFetchUser();
  const { data, update } = useSession();
  const { isVisible: isErrorDialogVisible, toggleVisibility: toggleErrorDialog } = useVisibility();
  const {
    isVisible: isAccountRegisterDialogVisible,
    toggleVisibility: toggleAccountRegisterDialog,
  } = useVisibility();

  const form = useForm<OcrFormData>({
    resolver: zodResolver(ocrSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const handleFileUpload = async () => {
    if (isApp) {
      // React Native WebView로 메시지 전송
      try {
        const result: any = await WebViewMessage('openCamera', {});

        if (result) {
          if (result.type === 'cameraResult' && result.photo?.base64Image) {
            // Base64 문자열을 File 객체로 변환
            const base64Data = result.photo.base64Image;
            const byteString = atob(base64Data.split(',')[1]);
            const ab = new ArrayBuffer(byteString.length);
            const ia = new Uint8Array(ab);

            for (let i = 0; i < byteString.length; i++) {
              ia[i] = byteString.charCodeAt(i);
            }

            const blob = new Blob([ab], { type: 'image/jpeg' });
            const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });

            setFile(file);
            handleOcrProcess(file);
          }
        }
      } catch (error) {
        console.error('웹뷰 메시지 처리 중 오류 발생:', error);
      }
    } else {
      fileUploaderRef.current?.click();
    }
  };

  const handleOcrProcess = async (file: File) => {
    setIsOcrLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const originalImageUrl = URL.createObjectURL(file);

      const docResult = await fetch('/api/ocr/document', {
        method: 'POST',
        body: formData,
      }).then((res) => res.json());

      if (!docResult.requestId) {
        errorToast({
          title: docResult.message,
        });
        return;
      }

      if (!docResult.images[0].idCard.result.isConfident) {
        toggleErrorDialog();
        // errorToast({
        //   title: docResult.images[0].verifyValidateMessage,
        // });
        return;
      }

      const type = docResult.images[0].inferDetailType;

      let vertices: Vertex[][] = [];

      if (type === 'IC') {
        vertices.push(docResult.images[0].idCard.result.ic.personalNum[0].maskingPolys[0].vertices);
      } else if (type === 'DL') {
        vertices.push(docResult.images[0].idCard.result.dl.personalNum[0].maskingPolys[0].vertices);
        vertices.push(docResult.images[0].idCard.result.dl.num[0].maskingPolys[0].vertices);
      }

      setDocumentResult(docResult);

      const payload = parseOcrResult(docResult);

      if (type === 'IC') {
        form.setValue('name', payload.name);
        form.setValue('personalNum', payload.personalNum);
        form.setValue('type', 'ic');
        form.setValue('issueDate', payload.issueDate);
      } else if (type === 'DL') {
        form.setValue('name', payload.name);
        form.setValue('personalNum', payload.personalNum);
        form.setValue('type', 'dl');
        form.setValue('num', payload.num);
        form.setValue('code', payload.code);
        form.setValue('skipCodeCheck', payload.skipCodeCheck);
      }

      handleStep(OcrSteps.VERIFY);

      // 이미지 마스킹 처리
      const maskedUrl = await maskPolygonAreas(originalImageUrl, vertices);
      form.setValue('maskedImageUrl', maskedUrl);
    } catch (error) {
      toggleErrorDialog();
      // errorToast({
      //   title: '인증에 실패했습니다.',
      //   description: error instanceof Error ? error.message : '인증에 실패했습니다.',
      // });
    } finally {
      setIsOcrLoading(false);
    }
  };

  const handleVerify = async () => {
    setIsVerifiedLoading(true);

    if (user?.kycStatus === KycStatus.NON_KYC) {
      errorToast({
        title: '본인인증이 필요합니다.',
      });
      return;
    }

    try {
      const { verifyResult } = await fetch('/api/ocr/verify', {
        method: 'POST',
        body: JSON.stringify(form.getValues()),
      }).then((res) => res.json());

      if (verifyResult.result !== 'SUCCESS') {
        throw new Error(verifyResult.message);
      }

      if (documentResult) {
        let ocrInfo = '';
        if (documentResult.images[0].inferDetailType === 'IC') {
          ocrInfo = JSON.stringify({
            requestId: verifyResult.requestId,
            uid: verifyResult.uid,
            timestamp: verifyResult.timestamp,
            images: [
              {
                name: documentResult.images[0].name,
                message: 'Success',
                inferDetailResource: documentResult.images[0].inferDetailResource,
                inferDetailType: documentResult.images[0].inferDetailType,
                inferResult: verifyResult.result,
                idCard: {
                  uid: documentResult.uid,
                  idtype: documentResult.images[0].idCard.result.idtype,
                  isConfident: documentResult.images[0].idCard.result.isConfident,
                  address: documentResult.images[0].idCard.result.ic?.address[0].text,
                  authority: documentResult.images[0].idCard.result.ic?.authority[0].text,
                  issueDate: documentResult.images[0].idCard.result.ic?.issueDate[0].text,
                  name: documentResult.images[0].idCard.result.ic?.name[0].text,
                  personalNum: documentResult.images[0].idCard.result.ic?.personalNum[0].text,
                },
              },
            ],
          });
        } else if (documentResult.images[0].inferDetailType === 'DL') {
          ocrInfo = JSON.stringify({
            requestId: verifyResult.requestId,
            uid: verifyResult.uid,
            timestamp: verifyResult.timestamp,
            images: [
              {
                name: documentResult.images[0].name,
                message: 'Success',
                inferDetailResource: documentResult.images[0].inferDetailResource,
                inferDetailType: documentResult.images[0].inferDetailType,
                inferResult: verifyResult.result,
                idCard: {
                  uid: documentResult.uid,
                  idtype: documentResult.images[0].idCard.result.idtype,
                  isConfident: documentResult.images[0].idCard.result.isConfident,
                  address: documentResult.images[0].idCard.result.dl?.address[0].text,
                  authority: documentResult.images[0].idCard.result.dl?.authority[0].text,
                  issueDate: documentResult.images[0].idCard.result.dl?.issueDate[0].text,
                  name: documentResult.images[0].idCard.result.dl?.name[0].text,
                  personalNum: documentResult.images[0].idCard.result.dl?.personalNum[0].text,
                },
              },
            ],
          });
        }

        await completeOcrVerification({
          ocrInfo,
          registeredAt: verifyResult.timestamp,
          name: form.getValues('name'),
          birthDate: extractBirthDate(form.getValues('personalNum')),
        });

        // OCR 인증 후 가상계좌 개설이 되어있다면 투자자 전환
        if (data?.user.role === UserRole.USR && isExistAccount) {
          await update({
            ...data,
          });
        }
      }
      await queryClient.invalidateQueries({
        queryKey: queries.user._def,
      });

      successToast({
        title: '인증에 성공했습니다.',
      });

      handleStep(OcrSteps.COMPLETE);
    } catch (error) {
      toggleErrorDialog();
      // errorToast({
      //   title: '인증에 실패했습니다.',
      //   description: error instanceof Error ? error.message : '인증에 실패했습니다.',
      // });
    } finally {
      setIsVerifiedLoading(false);
    }
  };

  const handleFileChange = (files: File[]) => {
    const selectedFile = files[0];
    if (selectedFile) {
      setFile(selectedFile);
      handleOcrProcess(selectedFile);
    }
  };

  const handleAccountRegisterDialog = () => {
    if (callbackUrl?.includes('/my/investment-settings') && !isExistAccount) {
      toggleAccountRegisterDialog();
      return;
    }

    goToCallbackUrl();
  };

  const goToAccountRegister = () => {
    router.replace(`/deposit/account-register?callbackUrl=${callbackUrl}`);
  };

  const goToCallbackUrl = () => {
    router.replace(callbackUrl ?? '/');
  };

  const getHeaderConfig = () => {
    if (isStep(OcrSteps.VERIFY) || isStep(OcrSteps.COMPLETE)) {
      return { mode: HeaderMode.LIST, subTitle: '신분증 확인' };
    }
    return { mode: HeaderMode.LIST, subTitle: '본인 실명인증' };
  };

  usePageHeader(getHeaderConfig());

  return {
    file,
    isOcrLoading,
    isVerifiedLoading,
    form,
    handleFileUpload,
    fileUploaderRef,
    handleVerify,
    step,
    handleFileChange,
    goToCallbackUrl,
    isOcrStep: isStep,
    isApp,
    isErrorDialogVisible,
    toggleErrorDialog,
    isAccountRegisterDialogVisible,
    toggleAccountRegisterDialog,
    handleAccountRegisterDialog,
    goToAccountRegister,
  };
};
