export const extractBirthDate = (personalNum: string) => {
  // 주민번호 형식: 901201-1234567 또는 9012011234567
  const cleanJumin = personalNum.replace(/[^0-9]/g, '');

  if (cleanJumin.length !== 13) {
    throw new Error('올바른 주민등록번호 형식이 아닙니다.');
  }

  const yy = cleanJumin.slice(0, 2);
  const mm = cleanJumin.slice(2, 4);
  const dd = cleanJumin.slice(4, 6);
  const genderCode = cleanJumin[6];

  let century;
  switch (genderCode) {
    case '1':
    case '2':
    case '5':
    case '6':
      century = '19';
      break;
    case '3':
    case '4':
    case '7':
    case '8':
      century = '20';
      break;
    case '9':
    case '0':
      century = '18';
      break;
    default:
      throw new Error('알 수 없는 성별 코드입니다.');
  }

  const fullYear = century + yy;
  return `${fullYear}-${mm}-${dd}`;
};
