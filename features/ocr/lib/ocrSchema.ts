import { z } from 'zod';

export const ocrSchema = z.object({
  maskedImageUrl: z.string().min(1, { message: '이미지를 업로드해주세요.' }),
  name: z.string().min(1),
  personalNum: z.string().min(1),
  type: z.enum(['ic', 'dl']),
  num: z.string().optional(),
  issueDate: z.string().optional(),
  code: z.string().optional(),
  skipCodeCheck: z.boolean().optional(),
});

export type OcrFormData = z.infer<typeof ocrSchema>;
