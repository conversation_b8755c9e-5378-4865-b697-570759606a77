import { OcrResult } from '../interface';

export const parseOcrResult = (result: any): OcrResult => {
  const { inferDetailType, idCard } = result.images[0];

  if (inferDetailType === 'IC') {
    const { formatted } = idCard.result.ic.issueDate[0];
    return {
      type: 'ic',
      name: idCard.result.ic.name[0].text,
      personalNum: idCard.result.ic.personalNum[0].text,
      issueDate: `${formatted.year} ${formatted.month} ${formatted.day}`,
    };
  }

  if (inferDetailType === 'DL') {
    return {
      type: 'dl',
      name: idCard.result.dl.name[0].text,
      personalNum: idCard.result.dl.personalNum[0].text,
      num: idCard.result.dl.num[0].text,
      code: idCard.result.dl.code[0].text.slice(0, 6),
      skipCodeCheck: true,
    };
  }

  throw new Error('Unsupported ID card type');
};
