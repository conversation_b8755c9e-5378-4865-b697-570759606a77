import { Vertex } from '../interface';

export const maskPolygonAreas = async (imageUrl: string, polygons: Vertex[][]): Promise<string> => {
  const img = new Image();
  img.src = imageUrl;

  await new Promise((resolve) => {
    img.onload = resolve;
  });

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return imageUrl;

  // 캔버스 크기를 이미지 크기로 설정
  canvas.width = img.width;
  canvas.height = img.height;

  // 원본 이미지 그리기
  ctx.drawImage(img, 0, 0);

  // 마스킹 영역들 그리기
  ctx.fillStyle = 'black';

  polygons.forEach((vertices) => {
    if (vertices.length < 3) return; // 최소한 3개의 점이 있어야 다각형이 됨

    ctx.beginPath();
    ctx.moveTo(vertices[0].x, vertices[0].y);

    for (let i = 1; i < vertices.length; i++) {
      ctx.lineTo(vertices[i].x, vertices[i].y);
    }

    ctx.closePath();
    ctx.fill();
  });

  return canvas.toDataURL();
};
