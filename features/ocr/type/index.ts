export type OcrDocumentResult = {
  requestId: string;
  timestamp: number;
  uid: string;
  images: {
    idCard: {
      meta: {
        estimatedLanguage: string;
      };
      result: OcrResult;
    };
    inferDetailResource: string;
    inferDetailType: string;
    inferResult: string;
    isGrayScale: boolean;
    message: string;
    name: string;
    uid: string;
  }[];
};

interface DocumentFields {
  address: {
    text: string;
  }[];
  authority: {
    text: string;
  }[];
  issueDate: {
    text: string;
  }[];
  name: {
    text: string;
  }[];
  personalNum: {
    text: string;
  }[];
}

interface OcrResult {
  idtype: string;
  isConfident: boolean;
  ic?: DocumentFields;
  dl?: DocumentFields;
}

export enum OcrSteps {
  UPLOAD = 'UPLOAD',
  VERIFY = 'VERIFY',
  COMPLETE = 'COMPLETE',
}
