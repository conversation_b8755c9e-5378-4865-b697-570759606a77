import env from '@/shared/lib/env.schema';

export const ocrVerify = async (data: Record<string, any>) => {
  const response = await fetch(`${env.OCR_API_URL}/id-card/verify/${data.type}`, {
    method: 'POST',
    body: JSON.stringify({
      requestId: crypto.randomUUID(),
      data,
    }),
    headers: {
      'X-EKYC-SECRET': env.OCR_SECRET_KEY,
      'Content-Type': 'application/json',
    },
  });

  return response.json();
};
