import env from '@/shared/lib/env.schema';

export const ocrDocumentRecognition = async (file: File) => {
  const response = await fetch(`${env.OCR_API_URL}/id-card/document`, {
    method: 'POST',
    body: JSON.stringify({
      requestId: crypto.randomUUID(),
      timestamp: Math.floor(Date.now() / 1000),
      images: [
        {
          format: file.type.split('/')[1],
          name: file.name,
          data: Buffer.from(await file.arrayBuffer()).toString('base64'),
        },
      ],
    }),
    headers: {
      'X-EKYC-SECRET': env.OCR_SECRET_KEY,
      'Content-Type': 'application/json',
    },
  });
  return response.json();
};
