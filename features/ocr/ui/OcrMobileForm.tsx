import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { hidePersonalNum } from '@/features/ocr/lib/hidePersonalNum';

import { usePageGNB } from '@/shared/model/layouts/usePageGNB';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { FileUploader } from '@/shared/ui/FileUploader';
import { InputField } from '@/shared/ui/InputField';
import { Spinner } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';

import { OcrFormData } from '../lib/ocrSchema';

interface OcrMobileFormProps {
  isOcrLoading: boolean;
  form: UseFormReturn<OcrFormData>;
  handleFileUpload: () => void;
  handleFileChange: (files: File[]) => void;
  handleVerify: () => void;
  isVerifiedLoading: boolean;
  fileUploaderRef: React.RefObject<HTMLInputElement | null>;
  isOnboarding?: boolean;
  className?: string;
}

export const OcrMobileForm = ({
  isOcrLoading,
  form,
  handleFileUpload,
  handleFileChange,
  handleVerify,
  isVerifiedLoading,
  fileUploaderRef,
  isOnboarding = false,
  className,
}: OcrMobileFormProps) => {
  const { watch, control, getValues, reset } = form;

  const ocrType = watch('type') === 'ic' ? '주민등록증' : '운전면허증';

  usePageHeader({ mode: HeaderMode.LIST, subTitle: '신분증확인' });

  const maskedImageUrl = watch('maskedImageUrl');

  return (
    <CommonMotionProvider
      className={`mx-auto flex ${isOnboarding ? 'h-[calc(100dvh-110px)]' : 'px-6'} max-w-screen-test flex-col justify-between py-6 ${className}`}
    >
      <div>
        <div className="space-y-3">
          <h2 data-testid="ocr-title" className="text-20">
            신분증 정보를 확인해 주세요
          </h2>
          <h4 className="text-sm">
            신분증이 유효하지 않거나 실제 정보와 다를 경우 <br />
            서비스 이용이 제한됩니다.
          </h4>
        </div>

        {isOcrLoading ? (
          <div className="my-8 flex h-[206px] items-center justify-center">
            <Spinner className="h-10 w-10" />
          </div>
        ) : (
          maskedImageUrl && (
            <div className="relative my-8 aspect-[327/206]">
              <FallbackImage
                src={maskedImageUrl}
                alt="ID card"
                fill
                sizes="327px"
                className="rounded-lg object-cover"
              />
            </div>
          )
        )}
        {maskedImageUrl && (
          <Form {...form}>
            <form className="mb-20 space-y-8">
              <InputField
                readOnly
                form={form}
                name="name"
                label="이름"
                data-testid="ocr-name-input"
                placeholder="이름을 입력해주세요."
              />
              <FormField
                control={control}
                name="personalNum"
                render={() => (
                  <FormItem className="w-full space-y-3">
                    <FormLabel className="font-semibold sm:text-base">주민등록번호</FormLabel>
                    <FormControl>
                      <Input
                        value={hidePersonalNum(getValues('personalNum'))}
                        placeholder="주민등록번호를 입력해주세요."
                        type="text"
                        readOnly
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
      </div>

      <div className="fixed bottom-0 left-0 flex w-full justify-center gap-2 bg-white px-6 py-2">
        <SecondaryButton
          onClick={() => {
            reset();
            requestAnimationFrame(() => {
              handleFileUpload();
            });
          }}
          className="!h-12 w-full text-base"
          text="다시 촬영하기"
        />
        <PrimaryButton
          type="button"
          data-testid="ocr-verify-button"
          disabled={isVerifiedLoading}
          onClick={handleVerify}
          className="!h-12 w-full text-base"
          text="확인"
        />
      </div>
      <FileUploader
        ref={fileUploaderRef}
        acceptType=".jpg,.png,.jpeg"
        uploadedFiles={handleFileChange}
        multiple={false}
        maxSizeMB={5}
      />
    </CommonMotionProvider>
  );
};
