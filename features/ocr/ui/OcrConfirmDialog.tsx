'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';

interface OcrConfirmDialogProps {
  isVisible: boolean;
  toggleVisibility: () => void;
  callbackUrl: string;
}

export const OcrConfirmDialog = ({
  isVisible,
  toggleVisibility,
  callbackUrl,
}: OcrConfirmDialogProps) => {
  const { routerPush } = useWebViewRouter();

  const handleAction = () => {
    routerPush(`/ocr?callbackUrl=${callbackUrl}`);
  };

  return (
    <ConfirmDialog
      isOpen={isVisible}
      handleOpen={toggleVisibility}
      title="본인 실명인증 안내"
      description={`투자 서비스 이용을 위해 본인 실명인증이 필요합니다.\n 인증하시겠습니까?`}
      isCancelButton
      handleAction={handleAction}
    />
  );
};
