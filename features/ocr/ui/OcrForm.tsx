import { InformationCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { hidePersonalNum } from '@/features/ocr/lib/hidePersonalNum';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { FileUploader } from '@/shared/ui/FileUploader';
import { InputField } from '@/shared/ui/InputField';
import { Spinner } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';

import { OcrFormData } from '../lib/ocrSchema';

interface OcrFormProps {
  isOcrLoading: boolean;
  form: UseFormReturn<OcrFormData>;
  handleFileUpload: () => void;
  handleFileChange: (files: File[]) => void;
  handleVerify: () => void;
  isVerifiedLoading: boolean;
  fileUploaderRef: React.RefObject<HTMLInputElement | null>;
  isOnboarding?: boolean;
}

export const OcrForm = ({
  isOcrLoading,
  form,
  handleFileUpload,
  handleFileChange,
  handleVerify,
  isVerifiedLoading,
  fileUploaderRef,
  isOnboarding = false,
}: OcrFormProps) => {
  const { watch, control, getValues, reset } = form;

  const maskedImageUrl = watch('maskedImageUrl');

  return (
    <CommonMotionProvider
      className={`max-w-screen-test ${isOnboarding ? 'h-[calc(100dvh-160px)]' : 'h-mobile-non-gnb'} mx-auto mb-12 flex flex-col justify-between py-6 sm:mt-[100px] sm:h-auto sm:justify-start sm:py-0`}
    >
      <div>
        <div className="space-y-4 sm:text-center">
          <h2 data-testid="ocr-title" className="text-44 hidden sm:block">
            본인 실명인증
          </h2>
          <h4 className="text-20 break-keep sm:text-lg">
            주민등록증 또는 운전면허증 원본파일을 업로드 해주세요.
          </h4>
        </div>

        {isOcrLoading ? (
          <div className="my-12 flex h-[276px] items-center justify-center sm:my-20">
            <Spinner className="h-10 w-10" />
          </div>
        ) : maskedImageUrl ? (
          <div className="sm:x-[70px] relative my-12 aspect-[440/276] sm:mb-10 sm:mt-14">
            <FallbackImage
              src={maskedImageUrl}
              alt="ID card"
              fill
              sizes="440px"
              className="object-cover"
            />
          </div>
        ) : (
          <div className="my-12 space-y-4 sm:my-20 sm:px-[75px]">
            <div className="flex h-[206px] flex-col items-center justify-center gap-5 rounded-lg border border-dashed border-gray-300 sm:h-[276px]">
              <FallbackImage
                src="/images/identification_off.png"
                alt="ID card"
                width={84}
                height={63}
              />
              <PrimaryButton
                type="button"
                data-testid="ocr-upload-button"
                onClick={handleFileUpload}
                className="w-[113px]"
                text="파일 업로드"
              />
            </div>
            <p className="flex items-center gap-1 rounded-lg bg-blue-gray-00 px-4 py-2 text-gray-600">
              <InformationCircleIcon className="h-5 w-5 text-gray-500" />빛 반사가 심하거나 식별이
              어려운 경우 인증이 되지 않습니다.
            </p>
          </div>
        )}
        {maskedImageUrl && (
          <Form {...form}>
            <form className="mb-20 space-y-8">
              <InputField
                readOnly
                form={form}
                name="name"
                label="이름"
                data-testid="ocr-name-input"
                placeholder="이름을 입력해주세요."
              />
              <FormField
                control={control}
                name="personalNum"
                render={() => (
                  <FormItem className="w-full space-y-3">
                    <FormLabel className="font-semibold sm:text-base">주민등록번호</FormLabel>
                    <FormControl>
                      <Input
                        value={hidePersonalNum(getValues('personalNum'))}
                        placeholder="주민등록번호를 입력해주세요."
                        type="text"
                        readOnly
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
      </div>

      <div className="fixed bottom-0 left-0 flex w-full justify-center gap-2 px-6 py-2 sm:static sm:px-0">
        {maskedImageUrl && (
          <>
            <SecondaryButton
              onClick={() => {
                reset();
                requestAnimationFrame(() => {
                  handleFileUpload();
                });
              }}
              className="w-full sm:w-40"
              text="다시 업로드하기"
            />
            <PrimaryButton
              type="button"
              data-testid="ocr-verify-button"
              disabled={isVerifiedLoading}
              onClick={handleVerify}
              className="w-full sm:w-40"
              text="다음"
            />
          </>
        )}
      </div>
      <FileUploader
        ref={fileUploaderRef}
        acceptType=".jpg,.png,.jpeg"
        uploadedFiles={handleFileChange}
        multiple={false}
        maxSizeMB={5}
      />
    </CommonMotionProvider>
  );
};
