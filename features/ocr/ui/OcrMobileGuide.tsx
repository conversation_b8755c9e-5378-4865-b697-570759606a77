import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/24/solid';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { FileUploader } from '@/shared/ui/FileUploader';
import { Spinner } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';

import { OcrFormData } from '../lib/ocrSchema';

interface OcrMobileGuideProps {
  isOcrLoading: boolean;
  form: UseFormReturn<OcrFormData>;
  handleFileUpload: () => void;
  handleFileChange: (files: File[]) => void;
  handleVerify: () => void;
  isVerifiedLoading: boolean;
  fileUploaderRef: React.RefObject<HTMLInputElement | null>;
  isOnboarding?: boolean;
  className?: string;
}

export const OcrMobileGuide = ({
  isOcrLoading,
  form,
  handleFileUpload,
  handleFileChange,
  fileUploaderRef,
  isOnboarding = false,
  className,
}: OcrMobileGuideProps) => {
  const { watch } = form;

  const maskedImageUrl = watch('maskedImageUrl');

  return (
    <CommonMotionProvider
      className={`mx-auto flex ${isOnboarding ? 'h-[calc(100dvh-110px)]' : 'px-6'} max-w-screen-test flex-col justify-between py-6 ${className}`}
    >
      <div>
        <div className="space-y-3">
          <h2 data-testid="ocr-title" className="text-20">
            투자 서비스 이용을 위해 <br />
            본인 실명인증이 필요합니다.
          </h2>
          <h4 className="text-sm">주민등록증 또는 운전면허증 원본파일을 업로드 해주세요.</h4>
        </div>

        {isOcrLoading ? (
          <div className="my-20 flex h-[123px] items-center justify-center">
            <Spinner className="h-10 w-10" />
          </div>
        ) : maskedImageUrl ? (
          <div className="relative mx-auto my-7 aspect-[4/3]">
            <FallbackImage
              src={maskedImageUrl}
              alt="ID card"
              fill
              sizes="240px"
              className="object-cover"
            />
          </div>
        ) : (
          <div className="my-4">
            <div className="relative mx-auto aspect-[4/3] max-h-[180px]">
              <FallbackImage
                src="/images/ocr_mobile.png"
                alt="ID card"
                fill
                sizes="240px"
                className="object-cover"
              />
            </div>
          </div>
        )}
        {!isOcrLoading && !maskedImageUrl && (
          <div className="space-y-3">
            <p className="font-semibold">이용안내</p>
            <div className="relative aspect-[327/187]">
              <FallbackImage
                src="/images/ocr_info.png"
                alt="ocr_info"
                fill
                sizes="327px"
                className="object-cover"
              />
            </div>
          </div>
        )}
      </div>
      {!isOnboarding && (
        <div className="fixed bottom-0 left-0 flex w-full justify-center bg-white px-6 py-2">
          <PrimaryButton
            onClick={handleFileUpload}
            text="다음"
            className="!h-12 w-full text-base"
          />
        </div>
      )}

      <FileUploader
        ref={fileUploaderRef}
        acceptType=".jpg,.png,.jpeg"
        uploadedFiles={handleFileChange}
        multiple={false}
        maxSizeMB={5}
      />
    </CommonMotionProvider>
  );
};
