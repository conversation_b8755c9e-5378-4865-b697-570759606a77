'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useRef } from 'react';
import { useForm } from 'react-hook-form';

import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { createInquiry } from '@/entities/inquiries/api/createInquiry';
import { CreateInquiryPayload } from '@/entities/inquiries/interface';
import { InquiryAnswerStatus } from '@/entities/inquiries/types';

import { useToast } from '@/shared/model/useToast';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { NotificationChannel } from '@/shared/types';

import { InquiryFormData, inquiryFormSchema } from '../lib/inquiryFormSchema';

export const useInquiryForm = (isMobile?: boolean) => {
  const { successToast, errorToast } = useToast();
  const ref = useRef<HTMLInputElement>(null);
  const { routerPush, routerBack } = useWebViewRouter();
  const router = useRouter();
  const queryClient = useQueryClient();
  const form = useForm<InquiryFormData>({
    resolver: zodResolver(inquiryFormSchema),
    mode: 'onChange',
    defaultValues: {
      attachFiles: [],
      isEmailNotification: false,
      isSmsNotification: false,
      terms: false,
    },
  });

  const { userId } = useFetchUser();

  const handleFileUpload = () => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const handleUploadedFiles = (files: File[]) => {
    form.setValue('attachFiles', [...(form.watch('attachFiles') || []), ...files]);
  };

  const handleDeleteFile = (file: File) => {
    form.setValue(
      'attachFiles',
      form.watch('attachFiles')?.filter((f) => f.name !== file.name) || [],
    );
  };

  const onSubmit = async (data: InquiryFormData) => {
    if (!userId) {
      errorToast({
        title: '로그인 후 이용해주세요.',
      });

      routerPush('/sign-in');
      return;
    }

    let notificationChannel: NotificationChannel[] = [];

    if (data.isEmailNotification) notificationChannel.push(NotificationChannel.EMAIL);
    if (data.isSmsNotification) notificationChannel.push(NotificationChannel.SMS);

    const payload: CreateInquiryPayload = {
      ...data,
      privacyAgreement: data.terms,
      notificationChannel,
      answerStatus: InquiryAnswerStatus.REQUEST,
      status: 'published',
      userId,
      attachFiles: data.attachFiles || [],
    };

    try {
      await createInquiry(payload);
      successToast({
        title: '문의 등록 성공',
      });
      queryClient.invalidateQueries({ queryKey: queries.inquiries._def });
      if (isMobile) {
        router.replace('/mobile/user/support/inquiry-history');
      }
      routerBack();
    } catch (error) {
      errorToast({
        title: '문의 등록 실패',
      });
    }
  };

  const notificationChecked = (key: 'isEmailNotification' | 'isSmsNotification') => {
    const currentValue = form.getValues(key);
    const newValue = !currentValue;

    form.setValue(key, newValue);
  };

  return {
    form,
    ref,
    onSubmit,
    handleFileUpload,
    handleUploadedFiles,
    handleDeleteFile,
    notificationChecked,
  };
};
