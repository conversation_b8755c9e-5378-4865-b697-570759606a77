'use client';

import { TrashIcon } from '@heroicons/react/24/outline';
import { PaperClipIcon, PlusIcon } from '@heroicons/react/24/solid';

import { InquiryCategorySearchOptions } from '@/entities/inquiries/config';
import { InquiryCategory } from '@/entities/inquiries/types';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { FileInfoTooltop } from '@/shared/ui/FileInfoTooltop';
import { FileUploader } from '@/shared/ui/FileUploader';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';
import { Label } from '@/shared/ui/shadcn/label';
import { TextareaField } from '@/shared/ui/TextareaField';

import { useInquiryForm } from '../model/useInquiryForm';

interface InquiryFormProps {
  support?: boolean;
  isMobile?: boolean;
}

export const InquiryForm = ({ support = false, isMobile = false }: InquiryFormProps) => {
  const {
    form,
    onSubmit,
    ref,
    handleFileUpload,
    handleUploadedFiles,
    handleDeleteFile,
    notificationChecked,
  } = useInquiryForm(isMobile);

  const { control, handleSubmit, watch, setValue, formState } = form;

  return (
    <>
      <form
        className={`flex flex-col space-y-8 rounded-[20px] pb-16 sm:space-y-10 sm:pb-0 ${!support && `sm:border sm:border-gray-300 sm:px-8 sm:py-8`}`}
      >
        <Form {...form}>
          <FormField
            control={control}
            name="category"
            render={() => (
              <FormItem className={`${support ? 'max-w-[488px]' : 'ml:w-1/2'}`}>
                <FormLabel className="text-sm font-semibold sm:text-base">
                  문의 분류 <strong className="font-semibold text-primary-500"> (필수)</strong>
                </FormLabel>
                <FormControl>
                  <CommonSelect
                    value={watch('category')}
                    onChange={(value) => form.setValue('category', value as InquiryCategory)}
                    options={InquiryCategorySearchOptions}
                    placeholder="문의 분류를 선택해 주세요."
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <InputField
            form={form}
            label="제목"
            requiredText
            name="title"
            placeholder="제목을 입력해 주세요."
          />
          <TextareaField
            form={form}
            label="내용"
            requiredText
            name="content"
            placeholder="문의 내용을 입력해 주세요."
          />
          <FormField
            control={control}
            name="attachFiles"
            render={() => (
              <FormItem>
                <div className="flex items-center gap-1">
                  <FormLabel className="text-sm font-semibold sm:text-base">
                    파일 등록 <span className="text-gray-500">(선택)</span>
                  </FormLabel>
                  <FileInfoTooltop />
                </div>
                <FormControl>
                  <div className="flex flex-col gap-2">
                    {watch('attachFiles')?.map((file) => (
                      <div
                        className="flex justify-between rounded-lg border border-gray-300 px-4 py-3"
                        key={file.name}
                      >
                        <div className="flex items-center gap-2">
                          <PaperClipIcon color="blue" className="h-5 w-5" />
                          {file.name}
                        </div>
                        <button type="button" onClick={() => handleDeleteFile(file)}>
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      onClick={handleFileUpload}
                      className="h-[44px] w-full rounded-lg border border-gray-300 text-gray-500 shadow-none sm:h-12"
                    >
                      <PlusIcon className="h-5 w-5" />
                      추가할 파일 선택
                    </Button>
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="isEmailNotification"
            render={() => (
              <FormItem>
                <FormLabel className="text-sm font-semibold sm:text-base">
                  답변 완료 시 알림 수단 <span className="text-gray-500">(선택)</span>
                </FormLabel>
                <FormControl>
                  <div className="flex gap-12 text-gray-700">
                    <CheckboxField
                      label="이메일"
                      value="isEmailNotification"
                      checked={watch('isEmailNotification') as boolean}
                      onCheckedChange={() => notificationChecked('isEmailNotification')}
                    />
                    <CheckboxField
                      label="SMS"
                      value="isSmsNotification"
                      checked={watch('isSmsNotification') as boolean}
                      onCheckedChange={(e: boolean) => notificationChecked('isSmsNotification')}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <div className="space-y-3">
            <Label className="text-sm font-semibold sm:text-base">개인정보 수집 및 이용 내용</Label>
            <div className="h-[146px] overflow-y-auto whitespace-pre-wrap rounded-lg border border-gray-300 bg-gray-50 px-4 py-3 text-sm leading-[170%]">
              개인정보 처리방침
              <br />
              <br />
              당사는 문의를 통해 입력받은 개인정보를 수집 및 이용하고 있습니다.
              <br />
              문의하신 사항에 대해 상담 처리를 원하시는 경우 개인정보 처리방침에 동의하셔야 합니다.
              <br />- 개인정보 수집 및 이용 목적: 문의 내용 해결
              <br />- 수집하는 개인정보 항목: 성명, 연락처, 이메일
              <br />- 개인정보 보유 및 이용 기간: 개인정보의 수집 및 이용목적 달성 후 파기 (단, 관계
              법령에 의해 보존할 필요성이 있는 경우, 법령이 정한 시점까지 보관 후 파기)
            </div>
            <div className="flex items-center gap-2">
              <CheckboxField
                label="개인정보 수집 및 이용 동의"
                required
                value="terms"
                checked={watch('terms') as boolean}
                onCheckedChange={(e: boolean) => {
                  setValue('terms', e);
                  form.trigger('terms');
                }}
              />
            </div>
          </div>
          {!support && (
            <>
              <PrimaryButton
                type="button"
                disabled={!formState.isValid}
                onClick={handleSubmit(onSubmit)}
                text="제출하기"
                className="hidden w-full sm:mx-auto sm:block sm:w-40 ml:mx-0"
              />
              <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:hidden">
                <PrimaryButton
                  type="button"
                  disabled={!formState.isValid}
                  onClick={handleSubmit(onSubmit)}
                  text="제출하기"
                  className="!h-12 w-full text-base"
                />
              </div>
            </>
          )}
          {support && (
            <div className="mt-10 flex justify-center">
              <PrimaryButton
                type="button"
                disabled={!formState.isValid}
                onClick={handleSubmit(onSubmit)}
                text="제출하기"
                className="w-full sm:mx-auto sm:w-40 ml:mx-0"
              />
            </div>
          )}
        </Form>
      </form>
      <FileUploader ref={ref} uploadedFiles={handleUploadedFiles} multiple />
    </>
  );
};
