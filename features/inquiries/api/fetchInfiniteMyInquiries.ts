import { useInfiniteQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { InquiryHistory } from '@/entities/inquiries/types';

import { ListRequest, ScrollApiResponse } from '@/shared/interface';

export const fetchInfiniteMyInquiries = (params: ListRequest, userId?: string) => {
  return useInfiniteQuery({
    ...queries.inquiries.infinite(userId as string, params),
    initialPageParam: 1,
    getNextPageParam: (lastPage: ScrollApiResponse<InquiryHistory>) => {
      if (!lastPage.isLast) return lastPage.nextPage;
      return null;
    },
    enabled: !!userId,
  });
};
