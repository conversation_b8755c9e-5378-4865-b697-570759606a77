import { useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { ListRequest } from '@/shared/interface';

export const fetchSubscriptionInquiries = (
  securitiesId: string,
  userId: string,
  params: ListRequest,
) => {
  return useQuery({
    ...queries.inquiries.subscriptionInquiries(securitiesId, userId, params),
    enabled: !!securitiesId && !!userId,
  });
};
