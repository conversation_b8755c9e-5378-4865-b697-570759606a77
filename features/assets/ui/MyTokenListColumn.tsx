import { ColumnDef } from '@tanstack/react-table';

import { Notice } from '@/entities/notices/types';

import { utilFormats } from '@/shared/lib/utilformats';

const { YYYYMMDD } = utilFormats();

export const MyTokenListColumn: ColumnDef<Notice>[] = [
  {
    accessorKey: 'title',
    size: 1000,
    header: () => <p className="flex-1 px-2 text-left">증권명</p>,
    cell: ({ row }) => {
      return (
        <div className="line-clamp-1 flex-1 px-2 text-left text-xl font-bold">
          {row.original.id}
        </div>
      );
    },
  },
  {
    accessorKey: 'title',
    size: 100,
    header: () => <p className="w-[100px] shrink-0 text-right">수량</p>,
    cell: ({ row }) => {
      return <div className="w-[100px] shrink-0 text-right">{row.original.content}</div>;
    },
  },
  {
    accessorKey: 'title',
    size: 160,
    header: () => <p className="w-[160px] shrink-0 text-right">취득단가</p>,
    cell: ({ row }) => {
      return <div className="w-[160px] shrink-0 text-right">{row.original.publishedAt}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    size: 160,
    header: () => <p className="w-[160px] shrink-0 px-2 text-right">총 매입가</p>,
    cell: ({ row }) => {
      return (
        <p className="w-[160px] shrink-0 px-2 text-right">{YYYYMMDD(row.original.createdAt)}</p>
      );
    },
  },
];
