import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { GetMyDepositHistoryPayload } from '@/entities/assets/interface';

export const fetchMyDepositHistory = (payload: GetMyDepositHistoryPayload, enabled: boolean) => {
  return useQuery({
    ...queries.assets.myDepositHistory(payload),
    enabled,
    placeholderData: keepPreviousData,
  });
};
