import { keepPreviousData, useInfiniteQuery, UseInfiniteQueryResult } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { GetMyDepositHistoryPayload } from '@/entities/assets/interface';
import { MyDepositHistory } from '@/entities/assets/types';

import { InfiniteScrollResponse, ScrollApiResponse } from '@/shared/interface';

export const fetchInfiniteMyDepositHistory = (
  params: GetMyDepositHistoryPayload,
  enabled: boolean,
): UseInfiniteQueryResult<InfiniteScrollResponse<MyDepositHistory>> => {
  return useInfiniteQuery({
    ...queries.assets.myDepositHistoryList(params),
    initialPageParam: 0,
    getNextPageParam: (lastPage: ScrollApiResponse<MyDepositHistory>) => {
      if (!lastPage.isLast) return lastPage.nextPage;
      return null;
    },
    enabled,
    placeholderData: keepPreviousData,
  });
};
