import { z } from 'zod';

import { phoneRegex } from '@/shared/lib/regex';

export const withdrawFormSchema = z.object({
  mobileNumber: z.string().regex(phoneRegex, '올바른 휴대전화번호를 입력하세요.'),
  mobileVerifiedCode: z.string().min(6, '6자리 숫자를 입력하세요.'),
  isMobileVerified: z.boolean(),
  withdrawAmount: z.number().min(1, '출금 금액을 입력하세요.'),
});

export type WithdrawFormData = z.infer<typeof withdrawFormSchema>;
