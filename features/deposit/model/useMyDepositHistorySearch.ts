import { se } from 'date-fns/locale';
import dayjs from 'dayjs';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import { useDatePickerDialog } from '@/shared/model/useDatePickerDialog';

export const useMyDepositHistorySearch = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [searchFormState, setSearchFormState] = useState({
    searchBeginAt: searchParams.get('searchBeginAt') || '',
    searchEndAt: searchParams.get('searchEndAt') || '',
    period: '',
  });

  const { isDatePickerOpen, handleDatePickerClick } = useDatePickerDialog();

  const handlePeriodChange = (value: string) => {
    let searchBeginAt = '';
    let searchEndAt = '';

    const searchParams = new URLSearchParams();
    if (value === '1week') {
      searchBeginAt = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss');
      searchEndAt = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }

    if (value === '1month') {
      searchBeginAt = dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss');
      searchEndAt = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }

    if (value === '3month') {
      searchBeginAt = dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss');
      searchEndAt = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }

    if (value === '6month') {
      searchBeginAt = dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss');
      searchEndAt = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }

    setSearchFormState({
      period: value,
      searchBeginAt,
      searchEndAt,
    });

    searchParams.set('searchBeginAt', searchBeginAt);
    searchParams.set('searchEndAt', searchEndAt);

    router.replace(`${pathname}?${searchParams.toString()}`, { scroll: false });
  };

  const handleStartDateChange = (value: string) => {
    setSearchFormState({ ...searchFormState, searchBeginAt: value });
  };

  const handleEndDateChange = (value: string) => {
    setSearchFormState({ ...searchFormState, searchEndAt: value });
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const searchParams = new URLSearchParams();
    if (searchFormState.searchBeginAt) {
      searchParams.set('searchBeginAt', searchFormState.searchBeginAt);
    }

    if (searchFormState.searchEndAt) {
      searchParams.set('searchEndAt', searchFormState.searchEndAt);
    }

    setSearchFormState({
      ...searchFormState,
      period: searchParams.get('period') || '',
    });

    router.replace(`${pathname}?${searchParams.toString()}`, { scroll: false });
  };

  return {
    searchFormState,
    handlePeriodChange,
    handleStartDateChange,
    handleEndDateChange,
    handleSearch,
    isDatePickerOpen,
    handleDatePickerClick,
  };
};
