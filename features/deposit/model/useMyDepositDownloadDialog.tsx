import React, { useState } from 'react';

import { useDatePickerDialog } from '@/shared/model/useDatePickerDialog';

export const useMyDepositDownloadDialog = () => {
  const [period, setPeriod] = useState<string>('');
  const [selectDate, setSelectDate] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: '',
    endDate: '',
  });

  const { isDatePickerOpen, handleDatePickerClick } = useDatePickerDialog();

  const handlePeriodChange = (value: string) => {
    setPeriod(value);
  };

  const handleStartDateChange = (value: string) => {
    setSelectDate({ ...selectDate, startDate: value });
  };

  const handleEndDateChange = (value: string) => {
    setSelectDate({ ...selectDate, endDate: value });
  };

  return {
    period,
    selectDate,
    isDatePickerOpen,
    handlePeriodChange,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
  };
};
