'use client';

import { fetchCorporateAccount } from '@/features/assets/api/fetchCorporateAccount';
import { fetchUserAccount } from '@/features/assets/api/fetchUserAccount';
import { useFetchUser } from '@/features/users/model/useFetchUser';

export const useFetchAccount = () => {
  const { isGeneral, isExistAccount } = useFetchUser();

  const {
    data: userAccount,
    isLoading: isLoadingUserAccount,
    refetch: refetchUserAccount,
    isFetching: isFetchingUserAccount,
  } = fetchUserAccount(isGeneral && isExistAccount);

  const {
    data: corporateAccount,
    isLoading: isLoadingCorporateAccount,
    refetch: refetchCorporateAccount,
    isFetching: isFetchingCorporateAccount,
  } = fetchCorporateAccount(!isGeneral && isExistAccount);

  return {
    account: isGeneral ? userAccount : corporateAccount,
    isLoading: isLoadingUserAccount || isLoadingCorporateAccount,
    refetch: isGeneral ? refetchUserAccount : refetchCorporateAccount,
    isFetching: isFetchingUserAccount || isFetchingCorporateAccount,
  };
};
