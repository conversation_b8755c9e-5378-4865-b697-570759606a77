import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  DepositAccountFormData,
  depositAccountSchema,
} from '@/features/deposit/lib/depositAccountSchema';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { createCorporateVirtualAccount } from '@/entities/assets/api/createCorporateVirtualAccount';
import { createVirtualAccount } from '@/entities/assets/api/createVirtualAccount';
import { verifyAccount } from '@/entities/assets/api/verifyAccount';
import { verifyCorporateAccount } from '@/entities/assets/api/verifyCorporateAccount';
import { DepositSteps } from '@/entities/assets/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { UserRole } from '@/shared/types';

export const useAccountRegister = () => {
  const { step, handleStep, isStep } = useStep<DepositSteps>(DepositSteps.TERMS);
  const [virtualAccount, setVirtualAccount] = useState({
    depositAccountInstCode: '',
    depositAccountNumberCode: '',
    depositorName: '',
  });
  const { isVisible: isAgree, toggleVisibility: toggleAgree } = useVisibility();
  const { isVisible: isIdentityVerification, toggleVisibility: toggleIdentityVerification } =
    useVisibility();
  const { successToast, errorToast } = useToast();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const callbackUrl = searchParams.get('callbackUrl');
  const form = useForm<DepositAccountFormData>({
    resolver: zodResolver(depositAccountSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
  });
  const { data, update } = useSession();
  const { user, isGeneral, isExistAccount } = useFetchUser();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { verifyIdentity } = useIdentityVerification();

  const handleIdentityVerification = async () => {
    toggleIdentityVerification();

    try {
      const registerForm = JSON.stringify(form.getValues());

      sessionStorage.setItem('registerForm', registerForm);

      await verifyIdentity('verify-account-register');

      handleStep(DepositSteps.CONFIRM);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const transactionType = searchParams.get('transactionType');

    if (transactionType === 'IDENTITY_VERIFICATION') {
      const registerForm = sessionStorage.getItem('registerForm');

      if (registerForm) {
        form.reset(JSON.parse(registerForm));
        clearParamsExceptCallbackUrl();

        handleStep(DepositSteps.CONFIRM);
      }

      sessionStorage.removeItem('registerForm');
    }

    return () => {
      sessionStorage.removeItem('registerForm');
    };
  }, [searchParams]);

  const goToCallbackUrl = () => {
    router.replace(callbackUrl || '/');
  };

  // callbackUrl을 제외한 모든 파라미터를 지우는 함수
  const clearParamsExceptCallbackUrl = () => {
    if (callbackUrl) {
      router.replace(`${window.location.pathname}?callbackUrl=${encodeURIComponent(callbackUrl)}`);
    } else {
      router.replace(window.location.pathname);
    }
  };

  const handleVerifyAccount = async () => {
    try {
      setIsLoading(true);
      if (isGeneral) {
        await verifyAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      } else {
        await verifyCorporateAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      }

      successToast({
        title: '계좌 인증이 완료되었습니다.',
      });

      toggleIdentityVerification();
    } catch (error) {
      errorToast({
        title: '계좌 인증에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateVirtualAccount = async () => {
    try {
      setIsLoading(true);
      let virtualAccount;
      if (isGeneral) {
        virtualAccount = await createVirtualAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      } else {
        virtualAccount = await createCorporateVirtualAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      }

      setVirtualAccount(virtualAccount);

      // 가상계좌 개설 후 OCR 인증이 되어있다면 투자자 전환
      if (data?.user.role === UserRole.USR && user?.userAuthOCR) {
        await update({
          ...data,
        });
      }

      await queryClient.invalidateQueries({ queryKey: queries.assets._def });
      await queryClient.invalidateQueries({ queryKey: queries.user._def });

      successToast({
        title: '가상 계좌가 생성되었습니다.',
      });

      handleStep(DepositSteps.COMPLETE);
    } catch (error) {
      errorToast({
        title: '가상 계좌 생성에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    handleVerifyAccount,
    handleCreateVirtualAccount,
    virtualAccount,
    step,
    handleStep,
    isStep,
    goToCallbackUrl,
    isAgree,
    toggleAgree,
    isExistAccount,
    isLoading,
    isIdentityVerification,
    toggleIdentityVerification,
    handleIdentityVerification,
  };
};
