import { fetchAccoutExistence } from '@/features/assets/api/fetchAccoutExistence';
import { fetchCorporateAccountExistence } from '@/features/assets/api/fetchCorporateAccountExistence';

import { KycStatus, User } from '@/entities/users/types';

import { UserCat } from '@/shared/types';

export const useAccountExistence = (user: User | undefined) => {
  const shouldFetchUserAccount = user?.type === UserCat.GENERAL && user.kycStatus === KycStatus.KYC;
  const shouldFetchCorporateAccount =
    user?.type === UserCat.CORPORATE && user.kycStatus === KycStatus.KYC;

  const { data: isUserExistAccount, isLoading: isLoadingUserAccount } =
    fetchAccoutExistence(shouldFetchUserAccount);
  const { data: isExistCorporateAccount, isLoading: isLoadingCorporateAccount } =
    fetchCorporateAccountExistence(shouldFetchCorporateAccount);

  return {
    isExistAccount:
      user?.type === UserCat.GENERAL ? !!isUserExistAccount : !!isExistCorporateAccount,
    isLoading: isLoadingUserAccount || isLoadingCorporateAccount,
  };
};
