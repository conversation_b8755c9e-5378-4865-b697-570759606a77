import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  DepositAccountFormData,
  depositAccountSchema,
} from '@/features/deposit/lib/depositAccountSchema';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { updateCorporateAccount } from '@/entities/assets/api/updateCorporateAccount';
import { updateUserAccount } from '@/entities/assets/api/updateUserAccount';
import { verifyAccount } from '@/entities/assets/api/verifyAccount';
import { verifyCorporateAccount } from '@/entities/assets/api/verifyCorporateAccount';
import { DepositChangeSteps, DepositSteps } from '@/entities/assets/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { useFetchAccount } from './useFetchAccount';

export const useAccountChange = () => {
  const { step, handleStep, isStep } = useStep<DepositChangeSteps>(DepositChangeSteps.FORM);
  const [withdrawAccount, setWithdrawAccount] = useState({
    depositAccountInstCode: '',
    depositAccountNumberCode: '',
    depositorName: '',
  });

  const { successToast, errorToast } = useToast();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const callbackUrl = searchParams.get('callbackUrl');
  const form = useForm<DepositAccountFormData>({
    resolver: zodResolver(depositAccountSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
  });
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const { isGeneral } = useFetchUser();
  const { routerPush, routerBack } = useWebViewRouter();

  const { account } = useFetchAccount();
  const hasChecked = useRef(false); // 첫 실행 여부 추적

  const handleChangeAccount = async () => {
    if (!account) {
      errorToast({
        title: '출금계좌가 존재하지 않습니다. 출금계좌를 등록해주세요.',
      });
      routerBack();
      return;
    }

    setIsLoading(true);

    try {
      let withdrawAccount;
      if (isGeneral) {
        await verifyAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
        withdrawAccount = await updateUserAccount(account?.accountId.toString(), {
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      } else {
        await verifyCorporateAccount({
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
        withdrawAccount = await updateCorporateAccount(account?.accountId.toString(), {
          bankCode: form.getValues('bankCode'),
          accountNumber: form.getValues('accountNumber'),
        });
      }

      setWithdrawAccount(withdrawAccount);

      await queryClient.invalidateQueries({ queryKey: queries.assets._def });
      await queryClient.invalidateQueries({ queryKey: queries.user._def });

      successToast({
        title: '가상 계좌가 변경되었습니다.',
      });

      handleStep(DepositChangeSteps.COMPLETE);
    } catch (error) {
      errorToast({
        title: '가상 계좌 변경에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const goToCallbackUrl = () => {
    router.replace(callbackUrl || '/');
  };

  useEffect(() => {
    if (hasChecked.current) return;
    hasChecked.current = true;

    const isVerified = sessionStorage.getItem('isVerifiedForAccountChange');

    if (isVerified !== 'true') {
      alert('본인인증 후 접근 가능합니다.');
      router.replace(callbackUrl || '/');
    } else {
      // 인증 후 접근이므로 인증 상태 제거
      sessionStorage.removeItem('isVerifiedForAccountChange');
    }
  }, []);

  return {
    form,
    handleChangeAccount,
    withdrawAccount,
    step,
    handleStep,
    isStep,
    goToCallbackUrl,
    isLoading,
  };
};
