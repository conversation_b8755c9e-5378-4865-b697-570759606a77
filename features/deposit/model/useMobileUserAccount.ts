import { usePathname } from 'next/navigation';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { useFetchAccount } from './useFetchAccount';

export const useMobileUserAccount = () => {
  const { isExistAccount } = useFetchUser();
  const { routerPush } = useWebViewRouter();
  const pathname = usePathname();
  const { account, isLoading, refetch, isFetching } = useFetchAccount();

  const handleWithdraw = () => {
    routerPush(`/deposit/withdraw?callbackUrl=${pathname}`);
  };

  return {
    account,
    isLoadingMyAccount: isLoading || isFetching,
    handleWithdraw,
    isExistAccount,
    refetchAccount: refetch,
  };
};
