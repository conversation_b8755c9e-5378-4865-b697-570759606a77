export const bankList = [
  { bankCode: '011', bankName: '농협은행', isValid: true },
  { bankCode: '012', bankName: '지역농축협', isValid: true },
  { bankCode: '264', bankName: '키움증권', isValid: true },
  { bankCode: '002', bankName: '산업은행', isValid: true },
  { bankCode: '003', bankName: '기업은행', isValid: true },
  { bankCode: '004', bankName: '국민은행', isValid: true },
  { bankCode: '007', bankName: '수협', isValid: true },
  { bankCode: '020', bankName: '우리은행', isValid: true },
  { bankCode: '023', bankName: 'SC제일은행', isValid: true },
  { bankCode: '027', bankName: '한국씨티은행', isValid: true },
  { bankCode: '031', bankName: '대구은행', isValid: true },
  { bankCode: '032', bankName: '부산은행', isValid: true },
  { bankCode: '034', bankName: '광주은행', isValid: true },
  { bankCode: '035', bankName: '제주은행', isValid: true },
  { bankCode: '037', bankName: '전북은행', isValid: true },
  { bankCode: '039', bankName: '경남은행', isValid: true },
  { bankCode: '045', bankName: '새마을금고', isValid: true },
  { bankCode: '048', bankName: '신용협동조합', isValid: true },
  { bankCode: '050', bankName: '상호저축은행', isValid: true },
  { bankCode: '064', bankName: '산림조합', isValid: true },
  { bankCode: '071', bankName: '우체국', isValid: true },
  { bankCode: '081', bankName: 'KEB하나은행', isValid: true },
  { bankCode: '088', bankName: '신한은행', isValid: true },
  { bankCode: '089', bankName: 'K뱅크', isValid: true },
  { bankCode: '090', bankName: '카카오뱅크', isValid: true },
  { bankCode: '092', bankName: '토스뱅크', isValid: true },
];

export const bankListOptions = bankList.map((bank: any) => ({
  label: bank.bankName,
  value: bank.bankCode,
}));
