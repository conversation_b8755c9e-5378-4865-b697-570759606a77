'use client';

import { InformationCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';

import { bankListOptions } from '../config/bankList';
import { DepositAccountFormData } from '../lib/depositAccountSchema';

interface WithdrawAccountRegisterFormProps {
  form: UseFormReturn<DepositAccountFormData>;
  handleVerifyAccount: () => void;
  isOnboarding?: boolean;
  isChange?: boolean;
  isLoading?: boolean;
}

export const WithdrawAccountRegisterForm = ({
  form,
  handleVerifyAccount,
  isOnboarding = false,
  isChange = false,
  isLoading = false,
}: WithdrawAccountRegisterFormProps) => {
  const { control, watch, formState } = form;

  const { isGeneral, isCorporate } = useFetchUser();

  return (
    <CommonMotionProvider
      className={`mx-auto flex ${isOnboarding && 'h-[calc(100dvh-120px)]'} w-full max-w-screen-test flex-col justify-between py-6 sm:mt-[100px] sm:h-auto sm:justify-start sm:py-0`}
    >
      <div>
        <div className="space-y-4">
          <h2 className="text-44 hidden sm:block">
            {isChange ? '출금계좌 변경' : '출금계좌 등록'}
          </h2>
          {isChange && isGeneral ? (
            <h4 className="text-20 sm:text-lg sm:font-normal">
              변경하실 본인 명의의 은행 계좌를 등록해 주세요. <br />
              등록된 계좌는 예치금 입출금 시 사용됩니다.
            </h4>
          ) : isGeneral ? (
            <h4 className="text-20 sm:text-lg sm:font-normal">
              본인 명의의 은행 계좌를 등록해주세요. <br />
              등록된 계좌는 예치금 입출금 시 사용됩니다.
            </h4>
          ) : (
            <h4 className="text-20 sm:text-lg sm:font-normal">
              법인 명의의 은행 계좌를 등록해주세요. <br />
              등록된 계좌는 예치금 입출금 시 사용됩니다.
            </h4>
          )}
          {isCorporate && (
            <div className="flex items-center gap-1 rounded-lg bg-blue-gray-00 px-3 py-2 text-base leading-[150%] text-gray-600">
              <InformationCircleIcon className="h-5 w-5 text-gray-500" />
              회원가입 신청 시 제출한 통장 사본의 계좌와 일치하지 않을 경우 개설이 어렵습니다.
            </div>
          )}
        </div>

        <Form {...form}>
          <form action="" className="my-10 space-y-8 sm:my-20 sm:space-y-10">
            <FormField
              control={control}
              name="bankCode"
              render={() => (
                <FormItem>
                  <FormLabel className="text-sm font-semibold sm:text-base">
                    은행 선택 <strong className="font-semibold text-primary-500"> (필수)</strong>
                  </FormLabel>
                  <FormControl>
                    <CommonSelect
                      value={watch('bankCode')}
                      onChange={(value) =>
                        form.setValue('bankCode', value, {
                          shouldValidate: true,
                        })
                      }
                      options={bankListOptions}
                      placeholder="은행 선택"
                      data-testid="withdraw-account-register-bank-code-select"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <InputField
              form={form}
              label="출금 계좌"
              data-testid="withdraw-account-register-account-number-input"
              requiredText
              name="accountNumber"
              placeholder="(-)없이 계좌번호 입력"
            />
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 flex w-full justify-center bg-white px-6 py-2 sm:static">
        <PrimaryButton
          onClick={handleVerifyAccount}
          disabled={!formState.isValid || isLoading}
          text="다음"
          type="button"
          className="!h-12 w-full text-base sm:w-40"
          data-testid="withdraw-account-register-next-button"
        />
      </div>
    </CommonMotionProvider>
  );
};
