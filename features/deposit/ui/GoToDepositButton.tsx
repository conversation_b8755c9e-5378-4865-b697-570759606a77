import React from 'react';

import { cn } from '@/shared/lib/utils';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const GoToDepositButton = ({ className }: { className?: string }) => {
  const { routerPush } = useWebViewRouter();

  return (
    <button
      onClick={() => routerPush('/user/assets/deposit')}
      className={cn(
        'h-12 w-full rounded-lg bg-blue-gray-50 font-semibold text-blue-gray-500',
        className,
      )}
    >
      자산 전체 보기
    </button>
  );
};
