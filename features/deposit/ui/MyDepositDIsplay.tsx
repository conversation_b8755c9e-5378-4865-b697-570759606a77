import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { InformationCircleIcon } from '@heroicons/react/24/solid';

import { Account } from '@/entities/assets/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useClipboard } from '@/shared/model/useClipboard';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Button } from '@/shared/ui/shadcn/button';
import { Separator } from '@/shared/ui/shadcn/separator';

import { formatAccountNumber } from '../lib/formatAccountNumber';
import { getBankName } from '../lib/getBankName';

interface MyDepositDIsplayProps {
  myAccount?: Account;
  refetch: () => void;
  isLoading: boolean;
  handleWithdraw: () => void;
  isExistAccount: boolean;
}

export const MyDepositDIsplay = ({
  myAccount,
  refetch,
  isLoading,
  handleWithdraw,
  isExistAccount,
}: MyDepositDIsplayProps) => {
  const { CASHCOMMA } = utilFormats();

  const { handleCopy } = useClipboard();

  const isNotLockedAmount = myAccount?.subscriptionMarginSummary.totalLockedMargin === 0;

  return (
    <div className="hidden space-y-6 px-6 sm:block sm:px-8 ml:px-0">
      <div className="rounded-[20px] border border-[rgba(0,0,0,0.04)] bg-custom-gradient">
        <div className="flex items-center gap-[10px] px-5 pt-5 sm:px-8 sm:pt-8">
          <p className="text-sm">
            {getBankName(myAccount?.depositAccountInstCode)}
            {formatAccountNumber(myAccount?.depositAccountNumberCode || '')}
          </p>
          {myAccount?.depositAccountNumberCode && (
            <button
              className="rounded-[20px] bg-black/[0.06] px-2 py-1 text-xs font-semibold text-gray-600"
              onClick={() => handleCopy(myAccount?.depositAccountNumberCode)}
            >
              복사
            </button>
          )}
        </div>
        <div
          className={`mt-4 flex items-center justify-between px-5 sm:px-8 ${isNotLockedAmount && 'pb-5 sm:pb-8'}`}
        >
          <div className="flex gap-6">
            <div className="flex items-center gap-2">
              <FallbackImage src="/images/deposit.png" alt="deposit" width={22} height={22} />
              <p className="text-lg font-bold text-primary-600">내 예치금</p>
            </div>
            <div className="flex items-center gap-3">
              <p className="text-32">{CASHCOMMA(myAccount?.currentNetBalance || 0)}원</p>
              <button onClick={refetch} disabled={isLoading || !isExistAccount}>
                <ArrowPathIcon
                  strokeWidth={2}
                  className={`h-6 w-6 text-black/30 ${isLoading && 'animate-spin'} `}
                />
              </button>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <InformationCircleIcon className="h-5 w-5" />
            <Button
              className="w-[96px] bg-gray-900 font-semibold text-white"
              onClick={handleWithdraw}
              disabled={!isExistAccount}
            >
              출금하기
            </Button>
          </div>
        </div>
        {!isNotLockedAmount && <Separator className="my-6 bg-black/10" />}
        {myAccount?.subscriptionMarginSummary.detail &&
          myAccount.subscriptionMarginSummary.detail.length > 0 && (
            <div className="space-y-5 px-5 pb-5 leading-[150%] sm:px-8 sm:pb-8">
              <h4 className="text-lg font-bold">청약 증거금</h4>
              <div className="space-y-3">
                {myAccount.subscriptionMarginSummary.detail.map((item, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{item.securities.securitiesName}</span>
                    <span className="font-semibold">
                      {CASHCOMMA(item.offeringPrice * item.applyQuantity)}원
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
      </div>
    </div>
  );
};
