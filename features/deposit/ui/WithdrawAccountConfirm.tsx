import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Input } from '@/shared/ui/shadcn/input';
import { Label } from '@/shared/ui/shadcn/label';

import { DepositAccountFormData } from '../lib/depositAccountSchema';
import { formatAccountInfo } from '../lib/formatAccountInfo';

interface WithdrawAccountConfirmProps {
  form: UseFormReturn<DepositAccountFormData>;
  handleCreateVirtualAccount: () => void;
  isOnboarding?: boolean;
  isLoading?: boolean;
}

export const WithdrawAccountConfirm = ({
  form,
  handleCreateVirtualAccount,
  isOnboarding = false,
  isLoading = false,
}: WithdrawAccountConfirmProps) => {
  const { user, isGeneral } = useFetchUser();

  const accountInfo = formatAccountInfo(
    form.watch('bankCode'),
    isGeneral
      ? (user?.userProfile?.name as string)
      : (user?.userCorporateProfile?.representativeName as string),
    form.watch('accountNumber'),
  );

  return (
    <CommonMotionProvider
      className={`mx-auto flex ${isOnboarding && 'h-[calc(100dvh-120px)]'} max-w-screen-test flex-col justify-between py-6 sm:mt-[100px] sm:h-auto sm:justify-start sm:py-0`}
    >
      <div>
        <div className="hidden space-y-4 sm:block">
          <h2 className="text-44">출금 계좌 인증 완료</h2>
          <h4 className="text-lg">
            출금 계좌 인증이 완료되었습니다. <br />
            아래 내역을 확인하신 후 [다음] 버튼을 누르시면 예치금 가상계좌 개설 발급이 완료됩니다.
          </h4>
        </div>
        <div className="space-y-3 sm:hidden">
          <h4 className="text-20">출금계좌 인증이 완료되었습니다.</h4>
          <h5>
            아래 내역을 확인하신 후 [다음] 버튼을 누르시면 예치금 가상계좌 개설 발급이 완료됩니다.
          </h5>
        </div>
        <div className="my-10 sm:my-20">
          <div className="w-full space-y-3">
            <Label className="font-semibold sm:text-base">출금 계좌</Label>
            <Input
              value={accountInfo}
              readOnly
              className="w-full"
              placeholder="(-)없이 계좌번호 입력"
              data-testid="withdraw-account-confirm-account-info"
            />
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 left-0 flex w-full justify-center bg-white px-6 py-2 sm:static">
        <PrimaryButton
          text="다음"
          disabled={isLoading}
          className="!h-12 w-full text-base sm:w-40"
          onClick={handleCreateVirtualAccount}
          data-testid="withdraw-account-confirm-next-button"
        />
      </div>
    </CommonMotionProvider>
  );
};
