import { periodOptions } from '@/shared/config/periodOptions';
import { CommonSearchTabs } from '@/shared/ui/CommonSearchTabs';
import { CustomDayPicker } from '@/shared/ui/CustomDayPicker';
import { SearchButton } from '@/shared/ui/SearchButton';

import { useMyDepositHistorySearch } from '../model/useMyDepositHistorySearch';

export const MyDepositHistorySearchForm = () => {
  const {
    searchFormState,
    handlePeriodChange,
    handleStartDateChange,
    handleEndDateChange,
    handleSearch,
    isDatePickerOpen,
    handleDatePickerClick,
  } = useMyDepositHistorySearch();

  return (
    <form className="flex flex-col items-center gap-2 sm:flex-row" onSubmit={handleSearch}>
      <CommonSearchTabs
        items={periodOptions}
        onChange={handlePeriodChange}
        value={searchFormState.period}
        className="!h-12 w-full sm:w-[320px]"
      />
      <div className="relative flex h-[44px] w-full items-center justify-between gap-4 rounded-lg border border-gray-300 px-4 py-[10px] sm:static sm:!h-12 sm:w-[384px]">
        <CustomDayPicker
          isOpen={isDatePickerOpen.startDate}
          handleDatePickerClick={handleDatePickerClick}
          type="startDate"
          selectedDate={searchFormState.searchBeginAt}
          handleDateChange={handleStartDateChange}
          label="시작 날짜"
          maxDate={searchFormState.searchEndAt}
        />
        <p>~</p>
        <CustomDayPicker
          isOpen={isDatePickerOpen.endDate}
          handleDatePickerClick={handleDatePickerClick}
          type="endDate"
          selectedDate={searchFormState.searchEndAt}
          handleDateChange={handleEndDateChange}
          label="종료 날짜"
          minDate={searchFormState.searchBeginAt}
        />
      </div>
      <SearchButton className="hidden w-full sm:flex sm:w-auto" />
      <SearchButton className="flex w-full sm:hidden" text="적용" />
    </form>
  );
};
