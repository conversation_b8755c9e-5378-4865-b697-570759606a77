import { ArrowPathIcon } from '@heroicons/react/24/outline';

import { DepositTooltip } from '@/features/deposit/ui/DepositTooltip';

import { Account } from '@/entities/assets/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useClipboard } from '@/shared/model/useClipboard';

import { formatAccountNumber } from '../lib/formatAccountNumber';
import { getBankName } from '../lib/getBankName';

interface SubscriptionApplyDepositCardProps {
  account?: Account;
  isLoadingMyAccount: boolean;
  refetchMyAccount: () => void;
}

export const SubscriptionApplyDepositCard = ({
  account,
  isLoadingMyAccount,
  refetchMyAccount,
}: SubscriptionApplyDepositCardProps) => {
  const { handleCopy } = useClipboard();

  const { CASHCOMMA } = utilFormats();

  return (
    <div className="space-y-3 px-6 sm:space-y-3 sm:px-8 ml:px-0">
      <div className="flex items-center justify-between">
        <h4 className="sm:text-20 text-base font-semibold">내 예치금 가상계좌</h4>
        <DepositTooltip />
      </div>
      <div className="space-y-6 rounded-lg border border-gray-300 p-5 sm:p-8">
        <div className="flex gap-[6px] text-sm text-gray-500">
          <p className="underline">
            {getBankName(account?.depositAccountInstCode)}
            {formatAccountNumber(account?.depositAccountNumberCode || '')}
          </p>
          {account?.depositAccountNumberCode && (
            <button onClick={() => handleCopy(account?.depositAccountNumberCode)}>복사</button>
          )}
        </div>
        <div className="space-y-[6px]">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold sm:text-base">내 예치금</span>
            {/* <p className="flex items-center gap-1 text-xs font-semibold text-red-500">
              <ExclamationCircleIcon className="h-4 w-4" />
              충전 후 새로고침 버튼을 눌러 다시 진행해주세요.
            </p> */}
          </div>
          <div className="text-20 sm:text-24 flex items-center gap-2 sm:gap-3">
            <span>{CASHCOMMA(account?.currentNetBalance || 0)}원</span>
            <button onClick={refetchMyAccount}>
              <ArrowPathIcon
                className={`h-5 w-5 text-gray-500 sm:h-6 sm:w-6 ${isLoadingMyAccount && 'animate-spin'}`}
                strokeWidth={2}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
