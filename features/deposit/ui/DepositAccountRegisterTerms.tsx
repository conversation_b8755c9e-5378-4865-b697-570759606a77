'use client';

import { TermsUnderLineButton } from '@/features/auth/ui/TermsUnderLineButton';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

interface DepositAccountRegisterTermsProps {
  handleStep: () => void;
  isOnboarding?: boolean;
  isAgree: boolean;
  toggleAgree: () => void;
}

export const DepositAccountRegisterTerms = ({
  handleStep,
  isOnboarding = false,
  isAgree,
  toggleAgree,
}: DepositAccountRegisterTermsProps) => {
  return (
    <CommonMotionProvider
      className={`max-w-screen-test ${isOnboarding && 'h-[calc(100dvh-120px)]'} mx-auto flex flex-col justify-between py-6 sm:mt-[100px] sm:h-auto sm:justify-start sm:py-0`}
    >
      <div>
        <div className="hidden space-y-4 sm:block">
          <h2 className="text-44">예치금 가상계좌 발급 동의</h2>
          <h4 className="text-lg">
            예치금 계좌는 뉴밋 서비스에서 사용하는 전용 가상 계좌입니다. <br />
            청약 서비스 참여를 위해 예치금 가상계좌 개설 발급에 동의해 주세요.
          </h4>
        </div>
        <div className="space-y-3 sm:hidden">
          <h3 className="text-20">예치금 계좌는 뉴밋 서비스에서 사용하는 전용 가상 계좌입니다.</h3>
          <h5 className="text-sm">
            청약 서비스 참여를 위해 예치금 계좌 개설 발급에 동의해 주세요.
          </h5>
        </div>
        <div className="my-12 flex items-center justify-between sm:my-20">
          <CheckboxField
            label="예치금 가상계좌 발급 및 제3자 개인정보 제공 약관 동의"
            value="isAgree"
            data-testid="deposit-account-register-terms-checkbox"
            checked={isAgree}
            onCheckedChange={toggleAgree}
            required
          />
          <TermsUnderLineButton type="terms" />
        </div>
      </div>
      <div className="fixed bottom-0 left-0 flex w-full justify-center bg-white px-6 py-2 sm:static">
        <PrimaryButton
          text="다음"
          onClick={handleStep}
          className="!h-12 w-full text-base sm:w-40"
          disabled={!isAgree}
          data-testid="deposit-account-register-terms-next-button"
        />
      </div>
    </CommonMotionProvider>
  );
};
