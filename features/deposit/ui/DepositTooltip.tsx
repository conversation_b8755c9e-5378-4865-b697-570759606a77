import { InformationCircleIcon } from '@heroicons/react/24/solid';

import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/ui/shadcn/tooltip';

export const DepositTooltip = () => {
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger>
          <div className="flex items-center gap-1 text-gray-600">
            <InformationCircleIcon className="h-4 w-4" />
            <p className="text-sm sm:text-base">예치금 출금 정책</p>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="max-w-[400px] bg-gray-900 px-[22px] py-4 text-sm text-white"
        >
          <ol className="list-outside list-disc pl-5">
            <li>
              보이스피싱 등 전자금융 사기 피해 방지를 위하여 예치금이 입금된 시간 기준 24시간
              이후부터 출금이 가능합니다.
            </li>
            <li>출금 수수료 정책 ~</li>
            <li>23:20 ~ 00:40분 사이에는 은행망 점검시간으로 이체가 불가할 수 있습니다.</li>
          </ol>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
