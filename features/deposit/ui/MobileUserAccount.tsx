import { ArrowPathIcon, Square2StackIcon } from '@heroicons/react/24/outline';
import { ChevronRightIcon, InformationCircleIcon } from '@heroicons/react/24/solid';

import { utilFormats } from '@/shared/lib/utilformats';
import { useClipboard } from '@/shared/model/useClipboard';
import { Separator } from '@/shared/ui/shadcn/separator';

import { formatAccountNumber } from '../lib/formatAccountNumber';
import { getBankName } from '../lib/getBankName';
import { useMobileUserAccount } from '../model/useMobileUserAccount';

export const MobileUserAccount = ({ className }: { className?: string }) => {
  const { account, isLoadingMyAccount, handleWithdraw, refetchAccount } = useMobileUserAccount();

  const { CASHCOMMA } = utilFormats();
  const { handleCopy } = useClipboard();

  const isNotLockedAmount = account?.subscriptionMarginSummary.totalLockedMargin === 0;

  return (
    <section className={className}>
      <div className="rounded-lg bg-custom-gradient px-5 pt-5">
        <div className="h-[42px] space-y-1">
          <p className="text-sm font-semibold">나의 예치금 가상계좌</p>
          <div className="flex items-center gap-1 text-xs opacity-60">
            <p>
              {getBankName(account?.depositAccountInstCode)}{' '}
              {formatAccountNumber(account?.depositAccountNumberCode || '')}
            </p>
            {account && (
              <button onClick={() => handleCopy(account.depositAccountNumberCode)}>
                <Square2StackIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
        <div className="mb-[18px] mt-3 flex items-center gap-2">
          <p className="text-24">{CASHCOMMA(account?.currentBalance || 0)}원</p>
          <button onClick={() => refetchAccount()} disabled={isLoadingMyAccount}>
            <ArrowPathIcon
              strokeWidth={2}
              className={`h-4 w-4 text-black/30 ${isLoadingMyAccount && 'animate-spin'}`}
            />
          </button>
        </div>
        {!isNotLockedAmount && <Separator className="mt-[18px] bg-black/10" />}
        {account?.subscriptionMarginSummary.detail &&
          account.subscriptionMarginSummary.detail.length > 0 && (
            <div className="my-[18px] space-y-[10px] leading-[150%]">
              <h4 className="text-base font-semibold">청약 증거금</h4>
              <div className="flex gap-3">
                <Separator
                  orientation="vertical"
                  className={`h-[${account.subscriptionMarginSummary.detail.length * 25}px] !w-[3px] bg-black/10`}
                />
                <div className="flex-1 space-y-[10px] leading-[150%]">
                  {account.subscriptionMarginSummary.detail.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>{item.securities.securitiesName}</span>
                      <span className="font-semibold">
                        {CASHCOMMA(item.offeringPrice * item.applyQuantity)}원
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

        <button
          onClick={handleWithdraw}
          className="w-full border-t border-black/10 px-4 py-[14px] font-semibold text-primary-500"
          disabled={isLoadingMyAccount}
        >
          출금하기
        </button>
      </div>
    </section>
  );
};
