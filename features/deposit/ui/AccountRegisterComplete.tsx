import React from 'react';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

import { formatAccountInfo } from '../lib/formatAccountInfo';

interface AccountRegisterCompleteProps {
  virtualAccount: {
    depositAccountInstCode: string;
    depositAccountNumberCode: string;
    depositorName: string;
  };
  goToCallbackUrl: () => void;
  isOnboarding?: boolean;
  isChange?: boolean;
}

export const AccountRegisterComplete = ({
  virtualAccount,
  goToCallbackUrl,
  isOnboarding = false,
  isChange = false,
}: AccountRegisterCompleteProps) => {
  const formatAccount = formatAccountInfo(
    virtualAccount.depositAccountInstCode,
    virtualAccount.depositorName,
    virtualAccount.depositAccountNumberCode,
  );

  return (
    <CommonMotionProvider
      className={`mx-auto ${isOnboarding ? 'h-[calc(100dvh-150px)]' : 'mt-[200px]'} flex max-w-screen-test flex-col items-center justify-center sm:justify-start`}
    >
      <div className="flex flex-col items-center">
        <FallbackImage src="/icons/complete.png" alt="complete" width={80} height={80} />
        <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">
          {isChange ? '출금계좌 변경 완료' : '예치금 가상계좌 개설 발급 완료'}
        </h3>
        <h6 className="whitespace-pre-line text-center text-base sm:text-lg">
          {isChange ? '출금계좌 변경이 완료되었습니다.' : '예치금 가상계좌 개설이 완료되었습니다.'}
        </h6>
        <div className="mt-10 flex w-full max-w-[480px] flex-col items-center justify-between gap-2 rounded-[10px] border border-gray-300 bg-gray-50 p-7 sm:mt-14 sm:flex-row sm:items-start sm:gap-0">
          <p className="text-sm font-semibold sm:text-base">
            {isChange ? '출금계좌' : '발급된 예치금 계좌'}
          </p>
          <p className="text-center">{formatAccount}</p>
        </div>
      </div>
      {!isOnboarding && (
        <div className="fixed bottom-0 mt-0 flex w-full justify-center px-6 py-2 sm:mt-0 sm:hidden">
          <PrimaryButton
            text="확인"
            className="!h-12 w-full text-base sm:w-40"
            onClick={goToCallbackUrl}
          />
        </div>
      )}
      <div className="mt-16 hidden w-full justify-center px-6 sm:flex">
        <PrimaryButton text="완료" className="w-full sm:w-40" onClick={goToCallbackUrl} />
      </div>
    </CommonMotionProvider>
  );
};
