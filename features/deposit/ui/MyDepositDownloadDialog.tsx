import {
  Description,
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import { Fragment } from 'react';

import { periodOptions } from '@/shared/config/periodOptions';
import { CommonSearchTabs } from '@/shared/ui/CommonSearchTabs';
import { CustomDayPicker } from '@/shared/ui/CustomDayPicker';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

import { useMyDepositDownloadDialog } from '../model/useMyDepositDownloadDialog';

interface MyDepositDownloadDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  title?: string;
  description?: string;
  caption?: string;
  handleAction?: () => void;
  text?: string;
  isCancelButton?: boolean;
}

export const MyDepositDownloadDialog = ({
  isOpen,
  handleOpen,
  handleAction,
}: MyDepositDownloadDialogProps) => {
  const {
    period,
    selectDate,
    isDatePickerOpen,
    handlePeriodChange,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
  } = useMyDepositDownloadDialog();

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog open={isOpen} onClose={handleOpen} className="relative z-50">
        <DialogBackdrop className="fixed inset-0 bg-black/80" />
        <div className="fixed inset-0 flex items-center justify-center px-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <DialogPanel className="w-full max-w-[540px] items-center rounded-[16px] border-none bg-white p-6 data-[closed]:duration-500 data-[open]:duration-700 data-[open]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[open]:fade-in-0 sm:min-w-[540px] sm:rounded-[30px] sm:p-3 sm:px-10">
              <div className="flex justify-end">
                <button
                  onClick={handleOpen}
                  className="flex h-5 w-5 items-center justify-center rounded-full sm:h-10 sm:w-10 sm:p-0"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              <div className="mb-8 flex w-full flex-col gap-3">
                <DialogTitle className="text-20 whitespace-pre-line text-center">
                  거래내역 다운로드
                </DialogTitle>
                <Description className="w-full text-center text-base">
                  설정된 기간으로 거래내역을 다운로드 받으실 수 있습니다.
                </Description>
              </div>
              <div className="space-y-2">
                <CommonSearchTabs
                  items={periodOptions}
                  onChange={handlePeriodChange}
                  value={period}
                  className="!h-12 w-full"
                />
                <div className="relative flex h-[44px] w-full items-center justify-between gap-4 rounded-lg border border-gray-300 px-4 py-[10px] sm:static sm:!h-12">
                  <CustomDayPicker
                    isOpen={isDatePickerOpen.startDate}
                    handleDatePickerClick={handleDatePickerClick}
                    type="startDate"
                    selectedDate={selectDate.startDate}
                    handleDateChange={handleStartDateChange}
                    label="시작 날짜"
                    minDate={new Date(
                      new Date().setFullYear(new Date().getFullYear() - 1),
                    ).toISOString()}
                    maxDate={selectDate.endDate}
                  />
                  <p>~</p>
                  <CustomDayPicker
                    isOpen={isDatePickerOpen.endDate}
                    handleDatePickerClick={handleDatePickerClick}
                    type="endDate"
                    selectedDate={selectDate.endDate}
                    handleDateChange={handleEndDateChange}
                    label="종료 날짜"
                    minDate={selectDate.startDate}
                  />
                </div>
              </div>
              <ol className="mb-10 mt-8 list-outside list-disc space-y-1 pl-4 text-sm leading-[170%]">
                <li>증명서로 사용하실 수 없는 참고용 엑셀 파일입니다.</li>
                <li>제출 용도로 사용하시려면 고객지원을 통해 문의해주세요.</li>
                <li>다운로드가 가능한 조회 기간은 최대 1년입니다.</li>
              </ol>

              <div className="mb-4 flex justify-center gap-2">
                <PrimaryButton
                  type="button"
                  className="!h-[44px] w-full text-sm font-semibold"
                  onClick={handleAction}
                  text="다운로드"
                />
              </div>
            </DialogPanel>
          </motion.div>
        </div>
      </Dialog>
    </Transition>
  );
};
