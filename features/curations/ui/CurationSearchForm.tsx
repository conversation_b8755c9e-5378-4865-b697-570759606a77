import { useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import { SearchButton } from '@/shared/ui/SearchButton';
import { Input } from '@/shared/ui/shadcn/input';

export const CurationSearchForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [keyword, setKeyword] = useState(searchParams.get('keyword') || '');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const searchParams = new URLSearchParams();
    if (keyword) {
      searchParams.set('keyword', keyword);
    } else {
      searchParams.delete('keyword');
    }

    router.replace(`/contents/curations?${searchParams.toString()}`);
  };

  return (
    <form className="flex w-full items-center gap-2 sm:w-auto" onSubmit={handleSubmit}>
      <Input
        name="keyword"
        value={keyword}
        placeholder="검색어를 입력해주세요."
        className="w-full sm:w-[300px]"
        onChange={(e) => setKeyword(e.target.value)}
      />
      <SearchButton />
    </form>
  );
};
