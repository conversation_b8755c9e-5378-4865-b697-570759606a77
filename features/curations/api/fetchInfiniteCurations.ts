import { useSuspenseInfiniteQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { CurationListRequest } from '@/entities/curations/interface';
import { Curation } from '@/entities/curations/types';

import { ScrollApiResponse } from '@/shared/interface';

export const fetchInfiniteCurations = (params: CurationListRequest) => {
  return useSuspenseInfiniteQuery({
    ...queries.curations.infinite(params),
    initialPageParam: 1,
    getNextPageParam: (lastPage: ScrollApiResponse<Curation>) => {
      if (!lastPage.isLast) return lastPage.nextPage;
      return null;
    },
  });
};
