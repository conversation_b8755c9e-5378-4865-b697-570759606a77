import { useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { CurationListRequest } from '@/entities/curations/interface';
import { Curation } from '@/entities/curations/types';

import { ListResponse } from '@/shared/interface';

export const fetchCurations = (filter: CurationListRequest, curations: ListResponse<Curation>) => {
  return useQuery({
    ...queries.curations.list(filter),
    initialData: curations,
  });
};
