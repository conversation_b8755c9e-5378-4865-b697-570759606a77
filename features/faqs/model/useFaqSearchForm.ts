'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { FaqCategory } from '@/entities/faqs/types';

export const useFaqSearchForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [selectedCategory, setSelectedCategory] = useState<FaqCategory | null>(
    searchParams.get('category') as FaqCategory | null,
  );
  const [searchState, setSearchState] = useState({
    type: searchParams.get('type') || 'title',
    keyword: searchParams.get('keyword') || '',
  });

  const handleSearchState = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchState({ ...searchState, [e.target.name]: e.target.value });
  };
  const handleCategoryChange = (value: FaqCategory | null) => setSelectedCategory(value);

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const searchParams = new URLSearchParams();

    if (selectedCategory) {
      searchParams.set('category', selectedCategory);
    }

    if (searchState.type) {
      searchParams.set('type', searchState.type);
    }

    if (searchState.keyword) {
      searchParams.set('keyword', searchState.keyword);
    }

    router.replace(`${pathname}?${searchParams.toString()}`);
  };

  const handleSearchTypeChange = (value: string) => {
    setSearchState({ ...searchState, type: value });
  };

  useEffect(() => {
    const locationParams = new URLSearchParams(window.location.search);
    const currentCategory = locationParams.get('category') as FaqCategory | null;
    if (JSON.stringify(currentCategory) !== JSON.stringify(selectedCategory)) {
      locationParams.delete('category');

      if (selectedCategory) {
        locationParams.append('category', selectedCategory);
      }

      const newUrl = `${window.location.pathname}?${locationParams.toString()}`;
      router.replace(newUrl, {
        scroll: false,
      });
    }
  }, [selectedCategory]);

  return {
    searchState,
    handleSearchState,
    handleSearch,
    handleCategoryChange,
    selectedCategory,
    handleSearchTypeChange,
  };
};
