'use client';

import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

import { FaqCategory, FaqCategoryLabel } from '@/entities/faqs/types';

import { defaultSearchOptions } from '@/shared/config/searchOptions';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { CommonTabButton } from '@/shared/ui/CommonTabButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Input } from '@/shared/ui/shadcn/input';

import { useFaqSearchForm } from '../model/useFaqSearchForm';

const CategoryTabs = [
  {
    label: '전체',
    value: null,
  },
  {
    label: FaqCategoryLabel[FaqCategory.SERVICE],
    value: FaqCategory.SERVICE,
  },
  {
    label: FaqCategoryLabel[FaqCategory.ACCOUNT],
    value: FaqCategory.ACCOUNT,
  },
  {
    label: FaqCategoryLabel[FaqCategory.OTHER],
    value: FaqCategory.OTHER,
  },
];

export const FaqSearchForm = () => {
  const {
    searchState,
    handleSearchState,
    handleSearch,
    handleCategoryChange,
    handleSearchTypeChange,
    selectedCategory,
  } = useFaqSearchForm();

  return (
    <form className="mb-16 flex h-12 flex-col justify-between ml:flex-row" onSubmit={handleSearch}>
      <div className="mb-6 flex space-x-2 ml:mb-0">
        {CategoryTabs.map((tab) => (
          <CommonTabButton
            key={tab.value}
            label={tab.label}
            value={tab.value as string}
            isSelected={selectedCategory === tab.value}
            handleAction={handleCategoryChange}
          />
        ))}
      </div>
      <div className="flex flex-col gap-2 sm:flex-row">
        <CommonSelect
          value={searchState.type}
          onChange={handleSearchTypeChange}
          options={defaultSearchOptions}
        />
        <div className="flex gap-2">
          <Input
            value={searchState.keyword}
            name="keyword"
            onChange={handleSearchState}
            className="h-[44px] w-full rounded-lg border border-gray-300 bg-white sm:h-12 sm:min-w-[300px]"
            placeholder="검색어를 입력해주세요"
          />
          <Button className="h-[44px] min-w-[60px] bg-gray-900 shadow-none sm:h-12">
            <MagnifyingGlassIcon className="h-5 w-5 text-white" />
          </Button>
        </div>
      </div>
    </form>
  );
};
