import { ChevronDownIcon } from '@heroicons/react/24/solid';
import 'ckeditor5/ckeditor5.css';
import { motion } from 'framer-motion';
import React from 'react';

import { Faq, FaqCategoryLabel } from '@/entities/faqs/types';

import { cn } from '@/shared/lib/utils';

interface FaqCardProps {
  faq: Faq;
  isLast: boolean;
  handleFaqClick: (faqId?: string) => void;
  selectedFaq: string | null;
  isInquiry?: boolean;
}

export const FaqCard = ({
  faq,
  isLast,
  handleFaqClick,
  selectedFaq,
  isInquiry = false,
}: FaqCardProps) => {
  const isVisible = isInquiry ? selectedFaq === faq.id : selectedFaq === faq._id;

  return (
    <div className={cn('border-t border-gray-300 sm:px-4 ml:px-10', isLast && 'border-b')}>
      <div
        className="my-6 flex cursor-pointer items-center justify-between sm:my-10"
        onClick={() => {
          if (isInquiry) {
            handleFaqClick(selectedFaq === faq.id ? undefined : faq.id);
          } else {
            handleFaqClick(selectedFaq === faq._id ? undefined : faq._id);
          }
        }}
      >
        <div className="flex items-center gap-3 sm:gap-5">
          <div className="flex items-center sm:min-w-20">
            <span className="rounded bg-gray-200 px-[6px] py-1 text-[10px] font-semibold leading-[15px] sm:px-2 sm:py-[3px] sm:text-xs sm:leading-[18px] ml:rounded-[24px]">
              {FaqCategoryLabel[faq.category]}
            </span>
          </div>

          <p className="line-clamp-1 text-sm font-semibold">{faq.title}</p>
        </div>

        <button className="transition-transform duration-300 ease-in-out">
          <ChevronDownIcon
            className={`h-5 w-5 transform transition-transform duration-300 ease-in-out ${
              isVisible ? 'rotate-180' : 'rotate-0'
            }`}
          />
        </button>
      </div>

      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
          className="mb-10 rounded-lg bg-blue-gray-00 px-5 py-6 sm:p-10"
        >
          <div
            dangerouslySetInnerHTML={{ __html: faq.content }}
            className="ck-content text-gray-700"
          />
        </motion.div>
      )}
    </div>
  );
};
