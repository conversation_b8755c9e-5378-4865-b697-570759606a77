import { ArrowLeftIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Button } from '../../../shared/ui/shadcn/button';

interface CarouselLeftButtonProps {
  handlePrev: () => void;
  className?: string;
  disabled?: boolean;
  'aria-label': string;
}

export const CarouselLeftButton = ({
  disabled,
  handlePrev,
  className,
  'aria-label': ariaLabel,
}: CarouselLeftButtonProps) => {
  return (
    <Button
      aria-label={ariaLabel}
      className={`${className} !h-[36px] !w-[36px] rounded-full bg-white hover:bg-gray-900 hover:text-white ml:!h-14 ml:!w-14 ${className}`}
      disabled={disabled}
      onClick={handlePrev}
    >
      <ArrowLeftIcon className="h-4 w-4 ml:!h-6 ml:!w-6" />
    </Button>
  );
};
