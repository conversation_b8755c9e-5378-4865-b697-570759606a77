import { Description, Dialog, DialogPanel, DialogTitle, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { Fragment } from 'react';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { But<PERSON> } from '@/shared/ui/shadcn/button';
import { Input } from '@/shared/ui/shadcn/input';

import { useNewsletterForm } from '../model/useNewsletterForm';

interface NewsletterFormDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
}

export const NewsletterFormDialog = ({ isOpen, handleOpen }: NewsletterFormDialogProps) => {
  const { handleSubscribe, enableCondition, email, isAgree, handleEmailChange, handleAgreeChange } =
    useNewsletterForm({ isOpen, handleOpen });

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog open={isOpen} onClose={handleOpen} className="relative z-50">
        {/* <DialogBackdrop className="fixed inset-0 bg-black/80" /> */}
        <div className="fixed bottom-5 right-0 px-4 sm:bottom-10 sm:px-10">
          <DialogPanel className="w-full max-w-[730px] items-center rounded-[16px] border-none bg-gray-900 p-3 text-white data-[closed]:duration-500 data-[open]:duration-700 data-[open]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[open]:fade-in-0 data-[closed]:zoom-out-95 data-[open]:zoom-in-95 sm:rounded-[30px] sm:p-3">
            <div className="flex justify-end">
              <button
                onClick={handleOpen}
                className="h-5 w-5 rounded-full bg-gray-900 sm:h-[52px] sm:w-[52px] sm:p-0"
              >
                <XMarkIcon className="h-4 w-4 sm:h-6 sm:w-6" />
              </button>
            </div>
            <div className="flex flex-col gap-2 px-6 sm:items-center sm:gap-4 sm:px-[55px] ml:px-[88px]">
              <DialogTitle className="ml:text-40 sm:text-32 text-18">
                뉴밋의 새로운 소식을 받아보세요!
              </DialogTitle>
              <Description className="text-center text-sm sm:text-lg">
                뉴밋과 관련된 새로운 소식과 다양한 정보를 <br className="hidden sm:block" /> 가장
                빠르게 만나보실 수 있습니다.
              </Description>
            </div>
            <div className="mt-7 flex flex-col gap-[14px] px-6 pb-5 sm:mt-[50px] sm:gap-6 sm:pb-[64px]">
              <div className="relative mx-auto w-full max-w-[520px]">
                <Input
                  value={email}
                  onChange={handleEmailChange}
                  placeholder="이메일을 입력해주세요."
                  className="h-12 bg-white text-black placeholder:text-gray-500 sm:h-[60px]"
                />
                <Button
                  onClick={handleSubscribe}
                  className="absolute right-[6px] top-[6px] !h-[36px] bg-gray-900 text-xs disabled:bg-gray-400 sm:right-[10px] sm:top-3 sm:text-base"
                  disabled={!enableCondition}
                >
                  소식 받기
                </Button>
              </div>
              <div className="flex flex-col justify-center gap-[6px] sm:flex-row sm:gap-5">
                <CheckboxField
                  label="개인정보 수집 이용 약관 동의"
                  value="personalInfo"
                  checked={isAgree.personalInfo}
                  onCheckedChange={() => handleAgreeChange('personalInfo')}
                  required
                  linkType="privacy"
                  size="sm"
                />
                <CheckboxField
                  label="광고성 정보수신 동의"
                  value="advertisement"
                  checked={isAgree.advertisement}
                  required
                  linkType="marketing"
                  size="sm"
                  onCheckedChange={() => handleAgreeChange('advertisement')}
                />
              </div>
            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </Transition>
  );
};
