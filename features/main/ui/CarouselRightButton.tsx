import { ArrowRightIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Button } from '../../../shared/ui/shadcn/button';

interface CarouselRightButtonProps {
  disabled?: boolean;
  handleNext: () => void;
  className?: string;
  'aria-label': string;
}

export const CarouselRightButton = ({
  disabled,
  handleNext,
  className,
  'aria-label': ariaLabel,
}: CarouselRightButtonProps) => {
  return (
    <Button
      aria-label={ariaLabel}
      className={`${className} !h-[36px] !w-[36px] rounded-full bg-white hover:bg-gray-900 hover:text-white ml:!h-14 ml:!w-14 ${className}`}
      disabled={disabled}
      onClick={handleNext}
    >
      <ArrowRightIcon className="h-4 w-4 ml:!h-6 ml:!w-6" />
    </Button>
  );
};
