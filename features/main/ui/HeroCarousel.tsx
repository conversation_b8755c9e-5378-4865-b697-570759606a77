import { Autoplay, Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperClass, SwiperSlide } from 'swiper/react';

import { BannerItem } from '@/entities/banners/types';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';

interface HeroCarouselProps {
  swiper?: SwiperClass;
  handleSlideChange: (swiperInstance: SwiperClass) => void;
  setSwiper: (swiperInstance: SwiperClass) => void;
  banners: BannerItem[];
  currentIndex: number;
}

export const HeroCarousel = ({
  handleSlideChange,
  setSwiper,
  banners,
  swiper,
  currentIndex,
}: HeroCarouselProps) => {
  const { routerPush } = useWebViewRouter();

  return (
    <Swiper
      spaceBetween={10}
      slidesPerView={1}
      modules={[Pagination, Navigation, Autoplay]}
      onSlideChange={(swiperInstance) => handleSlideChange(swiperInstance)}
      navigation
      loop={banners.length > 1}
      onSwiper={(e) => {
        setSwiper(e);
      }}
      autoplay={{
        delay: 5000,
        disableOnInteraction: false,
      }}
      grabCursor={true}
      touchMoveStopPropagation={true}
      className="rounded-[10px] sm:rounded-[20px] ml:rounded-[30px]"
    >
      {banners?.map((banner, index) => (
        <SwiperSlide key={index}>
          {banner.link ? (
            <div onClick={() => routerPush(banner.link)} className="cursor-pointer">
              <div className="relative hidden aspect-[23/8] sm:block">
                <FallbackImage
                  src={banner.image.url}
                  alt={banner.title}
                  fill
                  style={{ objectFit: 'cover' }}
                  quality={100}
                  priority
                  sizes="100vw, (min-width: 1940px) 1840px"
                  className="rounded-[20px] ml:rounded-[30px]"
                />
              </div>
              <div className="relative aspect-[327/340] sm:hidden">
                <FallbackImage
                  src={banner.mobileImage?.url || banner.image.url}
                  alt={banner.title}
                  style={{ objectFit: 'cover' }}
                  fill
                  quality={100}
                  priority
                  sizes="(max-width: 996px) 100vw, 924px"
                  className="rounded-[10px]"
                />
              </div>
            </div>
          ) : (
            <div>
              <div className="relative hidden aspect-[23/8] sm:block">
                <FallbackImage
                  src={banner.image.url}
                  alt={banner.title}
                  fill
                  style={{ objectFit: 'cover' }}
                  quality={100}
                  priority
                  sizes="100vw, (min-width: 1940px) 1840px"
                  className="rounded-[20px] ml:rounded-[30px]"
                />
              </div>
              <div className="relative aspect-[327/340] sm:hidden">
                <FallbackImage
                  src={banner.mobileImage?.url || banner.image.url}
                  alt={banner.title}
                  style={{ objectFit: 'cover' }}
                  fill
                  quality={100}
                  priority
                  sizes="(max-width: 996px) 100vw, 924px"
                  className="rounded-[10px]"
                />
              </div>
            </div>
          )}
        </SwiperSlide>
      ))}
      <div className="absolute bottom-3 left-1/2 z-20 flex h-[30px] w-[70px] -translate-x-1/2 items-center justify-center rounded-[60px] bg-black/20 ml:bottom-8">
        <div className="flex items-center gap-2 text-sm font-semibold text-white/50">
          <span className="text-white">{currentIndex + 1}</span> <span>∙</span>
          <span className="text-white/50">{swiper?.slides.length}</span>
        </div>
      </div>
    </Swiper>
  );
};
