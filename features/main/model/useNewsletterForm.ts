import { AxiosError } from 'axios';
import { useEffect, useMemo, useState } from 'react';

import { subscribeNewsletter } from '@/entities/users/api/subscribeNewsletter';

import { useToast } from '@/shared/model/useToast';

interface NewsletterFormProps {
  isOpen: boolean;
  handleOpen: () => void;
}

export const useNewsletterForm = ({ isOpen, handleOpen }: NewsletterFormProps) => {
  const [email, setEmail] = useState('');
  const [isAgree, setIsAgree] = useState({
    personalInfo: false,
    advertisement: false,
  });
  const { successToast, errorToast } = useToast();

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleAgreeChange = (type: 'personalInfo' | 'advertisement') => {
    setIsAgree((prev) => ({ ...prev, [type]: !prev[type] }));
  };

  const handleSubscribe = async () => {
    try {
      const response: any = await subscribeNewsletter(email);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        successToast({
          title: '이메일이 전송되었습니다. 메일함을 확인해주세요',
        });
      }
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 409) {
          errorToast({
            title: '이미 구독하신 이메일입니다.',
          });
        }
      } else {
        errorToast({
          title: '구독에 실패했습니다.',
        });
      }
    }
  };

  const cleanUp = () => {
    setEmail('');
    setIsAgree({
      personalInfo: false,
      advertisement: false,
    });
  };

  const enableCondition = useMemo(() => {
    return (
      email &&
      isAgree.personalInfo &&
      isAgree.advertisement &&
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(email)
    );
  }, [email, isAgree]);

  useEffect(() => {
    if (!isOpen) {
      cleanUp();
    }

    return () => {
      cleanUp();
    };
  }, [isOpen]);

  return {
    handleSubscribe,
    enableCondition,
    email,
    isAgree,
    handleEmailChange,
    handleAgreeChange,
  };
};
