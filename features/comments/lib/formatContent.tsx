import { urlRegex } from '@/shared/lib/regex';

export const formatContent = (content: string) => {
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;

  content.replace(urlRegex, (match, url, _, offset) => {
    if (lastIndex < offset) {
      parts.push(content.slice(lastIndex, offset));
    }

    const href = match.startsWith('http') ? match : `https://${match}`;
    parts.push(
      <a
        key={offset}
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="font-medium text-blue-600 hover:underline"
      >
        {match}
      </a>,
    );

    lastIndex = offset + match.length;
    return match;
  });

  if (lastIndex < content.length) {
    parts.push(content.slice(lastIndex));
  }

  return <>{parts}</>;
};
