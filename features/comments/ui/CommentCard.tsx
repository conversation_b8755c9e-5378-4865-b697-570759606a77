import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { Comment } from '@/entities/comments/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useVisibility } from '@/shared/model/useVisibility';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { Separator } from '@/shared/ui/shadcn/separator';

import { formatContent } from '../lib/formatContent';
import { maskEmail } from '../lib/maskEmail';
import { CommentForm } from './CommentForm';

interface CommentCardProps {
  isFirst: boolean;
  comment: Comment;
  handleUpdate: (commentId: string, comment: string) => void;
  handleDelete: (commentId: string) => void;
  isLoading: boolean;
  isDone?: boolean;
}

export const CommentCard = ({
  isFirst,
  comment,
  handleUpdate,
  handleDelete,
  isLoading,
  isDone,
}: CommentCardProps) => {
  const { isVisible: isDelete, toggleVisibility: toggleDelete } = useVisibility();
  const { isVisible: isEdit, toggleVisibility: toggleEdit } = useVisibility();

  const { user } = useFetchUser();

  const isAuthor = user?.userProfile?.userId === comment.userId;

  const commentId = comment._id;

  const { YYYYMMDD } = utilFormats();

  const updateComment = (newComment: string) => {
    handleUpdate(commentId, newComment);
    toggleEdit();
  };

  const deleteComment = () => {
    handleDelete(commentId);
    toggleDelete();
  };

  return (
    <>
      <div
        className={`flex justify-between divide-gray-300 border-b py-4 sm:py-7 ${isFirst && 'border-t'}`}
      >
        <div className="flex-1 space-y-2 sm:space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-[10px] sm:gap-4 ml:gap-6">
              <p className="text-xs font-semibold sm:text-sm">{maskEmail(comment.email)}</p>
              <Separator orientation="vertical" className="h-[10px] bg-gray-300 sm:h-4" />
              <span className="text-xs text-gray-700 sm:text-sm">
                {YYYYMMDD(comment.createdAt)}
              </span>
            </div>
            {isEdit && (
              <button
                onClick={toggleEdit}
                className="text-xs font-semibold text-gray-500 underline sm:text-sm"
              >
                취소
              </button>
            )}
          </div>
          {isEdit ? (
            <AnimatePresence mode="wait">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
              >
                <CommentForm
                  initialComment={comment.content}
                  onSubmit={updateComment}
                  isLoading={isLoading}
                />
              </motion.div>
            </AnimatePresence>
          ) : (
            <p className="whitespace-pre-wrap break-all text-sm sm:text-base">
              {formatContent(comment.content)}
            </p>
          )}
        </div>
        {isAuthor && !isEdit && !isDone && (
          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center gap-[10px] sm:gap-4 ml:gap-6">
              <button
                onClick={toggleEdit}
                className="text-xs font-semibold text-gray-500 sm:text-sm"
              >
                수정
              </button>
              <Separator orientation="vertical" className="h-4 bg-gray-300" />
              <button
                onClick={toggleDelete}
                className="text-xs font-semibold text-gray-500 sm:text-sm"
              >
                삭제
              </button>
            </div>
          </div>
        )}
      </div>
      <ConfirmDialog
        isOpen={isDelete}
        handleOpen={toggleDelete}
        title="댓글 삭제"
        description="댓글을 삭제하시겠습니까?"
        handleAction={deleteComment}
      />
    </>
  );
};
