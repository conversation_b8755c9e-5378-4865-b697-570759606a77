import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Textarea } from '@/shared/ui/shadcn/textarea';

import { useCommentForm } from '../model/useCommentForm';

interface CommentFormProps {
  initialComment?: string;
  onSubmit: (comment: string) => void;
  isLoading: boolean;
}

export const CommentForm = ({ initialComment, onSubmit, isLoading }: CommentFormProps) => {
  const { routerPush } = useWebViewRouter();
  const { comment, handleInput, handleSubmit, handleFocus, isVisible, toggleVisibility } =
    useCommentForm(initialComment || '', onSubmit);

  const handleLogin = () => {
    routerPush('/sign-in');
  };

  return (
    <form
      className={`flex flex-col gap-2 sm:flex-row sm:gap-3 ${initialComment ? 'sm:h-[100px]' : 'sm:h-[154px]'}`}
      onSubmit={handleSubmit}
    >
      <div className="h-full flex-1 rounded-lg border border-gray-300">
        <Textarea
          value={comment}
          onChange={handleInput}
          onFocus={handleFocus}
          placeholder="10자 이상으로 댓글을 입력해주세요. 이벤트와 무관하거나 이용약관에 위배되는 내용은 삭제될 수 있습니다."
          className={`resize-none border-none px-3 pt-[12px] shadow-none placeholder:text-sm placeholder:text-gray-500 disabled:bg-white placeholder:sm:text-base ${
            initialComment ? 'h-[48px] sm:h-[70px]' : 'h-[108px] sm:h-[120px]'
          }`}
        />
        <div className="mb-3 mr-4 flex justify-end sm:mt-1">
          <span className="text-xs text-gray-500">({comment.length} / 500자)</span>
        </div>
      </div>
      <SecondaryButton
        text="등록"
        className={`w-full sm:w-[120px] ${initialComment ? 'sm:!h-[100px]' : 'sm:!h-[154px]'}`}
        type="submit"
        disabled={isLoading || comment.length < 10}
      />
      <ConfirmDialog
        isOpen={isVisible}
        handleOpen={toggleVisibility}
        title="로그인 필요"
        description={`로그인이 필요한 서비스입니다. \n 로그인하시겠습니까?`}
        handleAction={handleLogin}
        isCancelButton
      />
    </form>
  );
};
