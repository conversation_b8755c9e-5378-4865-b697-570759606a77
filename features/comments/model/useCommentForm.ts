import { useSession } from 'next-auth/react';
import React, { useState } from 'react';

import { useVisibility } from '@/shared/model/useVisibility';

export const useCommentForm = (initialComment: string, onSubmit: (comment: string) => void) => {
  const [comment, setComment] = useState(initialComment || '');
  const { isVisible, toggleVisibility } = useVisibility();
  const { data } = useSession();

  const handleFocus = () => {
    if (!data?.user) {
      toggleVisibility();
    }
  };

  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length > 500) return;

    setComment(value);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    if (comment.length < 10) return;

    e.preventDefault();

    onSubmit(comment);
    setComment('');
  };

  return { comment, handleInput, handleSubmit, handleFocus, isVisible, toggleVisibility };
};
