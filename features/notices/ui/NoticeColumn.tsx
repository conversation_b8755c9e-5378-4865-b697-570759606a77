import { ColumnDef } from '@tanstack/react-table';

import { Notice } from '@/entities/notices/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { NewBadge } from '@/shared/ui/NewBadge';

const { YYYYMMDD } = utilFormats();

export const NoticeColumn: ColumnDef<Notice>[] = [
  {
    accessorKey: 'title',
    size: 1000,
    header: () => <p className="flex-1">제목</p>,
    cell: ({ row }) => {
      return (
        <div className="line-clamp-1 flex flex-1 items-center gap-[6px] text-xl font-bold">
          {row.original.title}
          <NewBadge size="md" date={row.original.createdAt} />
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    size: 120,
    header: () => <p className="w-[120px] shrink-0 text-right">작성일</p>,
    cell: ({ row }) => {
      return <p className="w-[120px] shrink-0 text-right">{YYYYMMDD(row.original.createdAt)}</p>;
    },
  },
];
