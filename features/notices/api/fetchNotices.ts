import { useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { Notice } from '@/entities/notices/types';

import { ListResponse } from '@/shared/interface';

import { NoticeListRequest } from '../interface';

export const fetchNotices = (filters: NoticeListRequest, notices?: ListResponse<Notice>) => {
  return useQuery({
    ...queries.notices.list(filters),
    initialData: notices,
  });
};
