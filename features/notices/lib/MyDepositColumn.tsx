import { ColumnDef } from '@tanstack/react-table';

import { MyDepositHistory } from '@/entities/assets/types';

import { utilFormats } from '@/shared/lib/utilformats';

const { YYYYMMDD, CASHCOMMA } = utilFormats();

export const MyDepositColumn: ColumnDef<MyDepositHistory>[] = [
  {
    accessorKey: 'title',
    size: 1000,
    header: () => <p className="flex-1">제목</p>,
    cell: ({ row }) => {
      return (
        <div className="line-clamp-1 flex-1 text-sm font-semibold">{row.original.eventDesc}</div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    size: 150,
    header: () => <p className="w-[150px] shrink-0 text-center text-sm">거래일시</p>,
    cell: ({ row }) => {
      return (
        <p className="w-[150px] shrink-0 text-center text-sm">
          {YYYYMMDD(row.original.createdAt, 'YYYY-MM-DD hh:mm:ss')}
        </p>
      );
    },
  },
  {
    accessorKey: 'previousBalance',
    size: 100,
    header: () => <p className="w-[100px] shrink-0 text-center text-sm">금액</p>,
    cell: ({ row }) => {
      return (
        <p className="w-[100px] shrink-0 text-right">
          {CASHCOMMA(row.original.currentBalance - row.original.previousBalance)}원
        </p>
      );
    },
  },
];
