'use client';

import { motion } from 'framer-motion';

import { WithdrawalCategorySearchOptions } from '@/entities/users/config';
import { WithdrawalCategory } from '@/entities/users/types';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form, FormControl, FormField, FormItem } from '@/shared/ui/shadcn/form';
import { TextareaField } from '@/shared/ui/TextareaField';

import { useWithdrawal } from '../model/useWithdrawal';

export const WithdrawalForm = () => {
  const {
    form,
    onSubmit,
    isVisible,
    toggleVisibility,
    isValid,
    handleWithdrawalBlock,
    withdrawalBlock,
  } = useWithdrawal();

  const { control, watch, setValue } = form;

  return (
    <form
      className="flex flex-col space-y-8 rounded-[20px] border-gray-300 py-4 sm:border sm:px-8 sm:py-8"
      onSubmit={(e) => {
        e.preventDefault();
        toggleVisibility();
      }}
    >
      <div className="space-y-2">
        <h4 className="text-xl font-semibold sm:text-2xl">어떤 점이 불편하셨나요?</h4>
        <h5 className="text-[15px] sm:text-lg">
          뉴밋을 이용하면서 불편했던 점을 말씀해주시면 서비스 개선에 참고하도록 하겠습니다.
        </h5>
      </div>
      <Form {...form}>
        <FormField
          control={control}
          name="category"
          render={() => (
            <FormItem className="sm:w-1/2">
              <FormControl>
                <CommonSelect
                  value={watch('category')}
                  onChange={(value) => setValue('category', value as WithdrawalCategory)}
                  options={WithdrawalCategorySearchOptions}
                  placeholder="탈퇴 사유 선택"
                />
              </FormControl>
            </FormItem>
          )}
        />
        {watch('category') === WithdrawalCategory.OTHER && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <TextareaField
              form={form}
              label="기타"
              requiredText
              name="description"
              placeholder="사유를 입력해 주세요. (20자 이상)"
            />
          </motion.div>
        )}
        <div className="space-y-4 pt-12">
          <h4 className="text-xl font-semibold sm:text-2xl">탈퇴 전 확인해 주세요.</h4>
          <ul className="list-inside list-disc pl-2 text-[15px] sm:text-lg">
            <li> 보유 중인 자산이 있을 경우 탈퇴가 불가능합니다.</li>
            <li> 청약 중인 공모가 있을 경우 탈퇴가 불가능합니다.</li>
            <li> 연동된 계정(이메일/네이버/카카오)은 일괄 삭제됩니다.</li>
            <li> 탈퇴 후 개인정보는 일괄 삭제됩니다.</li>
            <li> 탈퇴 시 작성한 게시글은 삭제되지 않습니다.</li>
            <li> 탈퇴 후 재가입 시 신규 회원 혜택에 제한이 있을 수 있습니다.</li>
            <li> 탈퇴 시 보유한 쿠폰 및 혜택은 모두 삭제되며 복원, 환불이 불가합니다.</li>
          </ul>
        </div>
        <CheckboxField
          label="상기 뉴밋 탈퇴 유의사항을 확인했습니다."
          value="isAgree"
          checked={watch('isAgree')}
          onCheckedChange={() => setValue('isAgree', !watch('isAgree'))}
          className="mt-4"
        />
      </Form>
      <PrimaryButton
        disabled={!isValid}
        className="h-[44px] w-full sm:mx-auto sm:h-12 sm:w-40 ml:mx-0"
        text="회원 탈퇴"
      />
      <ConfirmDialog
        isOpen={isVisible}
        handleOpen={toggleVisibility}
        title="회원 탈퇴"
        description="회원 탈퇴 하시겠습니까?"
        isCancelButton
        handleAction={() => {
          onSubmit(form.getValues());
        }}
      />
      <ConfirmDialog
        isOpen={withdrawalBlock.isBlock}
        handleOpen={() => handleWithdrawalBlock(false, '')}
        title="탈퇴 불가 안내"
        isCancelButton={false}
        handleAction={() => handleWithdrawalBlock(false, '')}
        description={withdrawalBlock.message}
      />
    </form>
  );
};
