'use client';

import { motion } from 'framer-motion';
import React from 'react';

import { WithdrawalCategorySearchOptions } from '@/entities/users/config';
import { WithdrawalCategory } from '@/entities/users/types';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { CheckboxField } from '@/shared/ui/CheckboxField';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form, FormControl, FormField, FormItem } from '@/shared/ui/shadcn/form';
import { TextareaField } from '@/shared/ui/TextareaField';

import { useWithdrawal } from '../model/useWithdrawal';

export const MobileWithdrawalForm = () => {
  const {
    form,
    onSubmit,
    isVisible,
    toggleVisibility,
    isValid,
    handleWithdrawalBlock,
    withdrawalBlock,
  } = useWithdrawal();

  const { control, watch, setValue } = form;
  const { routerBack } = useWebViewRouter();

  return (
    <section className="px-6 pb-24 pt-6">
      <form
        className="flex flex-col justify-between space-y-4"
        onSubmit={(e) => {
          e.preventDefault();
          toggleVisibility();
        }}
      >
        <div>
          <div className="mb-4 space-y-2">
            <h4 className="text-lg font-bold">어떤 점이 불편하셨나요?</h4>
            <h5 className="text-sm">
              뉴밋을 이용하면서 불편했던 점을 말씀해주시면 서비스 개선에 참고하도록 하겠습니다.
            </h5>
          </div>
          <Form {...form}>
            <FormField
              control={control}
              name="category"
              render={() => (
                <FormItem className="sm:w-1/2">
                  <FormControl>
                    <CommonSelect
                      value={watch('category')}
                      onChange={(value) => setValue('category', value as WithdrawalCategory)}
                      options={WithdrawalCategorySearchOptions}
                      placeholder="탈퇴 사유 선택"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            {watch('category') === WithdrawalCategory.OTHER && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-6"
              >
                <TextareaField
                  form={form}
                  label="기타"
                  requiredText
                  name="description"
                  placeholder="사유를 입력해 주세요. (20자 이상)"
                />
              </motion.div>
            )}
            <div className="space-y-8 pt-14">
              <div className="space-y-4">
                <h4 className="text-xl font-semibold sm:text-2xl">탈퇴 전 확인해 주세요.</h4>
                <ul className="list-outside list-disc pl-4 text-[15px] sm:text-lg">
                  <li> 보유 중인 자산이 있을 경우 탈퇴가 불가능합니다.</li>
                  <li> 청약 중인 공모가 있을 경우 탈퇴가 불가능합니다.</li>
                  <li> 연동된 계정(이메일/네이버/카카오)은 일괄 삭제됩니다.</li>
                  <li> 탈퇴 후 개인정보는 일괄 삭제됩니다.</li>
                  <li> 탈퇴 시 작성한 게시글은 삭제되지 않습니다.</li>
                  <li> 탈퇴 후 재가입 시 신규 회원 혜택에 제한이 있을 수 있습니다.</li>
                  <li> 탈퇴 시 보유한 쿠폰 및 혜택은 모두 삭제되며 복원, 환불이 불가합니다.</li>
                </ul>
              </div>
              <CheckboxField
                label="상기 뉴밋 탈퇴 유의사항을 확인했습니다."
                value="isAgree"
                checked={watch('isAgree')}
                onCheckedChange={() => setValue('isAgree', !watch('isAgree'))}
              />
            </div>
          </Form>
        </div>

        <div className="fixed bottom-0 left-0 flex w-full gap-2 bg-white px-6 py-2 sm:static">
          <SecondaryButton
            type="button"
            onClick={routerBack}
            className="h-12 w-full border-gray-300 text-base"
            text="취소"
          />
          <PrimaryButton disabled={!isValid} className="h-12 w-full text-base" text="회원 탈퇴" />
        </div>
        <ConfirmDialog
          isOpen={isVisible}
          handleOpen={toggleVisibility}
          title="회원 탈퇴"
          description="회원 탈퇴 하시겠습니까?"
          isCancelButton
          handleAction={() => {
            onSubmit(form.getValues());
          }}
        />
        <ConfirmDialog
          isOpen={withdrawalBlock.isBlock}
          handleOpen={() => handleWithdrawalBlock(false, '')}
          title="탈퇴 불가 안내"
          isCancelButton={false}
          handleAction={() => handleWithdrawalBlock(false, '')}
          description={withdrawalBlock.message}
        />
      </form>
    </section>
  );
};
