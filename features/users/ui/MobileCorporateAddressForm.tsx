import { InputField } from '@/shared/ui/InputField';
import { PostCodeDialog } from '@/shared/ui/PostCodeDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useMobileCorporateAddress } from '../model/useMobileCorporateAddress';

export const MobileCorporateAddressForm = () => {
  const {
    corporateProfileForm,
    corporateProfileFormSubmit,
    isPostDialog,
    togglePostDialog,
    handlePostCode,
    isDisabled,
  } = useMobileCorporateAddress();

  const { handleSubmit, setValue } = corporateProfileForm;
  return (
    <form className="flex h-mobile-non-gnb flex-col justify-between gap-3">
      <Form {...corporateProfileForm}>
        <div className="space-y-2">
          <div className="flex w-full items-end gap-2">
            <InputField
              form={corporateProfileForm}
              name="address1"
              placeholder="도로명 주소"
              readOnly
            />
            <SecondaryButton
              type="button"
              onClick={handlePostCode}
              className="min-w-[96px]"
              text="주소검색"
            />
          </div>
          <InputField form={corporateProfileForm} name="address2" placeholder="상세 주소" />
        </div>
        <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2">
          <PrimaryButton
            onClick={handleSubmit(corporateProfileFormSubmit)}
            disabled={isDisabled}
            text="저장하기"
            type="button"
            className="!h-12 w-full text-base"
          />
        </div>
        <PostCodeDialog
          isOpen={isPostDialog}
          close={togglePostDialog}
          setValue={(address) =>
            setValue('address1', address, {
              shouldValidate: true,
              shouldDirty: true,
            })
          }
        />
      </Form>
    </form>
  );
};
