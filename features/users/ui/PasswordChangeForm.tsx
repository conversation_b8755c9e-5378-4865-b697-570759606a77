'use client';

import { PasswordConfirmMessage } from '@/shared/ui/PasswordConfirmMessage';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { usePasswordChange } from '../model/usePasswordChange';

export const PasswordChangeForm = () => {
  const { form, onSubmit, isPasswordMatch } = usePasswordChange();

  const { formState, getValues } = form;
  return (
    <form className="w-1/2 space-y-16">
      <Form {...form}>
        <PasswordInputField
          form={form}
          name="password"
          label="현재 비밀번호"
          placeholder="현재 비밀번호 입력"
        />
        <div className="space-y-3">
          <PasswordInputField
            form={form}
            name="newPassword"
            label="신규 비밀번호"
            placeholder="신규 비밀번호 입력"
          />
          <div className="relative space-y-2">
            <PasswordInputField
              form={form}
              name="newPasswordConfirm"
              placeholder="신규 비밀번호 재입력"
            />
            {isPasswordMatch && (
              <PasswordConfirmMessage className="absolute bottom-[-24px] left-0" />
            )}
          </div>
        </div>

        <PrimaryButton
          onClick={() => onSubmit(getValues())}
          disabled={!formState.isValid}
          className="mt-2 sm:mx-auto sm:mt-12 sm:w-40 ml:mx-0"
          type="button"
        />
      </Form>
    </form>
  );
};
