import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { PasswordConfirmMessage } from '@/shared/ui/PasswordConfirmMessage';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { usePasswordChange } from '../model/usePasswordChange';

export const MobilePasswordChangeForm = () => {
  const { form, onSubmit, isPasswordMatch, isLoading } = usePasswordChange();

  const { formState, handleSubmit } = form;

  const { routerBack } = useWebViewRouter();

  const passwordChangeSubmit = async () => {
    try {
      await onSubmit(form.getValues());
      routerBack();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
    }
  };
  return (
    <form
      className="flex flex-col justify-between gap-3"
      onSubmit={handleSubmit(passwordChangeSubmit)}
    >
      <Form {...form}>
        <div className="space-y-12">
          <PasswordInputField
            form={form}
            name="password"
            label="현재 비밀번호"
            placeholder="현재 비밀번호 입력"
          />
          <div className="space-y-3">
            <PasswordInputField
              form={form}
              name="newPassword"
              label="신규 비밀번호"
              placeholder="신규 비밀번호 입력"
            />
            <div className="relative space-y-2">
              <PasswordInputField
                form={form}
                name="newPasswordConfirm"
                placeholder="신규 비밀번호 재입력"
              />
              {isPasswordMatch && (
                <PasswordConfirmMessage className="absolute bottom-[-24px] left-0" />
              )}
            </div>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2">
          <PrimaryButton
            type="submit"
            className={`h-12 w-full text-base`}
            text="저장하기"
            disabled={!formState.isValid || isLoading}
          />
        </div>
      </Form>
    </form>
  );
};
