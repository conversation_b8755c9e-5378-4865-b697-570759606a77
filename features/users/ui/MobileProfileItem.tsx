import { InformationCircleIcon } from '@heroicons/react/24/outline';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import React from 'react';

interface MobileProfileItemProps {
  label: string;
  value?: string;
  showInfoIcon?: boolean;
  showChevron?: boolean;
  onClick?: () => void;
  placeholder?: string;
  disabled?: boolean;
}

export const MobileProfileItem = ({
  label,
  value,
  showInfoIcon = false,
  showChevron = false,
  onClick,
  placeholder,
  disabled = false,
}: MobileProfileItemProps) => {
  return (
    <div className="flex items-center justify-between py-4">
      <div className="flex items-center gap-1">
        <p>{label}</p>
        {showInfoIcon && <InformationCircleIcon className="h-4 w-4" />}
      </div>
      <div className="flex cursor-pointer items-center gap-1" onClick={onClick}>
        {value ? (
          <span className="text-gray-700">{value}</span>
        ) : (
          <span className="text-gray-400">{placeholder}</span>
        )}
        {showChevron && (
          <button>
            <ChevronRightIcon className="h-4 w-4 text-gray-400" strokeWidth={2} />
          </button>
        )}
      </div>
    </div>
  );
};
