'use client';

import { motion } from 'framer-motion';
import React from 'react';

import { NotificationChannel, NotificationType } from '@/shared/types';
import { CheckboxField } from '@/shared/ui/CheckboxField';
import { Loading } from '@/shared/ui/Loading';
import { Label } from '@/shared/ui/shadcn/label';
import { Separator } from '@/shared/ui/shadcn/separator';
import { Switch } from '@/shared/ui/shadcn/switch';

import { useSetNotification } from '../model/useSetNotification';

export const MobileNotificationForm = () => {
  const { handleChannel, handleType, notification, isLoading, isMarketing, isNewsletter } =
    useSetNotification();

  if (isLoading) return <Loading />;

  const isShowChannel = isMarketing || isNewsletter;

  return (
    <section className="p-6">
      <form action="">
        <div className="flex items-center justify-between py-4">
          <Label className="text-base font-normal sm:font-semibold">이벤트 혜택 알림</Label>
          <Switch
            checked={isMarketing}
            value={NotificationType.MARKETING}
            onCheckedChange={() => handleType(NotificationType.MARKETING)}
            className="w-[46px]"
          />
        </div>
        <div className="flex items-center justify-between py-4">
          <Label className="text-base font-normal sm:font-semibold">상품 알림</Label>
          <Switch
            checked={isNewsletter}
            value={NotificationType.NEWSLETTER}
            onCheckedChange={() => handleType(NotificationType.NEWSLETTER)}
            className="w-[46px]"
          />
        </div>
        <Separator className="my-3 bg-gray-200" />
        {isShowChannel && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col"
          >
            <div className="space-y-2 py-4 leading-[150%]">
              <h3 className="font-semibold">알림 수신 동의</h3>
              <h5 className="text-xs text-gray-500">
                중요 공지는 수신 동의 여부와 상관없이 발송됩니다.
              </h5>
            </div>
            <div className="flex flex-col">
              <CheckboxField
                value="isEmailNotification"
                label="이메일"
                checked={notification?.email}
                onCheckedChange={() => handleChannel(NotificationChannel.EMAIL)}
                className="py-4"
              />
              <CheckboxField
                value="isSmsNotification"
                label="SMS"
                checked={notification?.sms}
                onCheckedChange={() => handleChannel(NotificationChannel.SMS)}
                className="py-4"
              />
            </div>
          </motion.div>
        )}
      </form>
    </section>
  );
};
