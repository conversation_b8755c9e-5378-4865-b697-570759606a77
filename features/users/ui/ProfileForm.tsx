'use client';

import { CheckCircleIcon } from '@heroicons/react/24/solid';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { CommonTooltip } from '@/shared/ui/CommonTooltip';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { InputField } from '@/shared/ui/InputField';
import { PostCodeDialog } from '@/shared/ui/PostCodeDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useMyProfile } from '../model/useMyProfile';

export const ProfileForm = () => {
  const {
    profileForm,
    profileFormSubmit,
    handleIdentityVerification,
    isPostDialog,
    togglePostDialog,
    isMobileDialog,
    toggleMobileDialog,
  } = useMyProfile();

  const { handleSubmit, setValue } = profileForm;

  const isDisabled = !(profileForm.formState.isValid && profileForm.formState.isDirty);

  const { routerPush } = useWebViewRouter();

  return (
    <>
      <div className="flex justify-end">
        <CommonTooltip title="기본 정보가 변경되셨나요?" description="기본 정보가 변경된 경우" />
      </div>
      <form className="flex flex-col gap-3" onSubmit={handleSubmit(profileFormSubmit)}>
        <Form {...profileForm}>
          <div className="flex flex-col gap-8 sm:gap-10">
            <InputField form={profileForm} label="이름" name="name" placeholder="이름" disabled />
            <div className="flex w-full gap-5">
              <div className="basis-1/2">
                <InputField
                  disabled
                  form={profileForm}
                  label="이메일"
                  name="email"
                  placeholder="이메일"
                />
              </div>
              <div className="relative basis-1/2 space-y-2 sm:space-y-3">
                <div className="flex w-full items-end gap-2">
                  <InputField
                    form={profileForm}
                    name="mobileNumber"
                    placeholder="휴대전화번호"
                    disabled
                    label="휴대전화번호"
                  />
                  <SecondaryButton
                    onClick={toggleMobileDialog}
                    className="min-w-[96px]"
                    text="변경"
                    type="button"
                  />
                </div>

                <div className="absolute bottom-[-24px] left-0 flex items-center gap-1 text-xs font-semibold text-green-500">
                  <CheckCircleIcon className="h-4 w-4" color="green" />
                  인증 완료
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex w-full items-end gap-2">
                <InputField
                  form={profileForm}
                  name="address1"
                  placeholder="도로명 주소"
                  disabled
                  label="주소"
                />
                <SecondaryButton
                  type="button"
                  onClick={togglePostDialog}
                  className="min-w-[96px]"
                  text="등록"
                />
              </div>
              <InputField form={profileForm} name="address2" placeholder="상세 주소" />
            </div>
          </div>
          <div className="mt-6 flex flex-row items-center gap-6 ml:mt-8 ml:flex-col ml:gap-8">
            <PrimaryButton disabled={isDisabled} className="w-40" text="저장하기" type="submit" />
            <button
              type="button"
              onClick={() => routerPush('/user/my/withdrawal')}
              className="text-xs font-semibold text-gray-500 underline"
            >
              회원탈퇴
            </button>
          </div>
          <PostCodeDialog
            isOpen={isPostDialog}
            close={togglePostDialog}
            setValue={(address) =>
              setValue('address1', address, {
                shouldValidate: true,
                shouldDirty: true,
              })
            }
          />

          <ConfirmDialog
            isOpen={isMobileDialog}
            handleOpen={toggleMobileDialog}
            handleAction={handleIdentityVerification}
            text="확인"
            title="휴대전화번호 본인인증 안내"
            description={`휴대전화번호 변경을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
          />
        </Form>
      </form>
    </>
  );
};
