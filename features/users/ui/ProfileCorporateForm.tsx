import { CheckCircleIcon } from '@heroicons/react/24/solid';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { InputField } from '@/shared/ui/InputField';
import { PostCodeDialog } from '@/shared/ui/PostCodeDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useMyCorporateProfile } from '../model/useMyCorporateProfile';

export const ProfileCorporateForm = () => {
  const {
    corporateProfileForm,
    corporateProfileFormSubmit,
    handleIdentityVerification,
    isPostDialog,
    togglePostDialog,
    isMobileDialog,
    toggleMobileDialog,
  } = useMyCorporateProfile();

  const { handleSubmit, setValue } = corporateProfileForm;

  const { routerPush } = useWebViewRouter();

  return (
    <form className="flex flex-col gap-3" onSubmit={handleSubmit(corporateProfileFormSubmit)}>
      <Form {...corporateProfileForm}>
        <div className="flex flex-col gap-8 sm:gap-10">
          <div className="flex gap-5">
            <InputField
              form={corporateProfileForm}
              label="법인명"
              name="companyName"
              placeholder="법인명"
              disabled
            />
            <InputField
              form={corporateProfileForm}
              label="법인등록번호"
              name="crn"
              placeholder="법인등록번호"
              disabled
            />
          </div>
          <div className="flex gap-5">
            <InputField
              form={corporateProfileForm}
              label="사업자등록번호"
              name="brn"
              placeholder="사업자등록번호"
              disabled
            />
            <InputField
              form={corporateProfileForm}
              label="이메일 주소"
              name="managerEmail"
              placeholder="이메일 주소"
              disabled
            />
          </div>

          <div className="relative space-y-2 sm:space-y-3">
            <div className="flex w-full items-end gap-2">
              <InputField
                form={corporateProfileForm}
                name="managerMobileNumber"
                placeholder="휴대전화번호"
                readOnly
                label="휴대전화번호"
              />
              <SecondaryButton
                onClick={toggleMobileDialog}
                className="min-w-[96px]"
                text="변경"
                type="button"
              />
            </div>

            <div className="absolute bottom-[-24px] left-0 flex items-center gap-1 text-xs font-semibold text-green-500">
              <CheckCircleIcon className="h-4 w-4" color="green" />
              인증 완료
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex w-full items-end gap-2">
              <InputField
                form={corporateProfileForm}
                name="address1"
                placeholder="도로명 주소"
                disabled
                label="주소"
              />
              <SecondaryButton
                type="button"
                onClick={togglePostDialog}
                className="min-w-[96px]"
                text="등록"
              />
            </div>
            <InputField form={corporateProfileForm} name="address2" placeholder="상세 주소" />
          </div>
        </div>
        <div className="mt-6 flex flex-col items-center gap-6 ml:mt-8 ml:flex-row ml:gap-8">
          <PrimaryButton
            disabled={!corporateProfileForm.formState.isValid}
            className="w-full sm:w-40"
            text="저장하기"
            type="submit"
          />
          <button
            onClick={() => routerPush('/user/my/withdrawal')}
            className="text-xs font-semibold text-gray-500 underline"
          >
            회원탈퇴
          </button>
        </div>
        <PostCodeDialog
          isOpen={isPostDialog}
          close={togglePostDialog}
          setValue={(address) => setValue('address1', address)}
        />
        <ConfirmDialog
          isOpen={isMobileDialog}
          handleOpen={toggleMobileDialog}
          handleAction={handleIdentityVerification}
          text="확인"
          title="휴대전화번호 본인인증 안내"
          description={`휴대전화번호 변경을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
        />
      </Form>
    </form>
  );
};
