'use client';

import { TrashIcon } from '@heroicons/react/24/outline';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import { PaperClipIcon, PlusIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useApplyInvestorTypeForm } from '@/features/investor/model/useApplyInvestorTypeForm';

import { InvestorQualificationStatus } from '@/entities/investor/interface';
import { InvestorQualificationType } from '@/entities/investor/types';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FileInfoTooltop } from '@/shared/ui/FileInfoTooltop';
import { FileUploader } from '@/shared/ui/FileUploader';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Checkbox } from '@/shared/ui/shadcn/checkbox';

import { useFetchUser } from '../model/useFetchUser';

export const MobileApplyInvestorForm = () => {
  const {
    form,
    handleFileUpload,
    ref,
    handleDeleteFile,
    handleUploadedFiles,
    onSubmit,
    isLoading,
    isApply,
    toggleApply,
  } = useApplyInvestorTypeForm();

  const { user } = useFetchUser();

  const isQualificationApplying =
    user?.investorQualification?.applying?.qualificationStatus ===
    InvestorQualificationStatus.PENDING;

  const { watch, setValue, handleSubmit } = form;

  return (
    <section>
      {isQualificationApplying && (
        <div className="flex items-center justify-center bg-blue-50">
          <span className="py-2 text-xs font-semibold leading-[150%] text-blue-500">
            투자자 유형 변경 신청이 완료되었으며 심사가 진행 중입니다.
          </span>
        </div>
      )}
      <div className="flex h-[calc(100vh-128px)] w-full flex-col justify-between px-6 py-4">
        <div>
          <div className="mb-6 flex h-[44px] items-center justify-between rounded-lg bg-gray-100 px-4 text-gray-500">
            <span className="font-semibold">투자자 유형 변경 안내</span>
            <button>
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="investorType" className="font-semibold">
              변경 유형
            </label>
            <div className="flex gap-8">
              <div className="flex items-center gap-2">
                <Checkbox
                  value={InvestorQualificationType.QUALIFIED}
                  disabled={
                    user?.investorQualification.current?.qualificationType ===
                      InvestorQualificationType.QUALIFIED || isQualificationApplying
                  }
                  onCheckedChange={(checked) => {
                    setValue(
                      'investorQualificationType',
                      checked
                        ? InvestorQualificationType.QUALIFIED
                        : InvestorQualificationType.GENERAL,
                    );
                  }}
                  checked={
                    watch('investorQualificationType') === InvestorQualificationType.QUALIFIED
                  }
                  className="h-5 w-5 rounded-full bg-white text-white shadow-none data-[state=checked]:bg-primary-500"
                />
                <p className="text-sm">적격투자자</p>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox
                  value={InvestorQualificationType.PROFESSIONAL}
                  onCheckedChange={(checked) => {
                    setValue(
                      'investorQualificationType',
                      checked
                        ? InvestorQualificationType.PROFESSIONAL
                        : InvestorQualificationType.GENERAL,
                    );
                  }}
                  checked={
                    watch('investorQualificationType') === InvestorQualificationType.PROFESSIONAL
                  }
                  disabled={isQualificationApplying}
                  className="h-5 w-5 rounded-full bg-white text-white shadow-none data-[state=checked]:bg-primary-500"
                />
                <p className="text-sm">전문투자자</p>
              </div>
            </div>
          </div>
          <div className="mt-12 flex flex-col gap-2">
            <p className="flex items-center gap-1 font-semibold">
              제출 서류 등록 <strong className="font-semibold text-primary-500">(필수)</strong>{' '}
              <FileInfoTooltop />
            </p>
            {watch('attachFiles')?.map((file) => (
              <div
                className="flex justify-between rounded-lg border border-gray-300 bg-white px-4 py-3"
                key={file.size}
              >
                <div className="flex items-center gap-2">
                  <PaperClipIcon color="blue" className="h-5 w-5" />
                  {file.name}
                </div>
                <button type="button" onClick={() => handleDeleteFile(file)}>
                  <TrashIcon className="h-5 w-5" />
                </button>
              </div>
            ))}
            <Button
              type="button"
              onClick={handleFileUpload}
              className="h-[44px] w-full rounded-lg border border-gray-300 bg-white text-gray-500 shadow-none sm:h-12"
              disabled={isQualificationApplying}
            >
              <PlusIcon className="h-5 w-5" />
              추가할 파일 선택
            </Button>
          </div>
        </div>
        {!isQualificationApplying && (
          <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2">
            <PrimaryButton
              disabled={!form.formState.isValid || isLoading}
              text="신청하기"
              className="h-12 w-full text-base"
              onClick={toggleApply}
            />
          </div>
        )}

        <FileUploader
          ref={ref}
          uploadedFiles={handleUploadedFiles}
          multiple
          maxSizeMB={5}
          acceptType=".pdf,.jpg,.png"
        />
      </div>
      <ConfirmDialog
        isOpen={isApply}
        handleAction={handleSubmit(onSubmit)}
        handleOpen={toggleApply}
        title="투자자 유형 변경 신청"
        description="투자자 유형 변경 신청을 하시겠습니까?"
      />
    </section>
  );
};
