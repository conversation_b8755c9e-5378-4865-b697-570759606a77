import { SubscriptionApplySteps } from '@/entities/subscriptions/types';

import { useVisibility } from '@/shared/model/useVisibility';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { InputField } from '@/shared/ui/InputField';
import { PostCodeDialog } from '@/shared/ui/PostCodeDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useFetchUser } from '../model/useFetchUser';
import { useMyCorporateProfile } from '../model/useMyCorporateProfile';
import { useMyProfile } from '../model/useMyProfile';

export const SubscriptionApplyUserCard = () => {
  const { user, isGeneral } = useFetchUser();

  const {
    isVisible: isAddressEdit,
    toggleVisibility: toggleAddressEdit,
    closeToggle: closeAddressEdit,
  } = useVisibility(false);

  const {
    profileForm,
    profileFormSubmit,
    isLoading,
    isPostDialog,
    togglePostDialog,
    handleIdentityVerification,
    isMobileDialog,
    toggleMobileDialog,
  } = useMyProfile();

  const {
    corporateProfileForm,
    corporateProfileFormSubmit,
    isLoading: isCorporateLoading,
    handleIdentityVerification: handleCorporateIdentityVerification,
    isPostDialog: isCorporatePostDialog,
    togglePostDialog: toggleCorporatePostDialog,
    isMobileDialog: isCorporateMobileDialog,
    toggleMobileDialog: toggleCorporateMobileDialog,
  } = useMyCorporateProfile();

  const { watch, getFieldState } = profileForm;

  const handleSubmit = async () => {
    if (isGeneral) {
      await profileFormSubmit(profileForm.getValues());
    } else {
      await corporateProfileFormSubmit(corporateProfileForm.getValues());
    }
    closeAddressEdit();
  };

  return (
    <div className="space-y-5 px-6 sm:space-y-3 sm:px-8 ml:px-0">
      <h4 className="sm:text-20 text-base font-semibold">내 정보 확인</h4>
      <div className="space-y-3 rounded-lg border-gray-300 sm:space-y-6 sm:border sm:p-8">
        <div className="flex items-center text-sm sm:text-base">
          <span className="w-[120px] text-gray-600">이름</span>
          <span className="flex-1 sm:font-semibold">
            {isGeneral ? user?.userProfile?.name : user?.userCorporateProfile?.representativeName}
          </span>
        </div>
        <div className="flex items-center text-sm sm:text-base">
          <span className="w-[120px] text-gray-600">이메일 주소</span>
          <span className="flex-1 sm:font-semibold">
            {isGeneral ? user?.userProfile?.email : user?.userCorporateProfile?.email}
          </span>
        </div>
        <div className="flex items-center text-sm sm:text-base">
          <span className="w-[120px] text-gray-600">휴대폰 번호</span>
          <div className="flex flex-1 items-center justify-between sm:font-semibold">
            <p>
              {isGeneral
                ? profileForm.watch('mobileNumber')
                : corporateProfileForm.watch('managerMobileNumber')}
            </p>
            <button
              onClick={isGeneral ? toggleMobileDialog : toggleCorporateMobileDialog}
              className="text-xs text-gray-500 underline"
            >
              변경
            </button>
          </div>
        </div>
        {isGeneral ? (
          <Form {...profileForm}>
            <div className={`flex text-sm sm:text-base ${isAddressEdit ? 'items-start' : ''}`}>
              <span className="w-[120px] text-gray-600">주소</span>
              {isAddressEdit ? (
                <div className="flex-1 space-y-2">
                  <div className="flex w-full items-end gap-2">
                    <InputField
                      form={profileForm}
                      name="address1"
                      placeholder="주소 입력"
                      readOnly
                    />
                    <SecondaryButton
                      type="button"
                      onClick={togglePostDialog}
                      className="min-w-[96px]"
                      text="주소검색"
                    />
                  </div>
                  <InputField form={profileForm} name="address2" placeholder="상세주소 입력" />
                  <div className="flex justify-end">
                    <PrimaryButton
                      type="button"
                      disabled={
                        !(getFieldState('address1').isDirty || getFieldState('address2').isDirty) ||
                        isLoading ||
                        isCorporateLoading
                      }
                      onClick={handleSubmit}
                      className="min-w-[96px]"
                      text="저장"
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-1 justify-between sm:items-center sm:font-semibold">
                  <p>
                    {watch('address1')} {watch('address2')}
                  </p>
                  <button
                    className="h-[18px] min-w-[22px] text-xs text-gray-500 underline"
                    onClick={toggleAddressEdit}
                  >
                    변경
                  </button>
                </div>
              )}
            </div>
          </Form>
        ) : (
          <Form {...corporateProfileForm}>
            <div className={`flex ${isAddressEdit ? 'items-start' : 'items-center'}`}>
              <span className="w-[120px] text-gray-600">주소</span>
              {isAddressEdit ? (
                <div className="flex-1 space-y-2">
                  <div className="flex w-full items-end gap-2">
                    <InputField
                      form={corporateProfileForm}
                      name="address1"
                      placeholder="주소 입력"
                      readOnly
                    />
                    <SecondaryButton
                      type="button"
                      onClick={togglePostDialog}
                      className="min-w-[96px]"
                      text="주소검색"
                    />
                  </div>
                  <InputField
                    form={corporateProfileForm}
                    name="address2"
                    placeholder="상세주소 입력"
                  />
                  <div className="flex justify-end">
                    <PrimaryButton
                      type="button"
                      disabled={
                        !(getFieldState('address1').isDirty || getFieldState('address2').isDirty) ||
                        isLoading
                      }
                      onClick={handleSubmit}
                      className="min-w-[96px]"
                      text="저장"
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-1 items-center justify-between font-semibold">
                  <p>
                    {watch('address1')} {watch('address2')}
                  </p>
                  <button className="text-xs text-gray-500 underline" onClick={toggleAddressEdit}>
                    변경
                  </button>
                </div>
              )}
            </div>
          </Form>
        )}
      </div>
      <PostCodeDialog
        isOpen={isPostDialog}
        close={togglePostDialog}
        setValue={(address) =>
          profileForm.setValue('address1', address, {
            shouldValidate: true,
            shouldDirty: true,
          })
        }
      />
      <PostCodeDialog
        isOpen={isCorporatePostDialog}
        close={toggleCorporatePostDialog}
        setValue={(address) =>
          corporateProfileForm.setValue('address1', address, {
            shouldValidate: true,
            shouldDirty: true,
          })
        }
      />
      <ConfirmDialog
        isOpen={isMobileDialog}
        handleOpen={toggleMobileDialog}
        title="휴대폰 본인인증 안내"
        isCancelButton={false}
        description={`휴대폰번호 변경을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
        handleAction={() => handleIdentityVerification(SubscriptionApplySteps.CONFIRM)}
      />
      <ConfirmDialog
        isOpen={isCorporateMobileDialog}
        handleOpen={toggleCorporateMobileDialog}
        title="휴대폰 본인인증 안내"
        isCancelButton={false}
        description={`휴대폰번호 변경을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
        handleAction={() => handleCorporateIdentityVerification(SubscriptionApplySteps.CONFIRM)}
      />
    </div>
  );
};
