import { PasswordConfirmMessage } from '@/shared/ui/PasswordConfirmMessage';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { usePasswordCreate } from '../model/usePasswordCreate';

interface PasswordCreateFormProps {
  handlePasswordNotExist: () => void;
}

export const PasswordCreateForm = ({ handlePasswordNotExist }: PasswordCreateFormProps) => {
  const { form, onSubmit, isPasswordMatch, formState } = usePasswordCreate();

  const handleSubmit = async () => {
    try {
      await onSubmit(form.getValues());

      handlePasswordNotExist();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
    }
  };

  return (
    <div className="flex flex-col justify-between sm:h-auto">
      <Form {...form}>
        <form className="w-1/2 space-y-3">
          <PasswordInputField
            form={form}
            name="password"
            label="신규 비밀번호"
            placeholder="신규 비밀번호 입력"
          />
          <div className="relative space-y-2">
            <PasswordInputField form={form} name="rePassword" placeholder="신규 비밀번호 재입력" />
            {isPasswordMatch && (
              <PasswordConfirmMessage className="absolute bottom-[-24px] left-0" />
            )}
          </div>
        </form>
      </Form>

      <PrimaryButton
        type="button"
        className={`mt-12 h-12 w-40`}
        text="저장하기"
        onClick={handleSubmit}
        disabled={!formState.isValid}
      />
    </div>
  );
};
