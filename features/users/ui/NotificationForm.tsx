'use client';

import { motion } from 'framer-motion';

import { useSetNotification } from '@/features/users/model/useSetNotification';

import { NotificationChannel, NotificationType } from '@/shared/types';
import { CheckboxField } from '@/shared/ui/CheckboxField';
import { Loading } from '@/shared/ui/Loading';
import { Label } from '@/shared/ui/shadcn/label';
import { Separator } from '@/shared/ui/shadcn/separator';
import { Switch } from '@/shared/ui/shadcn/switch';

export const NotificationForm = () => {
  const { handleChannel, handleType, notification, isLoading, isMarketing, isNewsletter } =
    useSetNotification();

  if (isLoading) return <Loading />;

  const isShowChannel = isMarketing || isNewsletter;

  return (
    <section className="flex">
      <div className="w-full rounded-[20px] border border-gray-300 px-5 py-6 sm:px-8 sm:py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-semibold sm:text-base">이벤트 혜택 알림</Label>
            <Switch
              checked={isMarketing}
              value={NotificationType.MARKETING}
              onCheckedChange={() => handleType(NotificationType.MARKETING)}
              className="w-[46px]"
            />
          </div>
          <div className="flex items-center justify-between">
            <Label className="text-sm font-semibold sm:text-base">상품 알림</Label>
            <Switch
              checked={isNewsletter}
              value={NotificationType.NEWSLETTER}
              onCheckedChange={() => handleType(NotificationType.NEWSLETTER)}
              className="w-[46px]"
            />
          </div>
        </div>
        <Separator className="my-8 bg-gray-200 sm:my-12" />
        {isShowChannel && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col"
          >
            <div className="flex items-center gap-12">
              <CheckboxField
                value="isEmailNotification"
                label="이메일"
                checked={notification?.email}
                onCheckedChange={() => handleChannel(NotificationChannel.EMAIL)}
              />
              <CheckboxField
                value="isSmsNotification"
                label="SMS"
                checked={notification?.sms}
                onCheckedChange={() => handleChannel(NotificationChannel.SMS)}
              />
            </div>
            <p className="mt-3 text-xs text-gray-700">
              주요 공지는 수신 동의 여부와 상관없이 발송됩니다.
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
};
