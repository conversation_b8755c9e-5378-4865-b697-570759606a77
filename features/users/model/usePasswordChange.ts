import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  PasswordChangeFormData,
  passwordChangeSchema,
} from '@/features/users/lib/passwordChangeSchema';

import { updatePassword } from '@/entities/users/api/updatePassword';

import { useToast } from '@/shared/model/useToast';

export const usePasswordChange = () => {
  const form = useForm<PasswordChangeFormData>({
    resolver: zodResolver(passwordChangeSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const { watch } = form;
  const [isLoading, setIsLoading] = useState(false);
  const { successToast, errorToast } = useToast();

  const newPassword = watch('newPassword');
  const newPasswordConfirm = watch('newPasswordConfirm');

  const isPasswordMatch = newPassword && newPasswordConfirm && newPassword === newPasswordConfirm;

  const onSubmit = async (data: PasswordChangeFormData) => {
    setIsLoading(true);
    try {
      await updatePassword({
        password: data.password,
        newPassword: data.newPassword,
      });

      successToast({
        title: '비밀번호 변경 성공',
      });

      form.reset();
      setIsLoading(false);
    } catch (error) {
      errorToast({
        title: '비밀번호 변경 실패',
        description: '비밀번호 수정에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { form, onSubmit, isPasswordMatch, isLoading };
};
