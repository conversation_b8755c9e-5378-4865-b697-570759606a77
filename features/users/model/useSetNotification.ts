'use client';

import { useQueryClient } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';
import { fetchUserNotification } from '@/features/users/api/fetchUserNotification';

import { updateUserNotification } from '@/entities/users/api/updateUserNotification';

import { useToast } from '@/shared/model/useToast';
import { NotificationChannel, NotificationType } from '@/shared/types';

export const useSetNotification = () => {
  const { successToast, errorToast } = useToast();
  const { data: notificationData, isLoading } = fetchUserNotification();

  const queryClient = useQueryClient();

  const notification = {
    email: notificationData?.channel?.includes(NotificationChannel.EMAIL) || false,
    sms: notificationData?.channel?.includes(NotificationChannel.SMS) || false,
  };

  const isMarketing = notificationData?.type?.includes(NotificationType.MARKETING);

  const isNewsletter = notificationData?.type?.includes(NotificationType.NEWSLETTER);

  const handleChannel = async (type: NotificationChannel) => {
    try {
      const currentChannels = [...(notificationData?.channel || [])];
      let updatedChannels: NotificationChannel[];

      if (currentChannels.includes(type)) {
        updatedChannels = currentChannels.filter((channel) => channel !== type);
        if (updatedChannels.length === 0) {
          errorToast({
            title: '하나 이상의 알림을 선택해주세요.',
          });
          return;
        }
      } else {
        updatedChannels = [...new Set([...currentChannels, type])];
      }

      await updateUserNotification({
        type: [...(notificationData?.type || [NotificationType.SERVICE])],
        channel: updatedChannels,
      });

      queryClient.setQueryData(queries.user.notification().queryKey, {
        ...notificationData,
        channel: updatedChannels,
      });

      successToast({
        title: `${type === NotificationChannel.EMAIL ? '이메일' : 'SMS'} 알림이 ${
          currentChannels.includes(type) ? '설정' : '해제'
        }되었습니다.`,
      });
    } catch (error) {
      errorToast({
        title: '알림 설정에 실패하였습니다.',
        description: '다시 시도해주세요.',
      });
    }
  };

  const handleType = async (value: NotificationType) => {
    const currentTypes = [...(notificationData?.type || [NotificationType.SERVICE])];
    let updatedTypes: NotificationType[];

    if (currentTypes.includes(value)) {
      updatedTypes = currentTypes.filter((type) => type !== value);
    } else {
      updatedTypes = [...new Set([...currentTypes, value])];
    }

    try {
      await updateUserNotification({
        type: updatedTypes,
        channel: [...(notificationData?.channel || [NotificationChannel.EMAIL])],
      });

      queryClient.setQueryData(queries.user.notification().queryKey, {
        ...notificationData,
        type: updatedTypes,
        channel:
          currentTypes.length === 1
            ? [NotificationChannel.EMAIL]
            : [...(notificationData?.channel || [NotificationChannel.EMAIL])],
      });

      successToast({
        title: `알림이 ${value ? '설정' : '해제'}되었습니다.`,
      });
    } catch (error) {
      errorToast({
        title: '알림 설정에 실패하였습니다.',
        description: '다시 시도해주세요.',
      });
    }
  };

  return {
    handleChannel,
    handleType,
    notification,
    isLoading,
    isMarketing,
    isNewsletter,
  };
};
