'use client';

import { useQueryClient } from '@tanstack/react-query';
import { Session } from 'next-auth';
import { useSession } from 'next-auth/react';
import { useEffect, useMemo } from 'react';

export const useOptimizedSession = () => {
  const queryClient = useQueryClient();
  const { data: liveSession, status } = useSession();

  const session = useMemo(() => {
    if (liveSession) return liveSession;
    return queryClient.getQueryData(['session']) as Session;
  }, [liveSession, queryClient]);

  useEffect(() => {
    if (liveSession && status === 'authenticated') {
      queryClient.setQueryData(['session'], liveSession);
    }
  }, [liveSession, status, queryClient]);

  return { session, status };
};
