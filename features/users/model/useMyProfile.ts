'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { queries } from '@/features/lib/queries';

import { checkMobileNumberDuplication } from '@/entities/auth/api/checkMobileNumberDuplication';
import { updateUser } from '@/entities/users/api/updateUser';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

import { MyProfileFormData, myProfileFormSchema } from '../lib/myProfileSchema';
import { useFetchUser } from './useFetchUser';

export const useMyProfile = () => {
  const { successToast, errorToast } = useToast();
  const { user, isGeneral } = useFetchUser();
  const queryClient = useQueryClient();
  const { verifyIdentity, isLoading: isIdentityLoading } = useIdentityVerification();
  const profileForm = useForm<MyProfileFormData>({
    resolver: zodResolver(myProfileFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const searchParams = useSearchParams();
  const { isVisible: isPostDialog, toggleVisibility: togglePostDialog } = useVisibility(false);
  const { isVisible: isMobileDialog, toggleVisibility: toggleMobileDialog } = useVisibility(false);
  const { isVisible: isLoading, toggleVisibility: isLoadingToggle } = useVisibility(false);
  const [isVerifyLoading, setIsVerifyLoading] = useState(false);

  const handleIdentityVerification = async (state?: string) => {
    // 폼 데이터를 로컬 스토리지에 저장
    const formData = profileForm.getValues();
    sessionStorage.setItem('profileFormData', JSON.stringify(formData));

    toggleMobileDialog();
    isLoadingToggle();
    const verifiedResult = await verifyIdentity(state);

    const { verifiedCustomer } = verifiedResult;

    const { data } = await checkMobileNumberDuplication(verifiedCustomer.phoneNumber);

    isLoadingToggle();

    if (data.isExist) {
      errorToast({
        title: '이미 존재하는 휴대폰 번호입니다.',
      });
      return;
    }

    if (verifiedCustomer) {
      profileForm.setValue('mobileNumber', verifiedCustomer.phoneNumber);
    }
  };

  useEffect(() => {
    const identityVerificationId = searchParams.get('identityVerificationId'); // 포트원 인증 고유번호
    const identityVerificationTxId = searchParams.get('identityVerificationTxId'); // 요청 시 설정한 유니크 ID
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부

    if (identityVerificationId && identityVerificationTxId) {
      setIsVerifyLoading(true);

      fetch('/api/identity/verify', {
        method: 'POST',
        body: JSON.stringify({ identityVerificationId, identityVerificationTxId, transactionType }),
      })
        .then((res) => res.json())
        .then(async ({ data }) => {
          if (data.verifiedCustomer) {
            if (!data.verifiedCustomer) return;

            const savedFormData = sessionStorage.getItem('signupFormData');
            const formValues = {
              mobileNumber: data.verifiedCustomer.phoneNumber,
              ...(savedFormData ? JSON.parse(savedFormData) : {}),
            };

            // 한 번에 모든 값을 설정
            profileForm.reset(formValues);

            if (savedFormData) {
              sessionStorage.removeItem('signupFormData');
            }

            const result = await checkMobileNumberDuplication(formValues.mobileNumber);
            if (result.data.isExist) {
              errorToast({
                title: '이미 존재하는 휴대폰 번호입니다.',
              });
            }
          }
        })
        .finally(() => {
          setIsVerifyLoading(false);
        });
    }
  }, [searchParams]);

  const formData = useMemo(() => {
    if (!user?.userProfile) return null;

    return {
      name: user.userProfile.name,
      mobileNumber: user.userProfile.mobileNumber,
      email: user.userProfile.email,
      address1: user.userProfile.address1,
      address2: user.userProfile.address2,
    };
  }, [user?.userProfile]);

  useEffect(() => {
    if (formData && isGeneral) {
      profileForm.reset(formData);
    }
  }, [formData, isGeneral]);

  const profileFormSubmit = async (data: MyProfileFormData) => {
    try {
      await updateUser(data);
      await queryClient.invalidateQueries({ queryKey: queries.user._def });
      successToast({
        title: '저장이 완료되었습니다.',
      });
    } catch (error) {
      errorToast({
        title: '저장에 실패했습니다.',
      });
    }
  };

  return {
    profileForm,
    profileFormSubmit,
    isLoading,
    isVerifyLoading,
    isIdentityLoading,
    handleIdentityVerification,
    isPostDialog,
    togglePostDialog,
    isMobileDialog,
    toggleMobileDialog,
  };
};
