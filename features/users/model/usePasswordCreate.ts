import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { createPassword } from '@/entities/users/api/createPassword';

import { useToast } from '@/shared/model/useToast';

import { PasswordResetFormData, passwordResetSchema } from '../lib/passwordResetSchema';

export const usePasswordCreate = () => {
  const form = useForm<PasswordResetFormData>({
    resolver: zodResolver(passwordResetSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });
  const { successToast, errorToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const { formState, watch } = form;
  const password = watch('password');
  const rePassword = watch('rePassword');

  const isPasswordMatch = password && rePassword && password === rePassword;

  const onSubmit = async (data: PasswordResetFormData) => {
    setIsLoading(true);
    try {
      await createPassword(data.password);

      successToast({
        title: '비밀번호 등록 성공',
      });

      form.reset();
      setIsLoading(false);
    } catch (error) {
      errorToast({
        title: '비밀번호 변경 실패',
        description: '비밀번호 수정에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { form, onSubmit, isPasswordMatch, formState, isLoading };
};
