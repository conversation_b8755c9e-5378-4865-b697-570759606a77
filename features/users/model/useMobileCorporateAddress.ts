import { useEffect } from 'react';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { MyCorporateProfileFormData } from '../lib/myCorporateProfileSchema';
import { useMyCorporateProfile } from './useMyCorporateProfile';

export const useMobileCorporateAddress = () => {
  const { isApp } = useWebViewRouter();

  const {
    corporateProfileForm,
    corporateProfileFormSubmit,
    isLoading,
    isPostDialog,
    togglePostDialog,
  } = useMyCorporateProfile();

  const { routerPush } = useWebViewRouter();

  const { setValue, getFieldState } = corporateProfileForm;

  const isDirty = getFieldState('address1').isDirty || getFieldState('address2').isDirty;

  const isDisabled = isLoading || !isDirty;

  const onSubmit = (data: MyCorporateProfileFormData) => {
    corporateProfileFormSubmit(data);
    routerPush('/mobile/user/my/profile');
  };

  const handlePostCode = () => {
    if (isApp) {
      WebViewMessage('openPostcode', {});
    } else {
      togglePostDialog();
    }
  };

  useEffect(() => {
    if (!isApp) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'postcodeResult') {
          setValue('address1', data.address, {
            shouldValidate: true,
            shouldDirty: true,
          });
        }
      } catch (error) {
        console.error('Failed to parse message:', error);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [isApp, setValue]);

  return {
    corporateProfileForm,
    corporateProfileFormSubmit: onSubmit,
    isLoading,
    isPostDialog,
    togglePostDialog,
    handlePostCode,
    isDisabled,
  };
};
