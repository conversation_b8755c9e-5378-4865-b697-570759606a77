'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { signOut } from 'next-auth/react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { fetchMyAssets } from '@/features/assets/api/fetchMyAssets';
import { useFetchAccount } from '@/features/deposit/model/useFetchAccount';
import { queries } from '@/features/lib/queries';
import { fetchMySubscriptions } from '@/features/subscription/api/fetchMySubscriptions';

import { AssetEventCode } from '@/entities/assets/types';
import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';
import { createWithdrawReason } from '@/entities/users/api/createWithdrawReason';
import { deleteUser } from '@/entities/users/api/deleteUser';
import { WithdrawalCategory } from '@/entities/users/types';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { UserRole } from '@/shared/types';

import { WithdrawalFormData, withdrawalFormSchema } from '../lib/withdrawalSchema';
import { useFetchUser } from './useFetchUser';

export const useWithdrawal = () => {
  const { successToast, errorToast } = useToast();
  const { userId, session } = useFetchUser();
  const { isVisible, toggleVisibility } = useVisibility();
  const queryClient = useQueryClient();
  const [withdrawalBlock, setWithdrawalBlock] = useState({
    isBlock: false,
    message: '',
  });

  const isInvestor = session && session?.user.role !== UserRole.USR;

  const { data: myAssets } = fetchMyAssets(AssetEventCode.ALLOCATION, !!isInvestor);
  const { account } = useFetchAccount();

  const { data: mySubscriptions } = fetchMySubscriptions(
    {
      page: 0,
      size: 5,
      subscriptionApplyStatusList: [
        SubscriptionApplyStatusList.APPLY,
        SubscriptionApplyStatusList.ALLOT_TARGET,
        SubscriptionApplyStatusList.WAIT,
      ],
    },
    !!isInvestor,
  );

  const form = useForm<WithdrawalFormData>({
    resolver: zodResolver(withdrawalFormSchema),
    mode: 'onChange',
    defaultValues: {
      isAgree: false,
      description: '',
    },
  });

  const { watch } = form;

  const isValid =
    watch('category') === WithdrawalCategory.OTHER
      ? watch('description') && (watch('description')?.length as number) > 19 && watch('isAgree')
      : watch('category') && watch('isAgree');

  const onSubmit = async (data: WithdrawalFormData) => {
    if (!userId) {
      return;
    }

    if (isInvestor) {
      const hasBalance = account?.currentBalance && account?.currentBalance > 0;
      const hasSubscription = mySubscriptions?.totalCount && mySubscriptions?.totalCount > 0;
      const hasAsset = myAssets?.totalCount && myAssets?.totalCount > 0;

      if (hasSubscription && (hasBalance || hasAsset)) {
        handleWithdrawalBlock(
          true,
          `청약 중인 상품과 보유하신 자산이 있습니다.\n 다시 확인해 주세요.`,
        );
        toggleVisibility();
        return;
      }

      if (hasSubscription) {
        handleWithdrawalBlock(true, `청약 중인 상품이 있습니다 \n 다시 확인해 주세요.`);
        toggleVisibility();
        return;
      }

      if (hasBalance || hasAsset) {
        handleWithdrawalBlock(true, `보유하신 자산이 있습니다. \n 다시 확인해 주세요.`);
        toggleVisibility();
        return;
      }
    }

    const { success: successWithdrawReason } = await createWithdrawReason({
      reason: data.category,
      userId,
      description: data.category === WithdrawalCategory.OTHER ? data.description : '',
    });

    const { success } = await deleteUser();

    if (successWithdrawReason && success) {
      successToast({
        title: '회원 탈퇴 성공',
      });

      queryClient.removeQueries({
        queryKey: queries.user,
      });
      await signOut({
        redirectTo: '/',
        redirect: true,
      });
    } else {
      errorToast({
        title: '회원 탈퇴 실패',
      });
    }
  };

  const handleWithdrawalBlock = (isBlock: boolean, message: string) => {
    setWithdrawalBlock({ isBlock, message });
  };

  return {
    form,
    onSubmit,
    isVisible,
    isValid,
    toggleVisibility,
    withdrawalBlock,
    handleWithdrawalBlock,
  };
};
