import { useEffect } from 'react';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { MyProfileFormData } from '../../../features/users/lib/myProfileSchema';
import { useMyProfile } from '../../../features/users/model/useMyProfile';

export const useMobileUserAddress = () => {
  const { isApp } = useWebViewRouter();

  const { profileForm, profileFormSubmit, isLoading, isPostDialog, togglePostDialog } =
    useMyProfile();

  const { routerPush } = useWebViewRouter();

  const { setValue, getFieldState } = profileForm;

  const isDirty = getFieldState('address1').isDirty || getFieldState('address2').isDirty;

  const isDisabled = isLoading || !isDirty;

  const onSubmit = async (data: MyProfileFormData) => {
    await profileFormSubmit(data);
    routerPush('/mobile/user/my/profile');
  };

  const handlePostCode = async () => {
    if (isApp) {
      try {
        const data: any = await WebViewMessage('openPostcode', {});
        if (data.type === 'postcodeResult') {
          setValue('address1', data.address, {
            shouldValidate: true,
            shouldDirty: true,
          });
        }
      } catch (error) {
        console.error('Failed to open postcode:', error);
      }
    } else {
      togglePostDialog();
    }
  };

  return {
    profileForm,
    profileFormSubmit: onSubmit,
    isLoading,
    isPostDialog,
    togglePostDialog,
    handlePostCode,
    isDisabled,
  };
};
