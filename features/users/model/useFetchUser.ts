'use client';

import { useQueryClient } from '@tanstack/react-query';
import { Session } from 'next-auth';
import { useSession } from 'next-auth/react';
import { useEffect, useMemo } from 'react';

import { fetchAccoutExistence } from '@/features/assets/api/fetchAccoutExistence';
import { fetchCorporateAccountExistence } from '@/features/assets/api/fetchCorporateAccountExistence';

import { CorporateUserResponse, GeneralUserResponse } from '@/entities/users/interface';
import { KycStatus } from '@/entities/users/types';

import { UserCat } from '@/shared/types';

import { fetchCorporateUser } from '../api/fetchCorporateUser';
import { fetchUser } from '../api/fetchUser';

export const useFetchUser = () => {
  const queryClient = useQueryClient();
  const { data: liveSession, status } = useSession();

  // 🔑 효율적인 세션 선택: 최신 세션 > 캐시된 세션
  const session = useMemo(() => {
    // 1. 최신 세션이 있으면 우선 사용
    if (liveSession) return liveSession;

    // 2. 로딩 중이거나 최신 세션이 없으면 캐시된 값 사용 (빠른 초기 렌더링)
    return queryClient.getQueryData(['session']) as Session;
  }, [liveSession, queryClient]);

  // 최신 세션으로 캐시 동기화
  useEffect(() => {
    if (liveSession && status === 'authenticated') {
      queryClient.setQueryData(['session'], liveSession);
    }
  }, [liveSession, status, queryClient]);

  const userCat = session?.user?.userCat;

  const isGeneral = useMemo(() => userCat === UserCat.GENERAL, [userCat]);
  const isCorporate = useMemo(() => userCat === UserCat.CORPORATE, [userCat]);

  // 일반 사용자 데이터 조회
  const { data: generalUser } = fetchUser(isGeneral);

  // 일반 사용자 계좌 존재 여부 조회
  const shouldFetchUserAccount = useMemo(
    () => generalUser?.type === UserCat.GENERAL && generalUser.kycStatus === KycStatus.KYC,
    [generalUser?.type, generalUser?.kycStatus],
  );
  const { data: isUserExistAccount, isLoading: isLoadingUserAccount } =
    fetchAccoutExistence(shouldFetchUserAccount);

  // 기업 사용자 데이터 조회
  const { data: corporateUser } = fetchCorporateUser(isCorporate);

  // 기업 사용자 계좌 존재 여부 조회
  const shouldFetchCorporateAccount = useMemo(
    () => corporateUser?.type === UserCat.CORPORATE,
    [corporateUser?.type, corporateUser?.kycStatus],
  );

  const { data: isExistCorporateAccount, isLoading: isLoadingCorporateAccount } =
    fetchCorporateAccountExistence(shouldFetchCorporateAccount);

  const user = useMemo(() => {
    if (generalUser) {
      const { userProfile, ...rest } = generalUser as GeneralUserResponse;
      return {
        ...rest,
        userProfile,
      };
    }

    if (corporateUser) {
      const { userCorporateProfile, ...rest } = corporateUser as CorporateUserResponse;
      return {
        ...rest,
        userCorporateProfile,
      };
    }

    return undefined;
  }, [userCat, generalUser, corporateUser]);

  const userId = useMemo(() => {
    if (userCat === UserCat.GENERAL && generalUser?.userProfile) {
      return generalUser.userProfile.userId;
    }
    if (userCat === UserCat.CORPORATE && corporateUser?.userCorporateProfile) {
      return corporateUser.userCorporateProfile.userId;
    }
    return undefined;
  }, [userCat, generalUser?.userProfile, corporateUser?.userCorporateProfile]);

  const username = useMemo(() => {
    if (userCat === UserCat.GENERAL && generalUser?.userProfile) {
      return generalUser.userProfile.name;
    }
    if (userCat === UserCat.CORPORATE && corporateUser?.userCorporateProfile) {
      return corporateUser.userCorporateProfile.companyName;
    }
    return undefined;
  }, [userCat, generalUser?.userProfile, corporateUser?.userCorporateProfile]);

  const isExistAccount = useMemo(() => {
    // 로딩 중이면 false 반환 (기존 동작 유지하되 로그로 확인)
    if (isLoadingUserAccount || isLoadingCorporateAccount) {
      return false;
    }

    if (userCat === UserCat.GENERAL) {
      const result = !!isUserExistAccount;

      return result;
    }
    if (userCat === UserCat.CORPORATE) {
      const result = !!isExistCorporateAccount;

      return result;
    }
    return false;
  }, [
    userCat,
    isUserExistAccount,
    isExistCorporateAccount,
    isLoadingUserAccount,
    isLoadingCorporateAccount,
  ]);

  return {
    user,
    isExistAccount,
    isGeneral,
    isCorporate,
    userId,
    username,
    session,
  };
};
