import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';

import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { queries } from '@/features/lib/queries';

import { checkMobileNumberDuplication } from '@/entities/auth/api/checkMobileNumberDuplication';
import { updateCorporateUser } from '@/entities/users/api/updateCorporateUser';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

import {
  MyCorporateProfileFormData,
  myCorporateProfileFormSchema,
} from '../lib/myCorporateProfileSchema';
import { useFetchUser } from './useFetchUser';

export const useMyCorporateProfile = () => {
  const { successToast, errorToast } = useToast();
  const { user, isCorporate } = useFetchUser();
  const queryClient = useQueryClient();
  const { verifyIdentity } = useIdentityVerification();
  const corporateProfileForm = useForm<MyCorporateProfileFormData>({
    resolver: zodResolver(myCorporateProfileFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });
  const searchParams = useSearchParams();

  const { isVisible: isPostDialog, toggleVisibility: togglePostDialog } = useVisibility(false);
  const { isVisible: isMobileDialog, toggleVisibility: toggleMobileDialog } = useVisibility(false);
  const { isVisible: isLoading, toggleVisibility: isLoadingToggle } = useVisibility(false);
  const { isVisible: isVerifyLoading, toggleVisibility: isVerifyLoadingToggle } =
    useVisibility(false);

  const handleIdentityVerification = async (state?: string) => {
    // 폼 데이터를 로컬 스토리지에 저장
    const formData = corporateProfileForm.getValues();
    sessionStorage.setItem('corporateProfileFormData', JSON.stringify(formData));

    toggleMobileDialog();
    isLoadingToggle();
    const verifiedResult = await verifyIdentity(state);

    const { verifiedCustomer } = verifiedResult;

    const { data } = await checkMobileNumberDuplication(verifiedCustomer.phoneNumber);

    if (data.isExist) {
      errorToast({
        title: '이미 존재하는 휴대폰 번호입니다.',
      });
      return;
    }

    if (verifiedCustomer) {
      corporateProfileForm.setValue('managerMobileNumber', verifiedCustomer.phoneNumber);
    }
  };

  const formData = useMemo(() => {
    if (!user?.userCorporateProfile) return null;

    return {
      companyName: user.userCorporateProfile.companyName,
      managerMobileNumber: user.userCorporateProfile.managerMobileNumber,
      managerEmail: user.userCorporateProfile.email,
      address1: user.userCorporateProfile.address1,
      address2: user.userCorporateProfile.address2,
      crn: user.userCorporateProfile.crn,
      brn: user.userCorporateProfile.brn,
    };
  }, [user?.userCorporateProfile]);

  useEffect(() => {
    if (formData && isCorporate) {
      corporateProfileForm.reset(formData);
    }
  }, [formData, isCorporate]);

  const corporateProfileFormSubmit = async (data: MyCorporateProfileFormData) => {
    try {
      await updateCorporateUser(data);
      successToast({
        title: '저장이 완료되었습니다.',
      });
      await queryClient.invalidateQueries({ queryKey: queries.user._def });
    } catch (error) {
      errorToast({
        title: '저장에 실패했습니다.',
      });
    }
  };

  useEffect(() => {
    const identityVerificationId = searchParams.get('identityVerificationId'); // 포트원 인증 고유번호
    const identityVerificationTxId = searchParams.get('identityVerificationTxId'); // 요청 시 설정한 유니크 ID
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부

    if (identityVerificationId && identityVerificationTxId) {
      isVerifyLoadingToggle();

      fetch('/api/identity/verify', {
        method: 'POST',
        body: JSON.stringify({ identityVerificationId, identityVerificationTxId, transactionType }),
      })
        .then((res) => res.json())
        .then(async ({ data }) => {
          if (data.verifiedCustomer) {
            if (!data.verifiedCustomer) return;

            const savedFormData = sessionStorage.getItem('corporateProfileFormData');
            const formValues = {
              managerMobileNumber: data.verifiedCustomer.phoneNumber,
              ...(savedFormData ? JSON.parse(savedFormData) : {}),
            };

            // 한 번에 모든 값을 설정
            corporateProfileForm.reset(formValues);

            if (savedFormData) {
              sessionStorage.removeItem('corporateProfileFormData');
            }

            const result = await checkMobileNumberDuplication(formValues.mobileNumber);
            if (result.data.isExist) {
              errorToast({
                title: '이미 존재하는 휴대폰 번호입니다.',
              });
            }
          }
        })
        .finally(() => {
          isVerifyLoadingToggle();
        });
    }
  }, [searchParams]);

  return {
    corporateProfileForm,
    corporateProfileFormSubmit,
    isLoading,
    isVerifyLoading,
    handleIdentityVerification,
    isPostDialog,
    togglePostDialog,
    isMobileDialog,
    toggleMobileDialog,
    isLoadingToggle,
    isVerifyLoadingToggle,
  };
};
