import { z } from 'zod';

import { passwordRegex } from '@/shared/lib/regex';

export const passwordChangeSchema = z
  .object({
    password: z.string().regex(passwordRegex, {
      message: '영문 대소문자, 숫자, 특수문자를 모두 포함한 8~16자리여야 합니다.',
    }),
    newPassword: z.string().regex(passwordRegex, {
      message: '영문 대소문자, 숫자, 특수문자를 모두 포함한 8~16자리여야 합니다.',
    }),
    newPasswordConfirm: z.string(),
  })
  .refine((data) => data.newPassword === data.newPasswordConfirm, {
    message: '비밀번호가 일치하지 않습니다.',
    path: ['newPasswordConfirm'],
  });

export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>;
