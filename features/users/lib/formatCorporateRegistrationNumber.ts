/**
 * 법인등록번호 포맷팅 (00000000-0000000)
 * @param crn - 법인등록번호 (숫자만)
 * @returns 포맷팅된 법인등록번호
 */
export const formatCorporateRegistrationNumber = (crn?: string): string => {
  if (!crn) return '';

  // 숫자만 추출
  const numbers = crn.replace(/[^0-9]/g, '');

  // 13자리가 아니면 원본 반환
  if (numbers.length !== 13) return crn;

  // 00000000-0000000 형식으로 포맷팅
  return `${numbers.slice(0, 8)}-${numbers.slice(8)}`;
};
