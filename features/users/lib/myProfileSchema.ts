import { z } from 'zod';

import { englishNameRegex, koreanNameRegex } from '@/shared/lib/regex';

export const myProfileFormSchema = z.object({
  name: z.string().refine(
    (val) => {
      return koreanNameRegex.test(val) || englishNameRegex.test(val);
    },
    {
      message: '이름은 한글 2~8글자 또는 영문 2~16글자여야 합니다.',
    },
  ),
  mobileNumber: z.string().min(1, { message: '전화번호는 필수입니다.' }),
  email: z.string().email({ message: '이메일 형식이 올바르지 않습니다.' }),
  address1: z.string().optional(),
  address2: z.string().optional(),
});

export type MyProfileFormData = z.infer<typeof myProfileFormSchema>;
