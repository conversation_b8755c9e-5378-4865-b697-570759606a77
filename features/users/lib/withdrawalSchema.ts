import { z } from 'zod';

import { WithdrawalCategory } from '@/entities/users/types';

export const withdrawalFormSchema = z
  .object({
    category: z.nativeEnum(WithdrawalCategory),
    description: z.string().optional(),
    isAgree: z.boolean().refine((data) => !!data, {
      message: '유의사항 동의는 필수입니다.',
    }),
  })
  .superRefine((data, ctx) => {
    if (!data.isAgree) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '유의사항 동의는 필수입니다.',
        path: ['isAgree'],
      });
    }

    if (data.category === WithdrawalCategory.OTHER) {
      if (data.description && data.description?.length < 20) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '20자 이상 입력해주세요.',
          path: ['description'],
        });
      }
    }
  });

export type WithdrawalFormData = z.infer<typeof withdrawalFormSchema>;
