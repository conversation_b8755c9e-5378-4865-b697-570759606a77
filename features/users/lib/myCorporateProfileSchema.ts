import { z } from 'zod';

export const myCorporateProfileFormSchema = z.object({
  companyName: z.string().min(1, { message: '회사명은 필수입니다.' }),
  crn: z.string().min(1, { message: '법인등록번호는 필수입니다.' }),
  brn: z.string().min(1, { message: '사업자등록번호는 필수입니다.' }),
  managerEmail: z.string().email({ message: '이메일 형식이 올바르지 않습니다.' }),
  managerMobileNumber: z.string().min(1, { message: '전화번호는 필수입니다.' }),
  address1: z.string().optional(),
  address2: z.string().optional(),
});

export type MyCorporateProfileFormData = z.infer<typeof myCorporateProfileFormSchema>;
