import { z } from 'zod';

import { passwordRegex } from '@/shared/lib/regex';

export const passwordResetSchema = z
  .object({
    password: z.string().regex(passwordRegex, {
      message: '영문 대소문자, 숫자, 특수문자를 모두 포함한 8~16자리여야 합니다.',
    }),
    rePassword: z.string().min(1, { message: '비밀번호 확인은 필수입니다.' }),
  })
  .refine((data) => data.password === data.rePassword, {
    message: '비밀번호가 일치하지 않습니다.',
    path: ['rePassword'],
  });

export type PasswordResetFormData = z.infer<typeof passwordResetSchema>;
