import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import { useDatePickerDialog } from '@/shared/model/useDatePickerDialog';

export const useDisclosureSearchForm = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [searchFormState, setSearchFormState] = useState({
    startDate: searchParams.get('createdAt')?.split(',')[0] || '',
    endDate: searchParams.get('createdAt')?.split(',')[1] || '',
    keyword: searchParams.get('keyword') || '',
    issuer: searchParams.get('issuer') || '',
    category: searchParams.get('category') || '',
    securitiesType: searchParams.get('securitiesType') || '',
  });

  const { isDatePickerOpen, handleDatePickerClick } = useDatePickerDialog();

  const handleformState = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchFormState({ ...searchFormState, [e.target.name]: e.target.value });
  };

  const handleCategoryChange = (value: string) => {
    setSearchFormState({ ...searchFormState, category: value });
  };

  const handleTypeChange = (value: string) => {
    setSearchFormState({ ...searchFormState, securitiesType: value });
  };

  const handleStartDateChange = (value: string | undefined) => {
    setSearchFormState({ ...searchFormState, startDate: value || '' });
  };

  const handleEndDateChange = (value: string | undefined) => {
    setSearchFormState({ ...searchFormState, endDate: value || '' });
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const searchParams = new URLSearchParams();

    if (searchFormState.startDate || searchFormState.endDate) {
      searchParams.set(
        'createdAt',
        `${searchFormState.startDate.toString()},${searchFormState.endDate.toString()}`,
      );
    }

    if (searchFormState.keyword) {
      searchParams.set('keyword', searchFormState.keyword);
    }
    if (searchFormState.securitiesType) {
      searchParams.set('securitiesType', searchFormState.securitiesType);
    }
    if (searchFormState.issuer) {
      searchParams.set('issuer', searchFormState.issuer);
    }
    if (searchFormState.category) {
      searchParams.set('category', searchFormState.category);
    }
    router.replace(`${pathname}?${searchParams.toString()}`, { scroll: false });
  };

  const handleReset = () => {
    setSearchFormState({
      startDate: '',
      endDate: '',
      keyword: '',
      issuer: '',
      category: '',
      securitiesType: '',
    });

    const newSearchParams = new URLSearchParams();
    router.replace(`${pathname}?${newSearchParams.toString()}`, {
      scroll: false,
    });
  };

  return {
    searchFormState,
    handleformState,
    handleSearch,
    handleTypeChange,
    handleCategoryChange,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
    isDatePickerOpen,
    handleReset,
  };
};
