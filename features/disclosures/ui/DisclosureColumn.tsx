import { ColumnDef } from '@tanstack/react-table';

import {
  Disclosure,
  DisclosureCategoryLabel,
  DisclosureScopeLabel,
} from '@/entities/disclosures/types';
import { SecuritiesTypeLabel } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';

const { YYYYMMDD } = utilFormats();

export const DisclosureColumn: ColumnDef<Disclosure>[] = [
  {
    accessorKey: 'createdAt',
    size: 250,
    header: '작성일',
    cell: ({ row }) => (
      <div className="text-center">{YYYYMMDD(row.original.createdAt, 'YYYY-MM-DD hh:mm')}</div>
    ),
  },
  {
    accessorKey: 'securitiesType',
    size: 250,
    header: '증권분류',
    cell: ({ row }) => (
      <div className="text-center">{SecuritiesTypeLabel[row.original.securitiesType]}</div>
    ),
  },
  {
    accessorKey: 'securitiesName',
    size: 400,
    header: '증권명',
    cell: ({ row }) => <div className="line-clamp-1">{row.original.securitiesName}</div>,
  },
  {
    accessorKey: 'issuer',
    header: '발행인',
    size: 250,
    cell: ({ row }) => <div className="text-center">{row.original.issuer}</div>,
  },
  {
    accessorKey: 'category',
    header: '공시구분',
    cell: ({ row }) => (
      <div className="text-center">{DisclosureCategoryLabel[row.original.category]}</div>
    ),
  },
  {
    accessorKey: 'scope',

    header: '공개범위',
    cell: ({ row }) => (
      <div className="text-center">{DisclosureScopeLabel[row.original.scope]}</div>
    ),
  },
];
