'use client';

import 'react-day-picker/dist/style.css';

import {
  DisclosureCategorySearchOptions,
  SecuritiesTypeSearchOptions,
} from '@/entities/disclosures/config';

import { CommonSelect } from '@/shared/ui/CommonSelect';
import { CustomDayPicker } from '@/shared/ui/CustomDayPicker';
import { FormResetButton } from '@/shared/ui/FormResetButton';
import { SearchButton } from '@/shared/ui/SearchButton';
import { Input } from '@/shared/ui/shadcn/input';
import { Label } from '@/shared/ui/shadcn/label';

import { useDisclosureSearchForm } from '../model/useDisclosureSearchForm';

export const DisclosureSearchForm = () => {
  const {
    searchFormState,
    handleformState,
    handleSearch,
    handleTypeChange,
    handleCategoryChange,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
    isDatePickerOpen,
    handleReset,
  } = useDisclosureSearchForm();

  return (
    <form
      className="mb-16 flex flex-col justify-between gap-5 rounded-[20px] border border-gray-300 bg-primary-100/40 px-5 py-8 md:gap-3 md:px-10 md:py-12"
      onSubmit={handleSearch}
    >
      <div className="md:flex">
        <div className="flex flex-col gap-3 sm:w-full sm:flex-row sm:items-center sm:gap-3 ml:basis-1/2 ml:pr-4">
          <Label className="shrink-0 text-sm font-semibold sm:min-w-16 sm:text-base ml:min-w-20">
            조회기간
          </Label>
          <div className="relative flex w-full items-center justify-between gap-4 rounded-lg border border-gray-300 bg-white px-4 py-[10px] md:static">
            <CustomDayPicker
              isOpen={isDatePickerOpen.startDate}
              handleDatePickerClick={handleDatePickerClick}
              type="startDate"
              maxDate={searchFormState.endDate}
              selectedDate={searchFormState.startDate}
              handleDateChange={handleStartDateChange}
              label="시작 날짜"
            />
            <p>~</p>
            <CustomDayPicker
              isOpen={isDatePickerOpen.endDate}
              handleDatePickerClick={handleDatePickerClick}
              type="endDate"
              minDate={searchFormState.startDate}
              selectedDate={searchFormState.endDate}
              handleDateChange={handleEndDateChange}
              label="종료 날짜"
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-5 sm:flex-row sm:gap-6 ml:gap-8">
        <div className="flex basis-1/2 flex-col gap-3 sm:flex-row sm:items-center sm:gap-3">
          <Label className="text-sm font-semibold sm:min-w-16 sm:text-base ml:min-w-20">
            증권명
          </Label>
          <Input
            value={searchFormState.keyword}
            name="keyword"
            onChange={handleformState}
            className="min-w-[228px] rounded-lg border border-gray-300 bg-white"
            placeholder="검색어를 입력해주세요"
          />
        </div>
        <div className="flex basis-1/2 flex-col gap-3 sm:flex-row sm:items-center sm:gap-3">
          <Label className="text-sm font-semibold sm:min-w-16 sm:text-base ml:min-w-20">
            공시구분
          </Label>
          <div className="w-full">
            <CommonSelect
              value={searchFormState.category}
              onChange={handleCategoryChange}
              options={DisclosureCategorySearchOptions}
              placeholder="전체"
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="mt-1 flex justify-center gap-2 sm:mt-7">
        <FormResetButton onClick={handleReset} />
        <SearchButton
          className="h-[44px] w-full text-sm font-semibold sm:h-12 sm:w-[96px]"
          text="검색"
        />
      </div>
    </form>
  );
};
