import { useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { BannerRequest } from '@/entities/banners/interface';
import { Banner } from '@/entities/banners/types';

import { ListResponse } from '@/shared/interface';

export const fetchBanners = (params: BannerRequest, banners?: ListResponse<Banner>) => {
  return useQuery({
    ...queries.banners.list(params),
    initialData: banners,
  });
};
