import { useSuspenseQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { Event } from '@/entities/events/types';

import { ListRequest, ListResponse } from '@/shared/interface';

export const fetchEvents = (filters: ListRequest, events?: ListResponse<Event>) => {
  return useSuspenseQuery({
    ...queries.events.list(filters),
    initialData: events,
  });
};
