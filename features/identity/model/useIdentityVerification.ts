'use client';

import * as PortOne from '@portone/browser-sdk/v2';
import { useState } from 'react';

import env from '@/shared/lib/env.schema';

export const useIdentityVerification = () => {
  const [isLoading, setIsLoading] = useState(false);

  const verifyIdentity = async (state?: string) => {
    setIsLoading(true);
    try {
      const response = await PortOne.requestIdentityVerification({
        storeId: env.NEXT_PUBLIC_STORE_ID,
        identityVerificationId: `identity-verification-${Math.random().toString(36).substring(2)}`,
        channelKey: env.NEXT_PUBLIC_CHANNE_KEY,
        redirectUrl: state ? `${window.location.href}&state=${state}` : window.location.href,
        popup: {
          center: true,
        },
      });

      if (response?.code !== undefined) {
        throw new Error(response.message);
      }

      const result = await fetch('/api/identity/verify', {
        method: 'POST',
        body: JSON.stringify(response),
      }).then((res) => res.json());

      if (!result.success) {
        throw new Error(result.message);
      }

      return result.data;
    } catch (error) {
      const message = error instanceof Error ? error.message : '본인확인에 실패했습니다.';
      alert(message);
      return undefined;
    } finally {
      setIsLoading(false);
    }
  };

  return { verifyIdentity, isLoading };
};
