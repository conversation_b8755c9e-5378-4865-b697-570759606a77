import env from '@/shared/lib/env.schema';

const PORTONE_API_URL = 'https://api.portone.io';

export const identityVerify = async (identityVerificationId: string) => {
  const response = await fetch(
    `${PORTONE_API_URL}/identity-verifications/${encodeURIComponent(identityVerificationId)}`,
    {
      headers: {
        Authorization: `PortOne ${env.PORTONE_API_SECRET}`,
      },
    },
  );

  if (!response.ok) {
    throw new Error(`Verification failed: ${await response.json()}`);
  }

  return response.json();
};
