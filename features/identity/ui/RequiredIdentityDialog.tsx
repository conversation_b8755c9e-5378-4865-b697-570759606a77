import { InformationCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/shadcn/dialog';

interface RequiredIdentityDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  handleVerify: () => void;
}

export const RequiredIdentityDialog = ({
  isOpen,
  handleOpen,
  handleVerify,
}: RequiredIdentityDialogProps) => {
  const { routerPush } = useWebViewRouter();

  const { screenSize } = useScreenStore();

  const handleWithdraw = () => {
    if (screenSize === ScreenSize.MOBILE) {
      routerPush('/mobile/user/my/withdrawal');
    } else {
      routerPush('/user/my/withdrawal');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpen}>
      <DialogContent className="flex max-w-[327px] flex-col gap-10 rounded-[16px] bg-white px-6 sm:max-w-[386px]">
        <DialogHeader className="mt-6 space-y-4">
          <DialogTitle className="whitespace-pre-line text-center text-base font-semibold">
            휴대폰 본인인증 진행안내
          </DialogTitle>

          <DialogDescription className="whitespace-pre-line text-center text-sm text-gray-900">
            서비스 이용약관에 따라 뉴밋 서비스를 <br /> 이용하시려면 본인인증 절차가 필요합니다.{' '}
            <br /> 휴대폰 본인인증을 진행해 주세요.
          </DialogDescription>
        </DialogHeader>
        <div className="flex gap-1 rounded-[10px] bg-gray-50 py-3 text-gray-600">
          <InformationCircleIcon className="h-4 min-h-4 w-4 min-w-4 text-gray-500" />
          <p className="text-xs">
            서비스 이용약관에 따라 휴대폰 본인인증 미진행 시 서비스 이용이 불가합니다. 더 이상
            이용을 원치 않으실 경우 회원 탈퇴를 진행하실 수 있습니다.
            <button onClick={handleWithdraw} className="inline pl-1 font-medium underline">
              회원 탈퇴
            </button>
          </p>
        </div>

        <div className="mb-[10px] flex justify-center gap-2">
          <SecondaryButton className="w-full" onClick={handleOpen} text="취소" />

          <PrimaryButton
            type="submit"
            className={`h-[44px] w-full font-semibold sm:h-12`}
            onClick={handleVerify}
            text="본인인증 진행"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
