import React from 'react';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';

interface IdentityDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  handleVerify: () => void;
  social?: boolean;
}

export const IdentityDialog = ({
  isOpen,
  handleOpen,
  handleVerify,
  social = true,
}: IdentityDialogProps) => {
  return (
    <ConfirmDialog
      isOpen={isOpen}
      handleOpen={handleOpen}
      handleAction={handleVerify}
      text="확인"
      title="휴대폰 본인인증 안내"
      description={`${social ? '소셜 회원가입' : '이메일 회원가입'} 진행을 위해 \n 휴대폰 본인인증을 진행해 주세요.`}
    />
  );
};
