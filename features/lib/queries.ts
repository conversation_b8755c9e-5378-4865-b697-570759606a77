import { createQ<PERSON>y<PERSON><PERSON><PERSON>, mergeQuery<PERSON>eys } from '@lukemorales/query-key-factory';

import { checkAccountExistence } from '@/entities/assets/api/checkAccountExistence';
import { checkCorporateAccountExistence } from '@/entities/assets/api/checkCorporateAccountExistence';
import { getCorporateAccount } from '@/entities/assets/api/getCorporateAccount';
import {
  getCorporateMyDepositHistory,
  getCorporateMyDepositHistoryList,
} from '@/entities/assets/api/getCorporateMyDepositHistory';
import { getMyAssets } from '@/entities/assets/api/getMyAssets';
import {
  getMyDepositHistory,
  getMyDepositHistoryList,
} from '@/entities/assets/api/getMyDepositHistory';
import { getUserAccount } from '@/entities/assets/api/getUserAccount';
import { GetMyDepositHistoryPayload } from '@/entities/assets/interface';
import { AssetEventCode } from '@/entities/assets/types';
import { getBanners } from '@/entities/banners/api/getBanners';
import { BannerRequest } from '@/entities/banners/interface';
import { getComment } from '@/entities/comments/api/getComment';
import { getComments } from '@/entities/comments/api/getComments';
import { getCuration } from '@/entities/curations/api/getCuration';
import { getCurations, getInfiniteCurations } from '@/entities/curations/api/getCurations';
import { CurationListRequest } from '@/entities/curations/interface';
import { getDisclosure } from '@/entities/disclosures/api/getDisclosure';
import { getDisclosures } from '@/entities/disclosures/api/getDisclosures';
import { DisclosureListRequest } from '@/entities/disclosures/interface';
import { getEvent } from '@/entities/events/api/getEvent';
import { getEvents } from '@/entities/events/api/getEvents';
import { getFaqs } from '@/entities/faqs/api/getFaqs';
import { getInvestorFaqs } from '@/entities/faqs/api/getInvestorFaqs';
import { FaqListRequest, InvestorFaqListRequest } from '@/entities/faqs/interface';
import { getMyInquiryDetail } from '@/entities/inquiries/api/getMyInquiryDetail';
import {
  getMyInquiryHistory,
  getMyInquiryHistoryList,
} from '@/entities/inquiries/api/getMyInquiryHistory';
import { getSubscriptionInquiries } from '@/entities/inquiries/api/getSubscriptionInquiries';
import { getInvestorLimit } from '@/entities/investor/api/getInvestorLimit';
import { getInvestorPropensityTest } from '@/entities/investor/api/getInvestorPropensityTest';
import { getInvestorSuitabilityTest } from '@/entities/investor/api/getInvestorSuitabilityTest';
import { getNews } from '@/entities/news/api/getNews';
import { getSubscriptionNews } from '@/entities/news/api/getSubscriptionNews';
import { NewsListRequest } from '@/entities/news/interface';
import { getNotice } from '@/entities/notices/api/getNotice';
import { getNotices } from '@/entities/notices/api/getNotices';
import {
  getNotifications,
  getNotificationsList,
} from '@/entities/notifications/api/getNotifications';
import { NotificationRequest } from '@/entities/notifications/interface';
import { checkSubscriptionAllotment } from '@/entities/subscriptions/api/checkSubscriptionAllotment';
import { getMySubscription } from '@/entities/subscriptions/api/getMySubscription';
import { getMySubscriptions } from '@/entities/subscriptions/api/getMySubscriptions';
import { getSubscription } from '@/entities/subscriptions/api/getSubscription';
import { getSubscriptionCmsDetail } from '@/entities/subscriptions/api/getSubscriptionCmsDetail';
import { getSubscriptions } from '@/entities/subscriptions/api/getSubscriptions';
import { getSubscriptionStatistics } from '@/entities/subscriptions/api/getSubscriptionStatistics';
import { MySubscriptionsRequest, SubscriptionsRequest } from '@/entities/subscriptions/interface';
import { getTerms } from '@/entities/terms/api/getTerms';
import { TermsRequest } from '@/entities/terms/interface';
import { getCorporateUser } from '@/entities/users/api/getCorporateUser';
import { getUser } from '@/entities/users/api/getUser';
import { getUserNotification } from '@/entities/users/api/getUserNotification';

import { ListRequest } from '@/shared/interface';

import { NoticeListRequest } from '../notices/interface';

const curations = createQueryKeys('curations', {
  list: (filters: CurationListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getCurations(filters),
  }),
  infinite: (filters: CurationListRequest) => ({
    queryKey: [{ filters }],
    queryFn: ({ pageParam }: { pageParam: number }) =>
      getInfiniteCurations({ ...filters, page: pageParam }),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getCuration(id),
  }),
});

const notices = createQueryKeys('notices', {
  list: (filters: NoticeListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getNotices(filters),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getNotice(id),
  }),
});

const events = createQueryKeys('events', {
  list: (filters: ListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getEvents(filters),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getEvent(id),
  }),
});

const comments = createQueryKeys('comments', {
  list: (contentId: string, filters: ListRequest) => ({
    queryKey: [{ contentId, filters }],
    queryFn: () => getComments(contentId, filters),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getComment(id),
  }),
});

const news = createQueryKeys('news', {
  list: (filters: NewsListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getNews(filters),
  }),
  subscriptionNews: (id: string, params: ListRequest) => ({
    queryKey: [{ id, params }],
    queryFn: () => getSubscriptionNews(id, params),
  }),
});

const banners = createQueryKeys('banners', {
  list: (filters: BannerRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getBanners(filters),
  }),
});

const terms = createQueryKeys('terms', {
  category: (filters: TermsRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getTerms(filters),
  }),
});

const faqs = createQueryKeys('faqs', {
  list: (filters: FaqListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getFaqs(filters),
  }),
  investorList: (filters: InvestorFaqListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getInvestorFaqs(filters),
  }),
});

const disclosures = createQueryKeys('disclosures', {
  list: (filters: DisclosureListRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getDisclosures(filters),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getDisclosure(id),
  }),
});

const user = createQueryKeys('user', {
  profile: () => ({
    queryKey: [''],
    queryFn: getUser,
  }),
  corporateProfile: () => ({
    queryKey: [''],
    queryFn: getCorporateUser,
  }),
  notification: () => ({
    queryKey: ['notification'],
    queryFn: getUserNotification,
  }),
});

const inquiries = createQueryKeys('inquiries', {
  history: (userId: string, filters: ListRequest) => ({
    queryKey: [{ userId, filters }],
    queryFn: () => getMyInquiryHistory(userId, filters),
  }),
  detail: (inquiryId: string) => ({
    queryKey: [inquiryId],
    queryFn: () => getMyInquiryDetail(inquiryId),
  }),
  infinite: (userId: string, filters: CurationListRequest) => ({
    queryKey: [userId, { filters }],
    queryFn: ({ pageParam }: { pageParam: number }) =>
      getMyInquiryHistoryList(userId, { ...filters, page: pageParam }),
  }),
  subscriptionInquiries: (securitiesId: string, userId: string, params: ListRequest) => ({
    queryKey: [{ securitiesId, userId, params }],
    queryFn: () => getSubscriptionInquiries({ securitiesId, userId, params }),
  }),
});

const investor = createQueryKeys('investor', {
  suitabilityTest: () => ({
    queryKey: [''],
    queryFn: () => getInvestorSuitabilityTest(),
  }),
  propensityTest: () => ({
    queryKey: [''],
    queryFn: () => getInvestorPropensityTest(),
  }),
  investorLimit: (issuerId: string) => ({
    queryKey: [{ issuerId }],
    queryFn: () => getInvestorLimit(issuerId),
  }),
});

const subscriptions = createQueryKeys('subscriptions', {
  list: (filters: SubscriptionsRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getSubscriptions(filters),
  }),
  detail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getSubscription(id),
  }),
  myList: (filters: MySubscriptionsRequest) => ({
    queryKey: [{ filters }],
    queryFn: () => getMySubscriptions(filters),
  }),
  myDetail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getMySubscription(id),
  }),
  cmsDetail: (id: string) => ({
    queryKey: [{ id }],
    queryFn: () => getSubscriptionCmsDetail(id),
  }),
  statistics: (subscriptionId: string) => ({
    queryKey: [{ subscriptionId }],
    queryFn: () => getSubscriptionStatistics(subscriptionId),
  }),
  checkSubscriptionAllotment: (subscriptionInfoId: string) => ({
    queryKey: [{ subscriptionInfoId }],
    queryFn: () => checkSubscriptionAllotment(subscriptionInfoId),
  }),
});

const assets = createQueryKeys('assets', {
  userAccount: () => ({
    queryKey: [''],
    queryFn: getUserAccount,
  }),
  myAssets: (eventCodeList: AssetEventCode) => ({
    queryKey: [eventCodeList],
    queryFn: () => getMyAssets({ eventCodeList }),
  }),
  corporateAccount: () => ({
    queryKey: [''],
    queryFn: getCorporateAccount,
  }),
  myDepositHistory: (payload: GetMyDepositHistoryPayload) => ({
    queryKey: [payload],
    queryFn: () => getMyDepositHistory(payload),
  }),
  myDepositHistoryList: (payload: GetMyDepositHistoryPayload) => ({
    queryKey: [payload],
    queryFn: ({ pageParam }: { pageParam: number }) =>
      getMyDepositHistoryList({ ...payload, page: pageParam }),
  }),
  corporateDepositHistory: (payload: GetMyDepositHistoryPayload) => ({
    queryKey: [payload],
    queryFn: () => getCorporateMyDepositHistory(payload),
  }),
  corporateDepositHistoryList: (payload: GetMyDepositHistoryPayload) => ({
    queryKey: [payload],
    queryFn: ({ pageParam }: { pageParam: number }) =>
      getCorporateMyDepositHistoryList({ ...payload, page: pageParam }),
  }),
  checkAccountExistence: () => ({
    queryKey: [''],
    queryFn: () => checkAccountExistence(),
  }),
  checkCorporateAccountExistence: () => ({
    queryKey: [''],
    queryFn: () => checkCorporateAccountExistence(),
  }),
});

const notifications = createQueryKeys('notifications', {
  list: (userId: string, params: NotificationRequest) => ({
    queryKey: [{ userId, params }],
    queryFn: () => getNotifications(userId, params),
  }),
  infinite: (userId: string, params: NotificationRequest) => ({
    queryKey: [userId, { params }],
    queryFn: ({ pageParam }: { pageParam: number }) =>
      getNotificationsList(userId, { ...params, page: pageParam }),
  }),
});

export const queries = mergeQueryKeys(
  curations,
  notices,
  events,
  news,
  banners,
  terms,
  faqs,
  disclosures,
  user,
  inquiries,
  investor,
  comments,
  subscriptions,
  assets,
  notifications,
);
