import { useSuspenseQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { NewsListRequest } from '@/entities/news/interface';
import { News } from '@/entities/news/types';

import { ListResponse } from '@/shared/interface';

export const fetchNews = (params: NewsListRequest, news?: ListResponse<News>) => {
  return useSuspenseQuery({
    ...queries.news.list(params),
    initialData: news,
  });
};
