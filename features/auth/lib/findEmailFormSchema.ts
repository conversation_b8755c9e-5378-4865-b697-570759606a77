import { z } from 'zod';

import { phoneRegex } from '@/shared/lib/regex';

export const findEmailFormSchema = z.object({
  mobileNumber: z.string().regex(phoneRegex, '올바른 휴대전화번호를 입력하세요.'),
  mobileVerifiedCode: z.string().min(6, { message: '올바른 인증번호를 입력하세요.' }),
  isMobileVerified: z.boolean().refine((val) => val === true, {
    message: '휴대폰 인증이 필요합니다.',
  }),
  name: z.string(),
  apiKey: z.string(),
});

export type FindEmailFormData = z.infer<typeof findEmailFormSchema>;
