import { z } from 'zod';

import { englishNameRegex, koreanNameRegex, passwordRegex, phoneRegex } from '@/shared/lib/regex';

export const formSchema = z
  .object({
    email: z
      .string()
      .min(1, { message: '아이디를 입력해주세요.' })
      .email({ message: '올바른 이메일 형식이 아닙니다.' }),
    isEmailVerified: z.boolean().refine((val) => val === true, {
      message: '이메일 인증이 필요합니다.',
    }),
    emailVerifiedCode: z.string().length(6, { message: '올바른 인증번호를 입력하세요.' }),
    password: z.string().regex(passwordRegex, {
      message: '영문 대소문자, 숫자, 특수문자를 모두 포함한 8~16자리여야 합니다.',
    }),
    rePassword: z.string().min(1, { message: '비밀번호 확인은 필수입니다.' }),
    mobileNumber: z
      .string()
      .min(10, { message: '올바른 휴대전화번호를 입력하세요.' })
      .regex(phoneRegex, {
        message: '올바른 휴대전화번호를 입력하세요.',
      }),
    mobileVerifiedCode: z.string().length(6, { message: '올바른 인증번호를 입력하세요.' }),
    isMobileVerified: z.boolean().refine((val) => val === true, {
      message: '휴대폰 인증이 필요합니다.',
    }),
    name: z.string().refine(
      (val) => {
        return koreanNameRegex.test(val) || englishNameRegex.test(val);
      },
      {
        message: '이름은 한글 2~8글자 또는 영문 2~16글자여야 합니다.',
      },
    ),
    isTermsAgree: z.boolean().refine((val) => val === true, {
      message: '약관에 동의해주세요.',
    }),
    isPrivacyAgree: z.boolean().refine((val) => val === true, {
      message: '개인정보 수집 및 이용에 동의해주세요.',
    }),
    isEmailAgree: z.boolean(),
    isSmsAgree: z.boolean(),
    gender: z.string().optional(),
    birthDate: z.string().optional(),
  })
  .refine((data) => data.password === data.rePassword, {
    message: '비밀번호가 일치하지 않습니다.',
    path: ['rePassword'], // 에러를 표시할 필드
  });

export type SignUpFormData = z.infer<typeof formSchema>;
