import { z } from 'zod';

import { phoneRegex } from '@/shared/lib/regex';

export const socialSignUpFormSchema = z.object({
  socialId: z.union([z.string(), z.number()]),
  gender: z.string().optional(),
  birthDate: z.string().optional(),
  email: z
    .string()
    .min(1, { message: '아이디를 입력해주세요.' })
    .email({ message: '올바른 이메일 형식이 아닙니다.' }),
  isEmailVerified: z.boolean().refine((val) => val === true, {
    message: '이메일 인증이 필요합니다.',
  }),
  emailVerifiedCode: z.string().min(1, { message: '인증번호를 입력해주세요.' }),
  mobileNumber: z.string().regex(phoneRegex, '올바른 휴대전화번호를 입력하세요.'),
  mobileVerifiedCode: z.string().min(1, { message: '올바른 인증번호를 입력하세요.' }),
  isMobileVerified: z.boolean().refine((val) => val === true, {
    message: '휴대폰 인증이 필요합니다.',
  }),
  name: z.string(),
  isTermsAgree: z.boolean().refine((val) => val === true, {
    message: '약관에 동의해주세요.',
  }),
  isPrivacyAgree: z.boolean().refine((val) => val === true, {
    message: '개인정보 수집 및 이용에 동의해주세요.',
  }),
  isMarketingAgree: z.boolean().refine((val) => val === true, {
    message: '마케팅 수집 및 이용에 동의해주세요.',
  }),
  isEmailAgree: z.boolean(),
  isSmsAgree: z.boolean(),
});

export type SocialSignUpFormData = z.infer<typeof socialSignUpFormSchema>;
