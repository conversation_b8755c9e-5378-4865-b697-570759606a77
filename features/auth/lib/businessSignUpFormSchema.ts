import { z } from 'zod';

import { englishNameRegex, koreanNameRegex, phoneRegex } from '@/shared/lib/regex';

export const businessSignUpFormSchema = z.object({
  email: z
    .string()
    .min(1, { message: '아이디를 입력해주세요.' })
    .email({ message: '올바른 이메일 형식이 아닙니다.' }),
  isEmailVerified: z.boolean().refine((val) => val === true, {
    message: '이메일 인증이 필요합니다.',
  }),
  emailVerifiedCode: z.string().length(6, { message: '올바른 인증번호를 입력하세요.' }),
  brn: z
    .string()
    .min(1, { message: '법인등록번호를 입력하세요.' })
    .max(13, {
      message: '법인등록번호는 13자리여야 합니다.',
    })
    .regex(/^\d+$/, { message: '법인등록번호는 숫자만 입력 가능합니다.' }),
  crn: z
    .string()
    .min(1, { message: '사업자등록번호를 입력하세요.' })
    .max(10, {
      message: '사업자등록번호는 10자리여야 합니다.',
    })
    .regex(/^\d+$/, { message: '사업자등록번호는 숫자만 입력 가능합니다.' }),
  representativeName: z.string().min(1, { message: '대표자 이름을 입력하세요.' }),
  companyName: z.string().min(1, { message: '회사 이름을 입력하세요.' }),
  brc: z.array(z.any()).min(1, { message: '사업자등록증을 업로드해주세요.' }),
  managerMobileNumber: z
    .string()
    .min(10, { message: '올바른 휴대전화번호를 입력하세요.' })
    .regex(phoneRegex, {
      message: '올바른 휴대전화번호를 입력하세요.',
    }),
  mobileVerifiedCode: z.string().length(6, { message: '올바른 인증번호를 입력하세요.' }),
  isMobileVerified: z.boolean().refine((val) => val === true, {
    message: '휴대폰 인증이 필요합니다.',
  }),
  managerName: z.string().refine(
    (val) => {
      return koreanNameRegex.test(val) || englishNameRegex.test(val);
    },
    {
      message: '이름은 한글 2~8글자 또는 영문 2~16글자여야 합니다.',
    },
  ),
  isTermsAgree: z.boolean().refine((val) => val === true, {
    message: '약관에 동의해주세요.',
  }),
  isPrivacyAgree: z.boolean().refine((val) => val === true, {
    message: '개인정보 수집 및 이용에 동의해주세요.',
  }),
});

export type BusinessSignUpFormData = z.infer<typeof businessSignUpFormSchema>;
