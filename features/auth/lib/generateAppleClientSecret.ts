import jwt from 'jsonwebtoken';

import env from '@/shared/lib/env.schema';

export function generateAppleClientSecret(type?: 'app') {
  const token = jwt.sign({}, env.APPLE_PRIVATE_KEY, {
    algorithm: 'ES256',
    keyid: env.APPLE_KEY_ID,
    issuer: env.APPLE_TEAM_ID,
    subject: type === 'app' ? env.APPLE_BUNDLE_ID : env.NEXT_PUBLIC_APPLE_CLIENT_ID,
    audience: 'https://appleid.apple.com',
    expiresIn: '180d',
  });

  return token;
}
