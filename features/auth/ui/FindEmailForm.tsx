import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { FindEmailFormData } from '../lib/findEmailFormSchema';
import { useVerification } from '../model/useVerification';

interface FindEmailFormProps {
  form: UseFormReturn<FindEmailFormData>;
  onSubmit: () => void;
}

export const FindEmailForm = ({ form, onSubmit }: FindEmailFormProps) => {
  const { register, control, getFieldState, watch, setValue, setError } = form;

  const { verifyCode, verifyContact, verificationTimerText, verificationTimer } = useVerification(
    VerificationType.SMS,
  );

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'mobileNumber' && value.mobileNumber !== form.getValues('mobileNumber')) {
        setValue('isMobileVerified', false);
        setValue('mobileVerifiedCode', '');
        setValue('apiKey', '');
      }
    });

    return () => subscription.unsubscribe();
  }, [form, setValue]);

  const verifyMobileCode = async () => {
    try {
      const { data } = await verifyCode(watch('mobileNumber'), watch('mobileVerifiedCode'));

      setValue('apiKey', data.token);
      setValue('isMobileVerified', true);

      form.trigger();
    } catch (error) {
      setError('mobileVerifiedCode', {
        type: 'manual',
        message: '올바른 인증번호를 입력하세요.',
      });
    }
  };

  const handleSubmit = () => {
    if (!watch('isMobileVerified')) {
      setError('mobileVerifiedCode', {
        type: 'manual',
        message: '휴대폰 인증이 필요합니다.',
      });
      return;
    }
    onSubmit();
  };

  const disabled = !(
    watch('name') &&
    watch('mobileNumber') &&
    watch('isMobileVerified') &&
    !getFieldState('name').invalid &&
    !getFieldState('mobileNumber').invalid
  );

  return (
    <div className="flex flex-col justify-between sm:h-auto">
      <div>
        <h4 className="mb-12 text-xl font-bold sm:text-2xl">
          아이디를 잊으셨나요? <br />
          아래의 정보를 입력해주세요.
        </h4>
        <Form {...form}>
          <form className="space-y-8 sm:space-y-10">
            <InputField
              form={form}
              label="이름"
              name="name"
              placeholder="이름 입력"
              readOnly={watch('isMobileVerified')}
            />

            <div className="space-y-2">
              <div className="flex items-end gap-2">
                <InputField
                  form={form}
                  label="휴대폰 번호"
                  name="mobileNumber"
                  placeholder="휴대폰 번호 입력"
                  type="tel"
                  readOnly={watch('isMobileVerified')}
                />
                <SecondaryButton
                  onClick={() => verifyContact(watch('mobileNumber'))}
                  type="button"
                  disabled={
                    !(watch('mobileNumber') && !getFieldState('mobileNumber').invalid) ||
                    watch('isMobileVerified')
                  }
                  text={verificationTimer ? '재요청' : '인증하기'}
                  className="mt-7 h-[44px] w-[90px] sm:h-12"
                />
              </div>

              {verificationTimer && !watch('isMobileVerified') && (
                <div className="flex gap-2">
                  <div className="relative w-full">
                    <InputField
                      form={form}
                      name="mobileVerifiedCode"
                      placeholder="인증번호 입력"
                      type="text"
                      maxLength={6}
                    />
                    <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                      {verificationTimerText}
                    </span>
                  </div>
                  <SecondaryButton
                    type="button"
                    text="확인"
                    disabled={
                      !watch('mobileVerifiedCode') ||
                      getFieldState('mobileVerifiedCode')?.invalid ||
                      !verificationTimer
                    }
                    className="h-[44px] w-[90px] sm:h-12"
                    onClick={verifyMobileCode}
                  />
                </div>
              )}

              {watch('isMobileVerified') && (
                <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                  <CheckCircleIcon className="h-4 w-4" color="green" />
                  휴대폰 인증 완료
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          className="h-12 w-full text-base sm:mt-16"
          text="아이디 찾기"
          disabled={disabled}
          onClick={handleSubmit}
        />
      </div>
    </div>
  );
};
