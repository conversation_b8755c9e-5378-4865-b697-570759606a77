import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { InputField } from '@/shared/ui/InputField';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { MobileVerifyFormData, mobileVerifyFormSchema } from '../lib/mobileVerifyFormSchema';
import { useVerification } from '../model/useVerification';

interface SubscriptionApplyMobileVerifyFormProps {
  handleVerify: (transactionId: string) => void;
}

export const SubscriptionApplyMobileVerifyForm = ({
  handleVerify,
}: SubscriptionApplyMobileVerifyFormProps) => {
  const {
    verifyCode,
    verifyContact,
    isEmailVerificationVisible,
    verificationTimerText,
    verificationTimer,
  } = useVerification(VerificationType.SMS);

  const form = useForm<MobileVerifyFormData>({
    resolver: zodResolver(mobileVerifyFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      isMobileVerified: false,
      mobileVerifiedCode: '',
      mobileNumber: '',
    },
  });

  const { watch, getFieldState, setValue } = form;

  const verifyMobileCode = async () => {
    const data = await verifyCode(watch('mobileNumber'), watch('mobileVerifiedCode'));

    if (data.statusCode === 201) {
      setValue('isMobileVerified', true);
      handleVerify(data.data.transactionId);
    }
  };

  return (
    <div className="space-y-6 break-keep text-center text-lg font-semibold sm:space-y-8">
      <h5 className="ml:px-8">
        투자내용 및 투자위험 확인에 동의하시면 [인증하기]를 통해 인증해 주세요.
      </h5>
      <Form {...form}>
        <form className="space-y-3">
          <div className="flex gap-2">
            <InputField
              disabled={watch('isMobileVerified')}
              form={form}
              name="mobileNumber"
              placeholder="휴대전화번호"
              type="tel"
            />
            <SecondaryButton
              onClick={() => verifyContact(watch('mobileNumber'))}
              type="button"
              disabled={
                !(watch('mobileNumber') && !getFieldState('mobileNumber').invalid) ||
                watch('isMobileVerified')
              }
              text={isEmailVerificationVisible ? '재요청' : '인증하기'}
              className="h-[44px] min-w-[85px] sm:h-12"
            />
          </div>
          {isEmailVerificationVisible && (
            <div className="flex gap-2">
              <div className="relative w-full">
                <InputField
                  form={form}
                  disabled={watch('isMobileVerified')}
                  name="mobileVerifiedCode"
                  placeholder="인증번호 입력"
                  type="text"
                />
                <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                  {verificationTimerText}
                </span>
              </div>
              <SecondaryButton
                type="button"
                text="확인"
                disabled={getFieldState('mobileVerifiedCode').invalid || !verificationTimer}
                className="h-[44px] min-w-[85px] sm:h-12"
                onClick={verifyMobileCode}
              />
            </div>
          )}

          {watch('isMobileVerified') && (
            <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
              <CheckCircleIcon className="h-4 w-4" color="green" />
              휴대폰 인증 완료
            </div>
          )}
        </form>
      </Form>
    </div>
  );
};
