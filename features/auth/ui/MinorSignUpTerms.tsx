import { Separator } from '@radix-ui/react-separator';
import { motion } from 'framer-motion';
import React from 'react';

import { MinorNoticeList } from '@/widgets/auth/ui/MinorNoticeList';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

export const MinorSignUpTerms = ({ toggleVisibility }: { toggleVisibility: () => void }) => {
  return (
    <motion.div
      className="flex h-auth-screen-mobile w-full max-w-[640px] flex-col justify-between rounded-[20px] border-gray-300 sm:h-auto sm:border"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="px-6 py-4 sm:p-20">
        <h4 className="text-xl font-semibold sm:font-bold">
          미성년자 회원의 서비스 이용을 위해 <br /> 법정대리인(보호자)의 본인인증 동의 절차가
          필요합니다. <br /> 아래 약관에 동의해 주세요.
        </h4>
        <div className="my-12 space-y-6">
          <CheckboxField
            label="약관 전체 동의"
            value="isAgreeAll"
            checked={true}
            onCheckedChange={() => {}}
          />
          <Separator className="h-[1px] bg-gray-150" />
          <CheckboxField
            label="만 19세 미만 아동의 개인정보를 수집 및 이용에 동의합니다."
            value="isAgreeAll"
            checked={true}
            onCheckedChange={() => {}}
            required
          />
          <CheckboxField
            label="법정대리인(보호자) 정보(이름, 연락처) 저장에 동의합니다."
            value="isAgreeAll"
            checked={true}
            onCheckedChange={() => {}}
            required
          />
        </div>
        <PrimaryButton text="다음" className="w-full" onClick={toggleVisibility} />
      </div>
      <div className="rounded-b-[20px] bg-gray-50 p-10">
        <MinorNoticeList />
      </div>
    </motion.div>
  );
};
