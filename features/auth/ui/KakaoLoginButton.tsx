'use client';

import React from 'react';

import { SignType } from '@/entities/auth/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Button } from '@/shared/ui/shadcn/button';

import { useKakaoLogin } from '../model/useKakaoLogin';

interface KakaoLoginButtonProps {
  type: SignType;
  text: string;
}

export const KakaoLoginButton = ({ type, text }: KakaoLoginButtonProps) => {
  const { kakaoLogin } = useKakaoLogin({ type });

  return (
    <Button
      onClick={kakaoLogin}
      className="relative h-12 w-full rounded-lg bg-kakao hover:bg-kakao hover:text-gray-900"
    >
      <FallbackImage
        src="/logo/kakao.png"
        alt="kakao"
        width={18}
        height={18}
        className="absolute left-[22px] top-1/2 -translate-y-1/2"
      />
      <p className="text-base font-semibold">{text}</p>
    </Button>
  );
};
