'use client';

import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useRouter } from 'next/navigation';
import { UseFormReturn } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useVerification } from '../model/useVerification';
import { DuplicateConfirmDialog } from './DuplicateConfirmDialog';

interface SignUpMobileFormProps {
  handleNextStep: () => void;
  form: UseFormReturn<any>;
}

export const SignUpMobileForm = ({ handleNextStep, form }: SignUpMobileFormProps) => {
  const { watch, setValue, getFieldState } = form;
  const router = useRouter();
  const {
    verifyCode,
    verifyContact,
    isEmailVerificationVisible,
    verificationTimerText,
    verificationTimer,
    isDuplicate,
  } = useVerification(VerificationType.SMS);

  const verifyMobileCode = async () => {
    const data = await verifyCode(watch('mobileNumber'), watch('mobileVerifiedCode'));

    if (data.statusCode === 201) {
      setValue('isMobileVerified', true);
    }
  };

  return (
    <AuthFormContainer>
      <div>
        <h5 className="text-2xl font-semibold leading-[36px]">
          휴대전화번호 인증을 진행해 주세요.{' '}
          <span className="text-base font-normal text-primary-500">(필수)</span>
        </h5>
        <Form {...form}>
          <form className="mt-10 space-y-3 sm:mt-16" onSubmit={(e) => e.preventDefault()}>
            <div className="flex gap-2">
              <InputField
                disabled={watch('isMobileVerified')}
                maxLength={11}
                form={form}
                name="mobileNumber"
                placeholder="휴대전화번호"
                type="tel"
              />
              <SecondaryButton
                onClick={() => verifyContact(watch('mobileNumber'))}
                type="button"
                disabled={
                  !(watch('mobileNumber') && !getFieldState('mobileNumber').invalid) ||
                  watch('isMobileVerified')
                }
                text={isEmailVerificationVisible ? '재요청' : '인증하기'}
                className="h-[44px] min-w-[85px] sm:h-12"
              />
            </div>
            {isEmailVerificationVisible && (
              <div className="flex gap-2">
                <div className="relative w-full">
                  <InputField
                    form={form}
                    disabled={watch('isMobileVerified')}
                    name="mobileVerifiedCode"
                    placeholder="인증번호 입력"
                    type="text"
                  />
                  <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                    {verificationTimerText}
                  </span>
                </div>
                <SecondaryButton
                  type="button"
                  text="확인"
                  disabled={getFieldState('mobileVerifiedCode').invalid || !verificationTimer}
                  className="h-[44px] min-w-[85px] sm:h-12"
                  onClick={verifyMobileCode}
                />
              </div>
            )}

            {watch('isMobileVerified') && (
              <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                <CheckCircleIcon className="h-4 w-4" color="green" />
                휴대폰 인증 완료
              </div>
            )}
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          disabled={!watch('isMobileVerified')}
          onClick={handleNextStep}
          className="h-12 w-full text-base sm:mt-16"
          text="다음"
        />
      </div>
      <DuplicateConfirmDialog
        isOpen={isDuplicate.isOpen}
        handleOpen={() => router.replace('/sign-in')}
        signupPath={isDuplicate.signupPath}
      />
    </AuthFormContainer>
  );
};
