'use client';

import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { UseFormReturn } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { SocialSignUpFormData } from '../lib/socialSignUpFormSchema';
import { useVerification } from '../model/useVerification';

interface SocialSignUpEmailFormProps {
  handleNextStep: () => void;
  form: UseFormReturn<SocialSignUpFormData>;
}

export const SocialSignUpEmailForm = ({ handleNextStep, form }: SocialSignUpEmailFormProps) => {
  const { register, control, getFieldState, watch } = form;

  const isDisabled = !(
    (watch('email') && !getFieldState('email').invalid)
    //   &&
    //   watch('emailVerifiedCode') &&
    // watch('isEmailVerified')
  );

  const { verifyContact } = useVerification(VerificationType.EMAIL);

  return (
    <AuthFormContainer>
      <div>
        <h5 className="text-xl font-semibold leading-[36px] sm:text-2xl">
          이메일 인증을 진행해주세요.
        </h5>
        <Form {...form}>
          <form className="my-10 sm:my-16">
            <div className="space-y-2">
              <div className="flex gap-2">
                <InputField form={form} name="email" placeholder="아이디" />
                <SecondaryButton
                  onClick={() => verifyContact(watch('email'))}
                  type="button"
                  disabled={!(watch('email') && !getFieldState('email').invalid)}
                  text="인증하기"
                  className="h-[44px] min-w-[80px] sm:h-12"
                />
              </div>
              <div className="flex gap-2">
                <InputField form={form} name="emailVerifiedCode" placeholder="인증번호 입력" />
                <SecondaryButton
                  text="확인"
                  className="h-[44px] min-w-[80px] sm:h-12"
                  disabled={!watch('emailVerifiedCode')}
                />
              </div>
              {watch('isEmailVerified') && (
                <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                  <CheckCircleIcon className="h-4 w-4" color="green" />
                  이메일 인증 완료
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          disabled={isDisabled}
          className="h-12 w-full text-base"
          text="다음"
          onClick={handleNextStep}
        />
      </div>
    </AuthFormContainer>
  );
};
