import React from 'react';

interface TermsUnderLineButtonProps {
  type: 'marketing' | 'privacy' | 'terms';
}

export const TermsUnderLineButton = ({ type }: TermsUnderLineButtonProps) => {
  const handleOpenPopup = () => {
    let url;
    if (type === 'marketing') {
      url = '/terms/marketing-usage';
    } else if (type === 'privacy') {
      url = '/terms/privacy-policy';
    } else if (type === 'terms') {
      url = '/terms/terms-of-use';
    }

    window.open(url, '_blank', 'width=700, height=600, top=0, left=0, scrollbars=yes');
  };

  return (
    <button
      onClick={handleOpenPopup}
      className="min-w-[30px] text-xs font-semibold text-gray-700 underline"
    >
      보기
    </button>
  );
};
