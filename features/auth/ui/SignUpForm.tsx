'use client';

import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useRouter } from 'next/navigation';
import { UseFormReturn } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { InputField } from '@/shared/ui/InputField';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';

import { SignUpFormData } from '../lib/signUpFormSchema';
import { useVerification } from '../model/useVerification';
import { DuplicateConfirmDialog } from './DuplicateConfirmDialog';

interface SignUpFormProps {
  onSubmit: (data: SignUpFormData) => void;
  form: UseFormReturn<SignUpFormData>;
  isLoading: boolean;
}

export const SignUpForm = ({ onSubmit, form, isLoading }: SignUpFormProps) => {
  const { register, getFieldState, watch, setValue } = form;
  const router = useRouter();

  const {
    verifyContact,
    verifyCode,
    verificationTimerText,
    verificationTimer,
    isDuplicate,
    isEmailVerificationVisible,
  } = useVerification(VerificationType.EMAIL);

  const isPasswordMatch =
    watch('password') && watch('rePassword') && watch('password') === watch('rePassword');

  const isDisabled = !(
    watch('email') &&
    !getFieldState('email').invalid &&
    isPasswordMatch &&
    watch('isEmailVerified')
  );

  const verifyEmailCode = async () => {
    const { data } = await verifyCode(watch('email'), watch('emailVerifiedCode'));
    if (data.verified) {
      setValue('isEmailVerified', data.verified);
    }
  };

  return (
    <AuthFormContainer>
      <div>
        <h5 className="text-xl font-semibold leading-[36px] sm:text-2xl">
          로그인 시 사용할 <br /> 이메일과 비밀번호를 입력해 주세요.
          <span className="text-base font-normal text-primary-500">(필수)</span>
        </h5>

        <Form {...form}>
          <form className="my-10 space-y-10 sm:my-16 sm:space-y-16">
            <div>
              <div className="flex gap-2">
                <InputField
                  form={form}
                  name="email"
                  disabled={watch('isEmailVerified')}
                  placeholder="이메일 입력"
                />
                <SecondaryButton
                  onClick={() => verifyContact(watch('email'))}
                  type="button"
                  disabled={
                    !(watch('email') && !getFieldState('email').invalid) || watch('isEmailVerified')
                  }
                  text={
                    isEmailVerificationVisible && !watch('isEmailVerified') ? '재요청' : '인증하기'
                  }
                  className="min-w-[84px] text-base"
                />
              </div>
              {isEmailVerificationVisible && (
                <FormField
                  name="emailVerifiedCode"
                  render={() => (
                    <FormItem>
                      <FormLabel />
                      <FormControl>
                        <div className="flex gap-2">
                          <div className="relative w-full">
                            <Input
                              {...register('emailVerifiedCode')}
                              placeholder="인증번호 입력"
                              type="text"
                              readOnly={watch('isEmailVerified')}
                              className={`${
                                !(
                                  watch('emailVerifiedCode') &&
                                  !getFieldState('emailVerifiedCode').invalid
                                ) && 'border-2 border-red-500'
                              }`}
                            />
                            {verificationTimer && (
                              <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                                {verificationTimerText}
                              </span>
                            )}
                          </div>
                          <SecondaryButton
                            type="button"
                            text="확인"
                            onClick={verifyEmailCode}
                            className="h-[44px] min-w-[84px] text-base sm:h-12"
                            disabled={
                              !(
                                verificationTimer &&
                                watch('emailVerifiedCode') &&
                                !getFieldState('emailVerifiedCode').invalid
                              )
                            }
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                      {watch('isEmailVerified') && (
                        <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                          <CheckCircleIcon className="h-4 w-4" color="green" />
                          이메일 인증 완료
                        </div>
                      )}
                    </FormItem>
                  )}
                />
              )}
            </div>
            <div className="space-y-2">
              <PasswordInputField form={form} name="password" placeholder="비밀번호 입력" />
              <PasswordInputField
                form={form}
                name="rePassword"
                placeholder="비밀번호 재입력"
                className={`${
                  watch('rePassword') && !isPasswordMatch && 'border-2 border-red-500'
                }`}
              />

              {isPasswordMatch && (
                <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                  <CheckCircleIcon className="h-4 w-4" color="green" />
                  비밀번호가 일치합니다.
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          disabled={isDisabled || isLoading}
          className="h-12 w-full text-base"
          text="다음"
          onClick={() => onSubmit(form.getValues())}
        />
      </div>

      <DuplicateConfirmDialog
        isOpen={isDuplicate.isOpen}
        handleOpen={() => router.replace('/sign-in')}
        signupPath={isDuplicate.signupPath}
      />
    </AuthFormContainer>
  );
};
