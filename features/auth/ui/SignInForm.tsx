'use client';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { InputField } from '@/shared/ui/InputField';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { useSignIn } from '../model/useSignIn';

interface SignInFormProps {
  isCorporate?: boolean;
}

export const SignInForm = ({ isCorporate = false }: SignInFormProps) => {
  const { form, onSubmit, isSaveId, toggleSaveId } = useSignIn({ isCorporate });

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <InputField form={form} name="account" placeholder="아이디 입력" type="email" />
          <PasswordInputField form={form} name="password" placeholder="비밀번호 입력" />

          <PrimaryButton
            type="submit"
            className="h-12 w-full text-base"
            text="이메일로 로그인하기"
            disabled={!form.formState.isValid}
          />
        </form>
      </Form>
      <div className="flex items-center gap-6">
        <CheckboxField
          label="아이디 저장"
          value="isKeepLogin"
          checked={isSaveId}
          onCheckedChange={toggleSaveId}
          className="mt-6 sm:mt-8"
        />
      </div>
    </>
  );
};
