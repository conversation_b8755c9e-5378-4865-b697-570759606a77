import { UseFormReturn } from 'react-hook-form';

import { PasswordConfirmMessage } from '@/shared/ui/PasswordConfirmMessage';
import { PasswordInputField } from '@/shared/ui/PasswordInputField';
import { Form } from '@/shared/ui/shadcn/form';

import { RegistrationPasswordFormData } from '../lib/registrationPasswordSchema';

interface CorporatePasswordProps {
  form: UseFormReturn<RegistrationPasswordFormData>;
}
export const CorporatePassword = ({ form }: CorporatePasswordProps) => {
  const { watch } = form;

  const isPasswordMatch =
    watch('password') && watch('rePassword') && watch('password') === watch('rePassword');

  return (
    <div className="space-y-10">
      <h2 className="text-20 sm:text-24">
        로그인 시 사용할 <br />
        비밀번호를 입력해 주세요. <strong className="font-medium text-primary-500">(필수)</strong>
      </h2>
      <Form {...form}>
        <form className="space-y-3">
          <PasswordInputField form={form} name="password" placeholder="비밀번호 입력" />
          <div className="relative space-y-2">
            <PasswordInputField form={form} name="rePassword" placeholder="비밀번호 재입력" />
            {isPasswordMatch && (
              <PasswordConfirmMessage className="absolute bottom-[-24px] left-0" />
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};
