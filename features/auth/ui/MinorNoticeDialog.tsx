import { useRouter } from 'next/navigation';

import { MinorNoticeList } from '@/widgets/auth/ui/MinorNoticeList';

import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/shadcn/dialog';

interface MinorNoticeDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  title?: string;
  description?: string;
  caption?: string;
  handleAction?: () => void;
  text?: string;
  isCancelButton?: boolean;
}

export const MinorNoticeDialog = ({ isOpen, handleOpen }: MinorNoticeDialogProps) => {
  const router = useRouter();

  return (
    <Dialog open={isOpen} onOpenChange={handleOpen}>
      <DialogContent className="flex max-w-[327px] flex-col gap-10 rounded-[16px] bg-white px-6 sm:max-w-[386px]">
        <DialogHeader className="mt-6 space-y-4" aria-describedby="minor-notice-description">
          <DialogTitle className="whitespace-pre-line text-center text-base font-semibold">
            미성년자 회원 법정대리인 동의 필요
          </DialogTitle>

          <MinorNoticeList />
        </DialogHeader>

        <div className="mb-4 flex justify-center gap-2">
          <PrimaryButton
            type="button"
            className="!h-[44px] w-full text-sm font-semibold"
            onClick={handleOpen}
            text="확인"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
