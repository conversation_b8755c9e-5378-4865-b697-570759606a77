import { UseFormReturn } from 'react-hook-form';

import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { FindPasswordFormData } from '../lib/findPasswordFormSchema';

interface FindPasswordFormProps {
  form: UseFormReturn<FindPasswordFormData>;
  onSubmit: (formData: FindPasswordFormData) => void;
}

export const FindPasswordForm = ({ form, onSubmit }: FindPasswordFormProps) => {
  return (
    <div className="flex flex-col justify-between sm:h-auto">
      <div>
        <h4 className="mb-12 text-xl font-bold sm:text-2xl">
          비밀번호를 잊으셨나요? <br />
          아래의 정보를 입력해주세요.
        </h4>
        <Form {...form}>
          <form className="space-y-8 sm:space-y-10">
            <InputField label="이름" form={form} name="name" placeholder="이름 입력" type="text" />
            <InputField
              label="이메일"
              form={form}
              name="email"
              placeholder="이메일 입력"
              type="email"
            />
          </form>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          onClick={() => onSubmit(form.getValues())}
          className="h-12 w-full text-base sm:mt-16"
          text="이메일로 비밀번호 재설정하기"
          disabled={!form.formState.isValid}
        />
      </div>
    </div>
  );
};
