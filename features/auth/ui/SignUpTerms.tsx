'use client';

import { UseFormReturn } from 'react-hook-form';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { CheckboxField } from '@/shared/ui/CheckboxField';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Separator } from '@/shared/ui/shadcn/separator';

import { useSignUpTerms } from '../model/useSignUpTerms';
import { TermsUnderLineButton } from './TermsUnderLineButton';

interface SignUpTermsProps {
  handleNextStep: () => void;
  form: UseFormReturn<any>;
  disabled: boolean;
}

export const SignUpTerms = ({ handleNextStep, form, disabled }: SignUpTermsProps) => {
  const { watch, setValue } = form;
  const { isAllAgree, isAlarmAgree, isDisabled, handleAllAgree } = useSignUpTerms(form);

  return (
    <AuthFormContainer>
      <div>
        <h4 className="text-xl font-semibold sm:font-bold">
          서비스 이용을 위해 <br /> 아래의 약관에 동의해 주세요.
        </h4>
        <div className="my-10 space-y-6 sm:my-16">
          <CheckboxField
            label="약관 전체 동의"
            value="isAgreeAll"
            checked={isAllAgree}
            onCheckedChange={handleAllAgree}
          />
          <Separator className="bg-gray-150" />
          <div className="flex items-center justify-between">
            <CheckboxField
              label="뉴밋 이용약관 동의"
              value="isAgreeTerms"
              checked={watch('isTermsAgree')}
              onCheckedChange={() => setValue('isTermsAgree', !watch('isTermsAgree'))}
              required
            />
            <TermsUnderLineButton type="terms" />
          </div>
          <div className="flex items-center justify-between">
            <CheckboxField
              label="개인정보 수집 및 이용 동의"
              value="isAgreePrivacy"
              checked={watch('isPrivacyAgree')}
              onCheckedChange={() => setValue('isPrivacyAgree', !watch('isPrivacyAgree'))}
              required
            />
            <TermsUnderLineButton type="privacy" />
          </div>

          <CheckboxField
            label="이벤트 혜택 알림 동의 (선택)"
            value="isAgreeEmail"
            checked={isAlarmAgree}
            onCheckedChange={() => {
              if (isAlarmAgree) {
                setValue('isEmailAgree', false);
                setValue('isSmsAgree', false);
              } else {
                setValue('isEmailAgree', true);
                setValue('isSmsAgree', true);
              }
            }}
          />
          <div className="flex items-center gap-2">
            <FallbackImage
              src="/icons/ic_include.png"
              alt="ic_include.png"
              width={16}
              height={16}
            />
            <CheckboxField
              label="이메일"
              value="isAgreeEmail"
              checked={watch('isEmailAgree')}
              onCheckedChange={() => setValue('isEmailAgree', !watch('isEmailAgree'))}
            />
          </div>
          <div className="flex items-center gap-2">
            <FallbackImage
              src="/icons/ic_include.png"
              alt="ic_include.png"
              width={16}
              height={16}
            />
            <CheckboxField
              label="SMS"
              value="isAgreeSms"
              checked={watch('isSmsAgree')}
              onCheckedChange={() => setValue('isSmsAgree', !watch('isSmsAgree'))}
            />
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:p-0">
        <PrimaryButton
          className="h-12 w-full text-base"
          text="다음"
          onClick={handleNextStep}
          disabled={isDisabled || disabled}
        />
      </div>
    </AuthFormContainer>
  );
};
