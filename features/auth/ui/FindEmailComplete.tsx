import { useRouter } from 'next/navigation';
import React from 'react';

import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

export const FindEmailComplete = ({
  email,
  handleBack,
}: {
  email: string;
  handleBack: () => void;
}) => {
  const router = useRouter();

  return (
    <div className="flex flex-col justify-between sm:h-auto">
      <div>
        <h4 className="mb-12 text-xl font-bold sm:text-2xl">
          입력한 정보와 일치하는 <br /> 아이디 찾기가 완료되었습니다.
        </h4>
        <div className="h-30 rounded-lg border border-gray-300 bg-gray-50 p-7 text-sm sm:text-base">
          <div className="flex justify-between">
            <p>이메일</p>
            <p className="font-semibold">{email}</p>
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          className="mt-16 h-12 w-full text-base"
          onClick={() => router.replace('/sign-in')}
          text="로그인"
        />
        <SecondaryButton onClick={handleBack} text="이전으로" className="mt-3 w-full" />
      </div>
    </div>
  );
};
