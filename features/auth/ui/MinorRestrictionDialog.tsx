import { signOut } from 'next-auth/react';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/shadcn/dialog';

interface MinorRestrictionDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
}

export const MinorRestrictionDialog = ({ isOpen, handleOpen }: MinorRestrictionDialogProps) => {
  const { routerPush, isApp } = useWebViewRouter();
  const { screenSize } = useScreenStore();

  const handleConfirm = () => {
    handleOpen();
    if (screenSize === ScreenSize.MOBILE) {
      routerPush('/mobile/user/my/withdrawal');
    } else {
      routerPush('/user/my/withdrawal');
    }
  };

  const handleCancel = async () => {
    handleOpen();
    if (isApp) {
      await signOut();
      WebViewMessage('logout', {});
    } else {
      await signOut({ redirect: true, callbackUrl: '/' });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="flex max-w-[327px] flex-col gap-10 rounded-[16px] bg-white px-6 sm:max-w-[386px]">
        <DialogHeader className="mt-6 space-y-4">
          <DialogTitle className="whitespace-pre-line text-center text-base font-semibold">
            서비스 이용 불가 안내
          </DialogTitle>
          <DialogDescription className="whitespace-pre-line text-center text-sm text-gray-900">
            <strong className="font-medium text-red-500">서비스 이용약관</strong>에 따라 미성년자는{' '}
            <br />더 이상 뉴밋 서비스를 이용하실 수 없습니다.
          </DialogDescription>
          <DialogDescription className="whitespace-pre-line text-center text-sm text-gray-700">
            아래 버튼을 통해 회원탈퇴를 진행하실 수 있으며, <br />
            탈퇴를 진행하지 않으실 경우 7일 후 자동 탈퇴 처리됩니다.
          </DialogDescription>
        </DialogHeader>

        <div className="mb-[10px] flex justify-center gap-2">
          <SecondaryButton className="w-full" onClick={handleCancel} text="확인" />
          <PrimaryButton
            type="submit"
            className={`h-[44px] w-full font-semibold sm:h-12`}
            onClick={handleConfirm}
            text="회원탈퇴"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
