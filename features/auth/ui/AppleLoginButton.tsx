import React from 'react';

import { SignType } from '@/entities/auth/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Button } from '@/shared/ui/shadcn/button';

import { useAppleLogin } from '../model/useAppleLogin';

interface AppleLoginButtonProps {
  type: SignType;
  text: string;
}

export const AppleLoginButton = ({ type, text }: AppleLoginButtonProps) => {
  const { loginWithApple } = useAppleLogin({ type });

  return (
    <Button
      onClick={loginWithApple}
      className="relative h-12 w-full rounded-lg bg-apple hover:text-gray-900"
    >
      <FallbackImage
        src="/logo/apple.png"
        alt="apple"
        width={21}
        height={21}
        className="absolute left-[22px] top-1/2 -translate-y-1/2"
      />
      <p className="text-base font-bold text-white">{text}</p>
    </Button>
  );
};
