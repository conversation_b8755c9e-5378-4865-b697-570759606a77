import { SignupPath } from '@/entities/auth/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/shadcn/dialog';

interface DuplicateConfirmDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  handleAction?: () => void;
  signupPath?: { type: SignupPath; email?: string }[] | { type: SignupPath; email?: string };
}

export const DuplicateConfirmDialog = ({
  isOpen,
  handleOpen,
  handleAction,
  signupPath,
}: DuplicateConfirmDialogProps) => {
  const isArray = Array.isArray(signupPath);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpen}>
      <DialogContent className="flex max-w-[327px] flex-col rounded-2xl bg-white px-6 sm:max-w-[386px]">
        <DialogHeader className="mt-6 space-y-4">
          <DialogTitle className="text-center font-semibold">
            이미 가입된 계정이 있습니다.
          </DialogTitle>
          <DialogDescription className="text-center text-sm text-gray-900">
            이전에 가입한 계정을 확인해 주세요.
          </DialogDescription>
        </DialogHeader>
        <div className="mb-10 mt-8 flex flex-col items-center justify-center gap-2 rounded-[10px] border border-gray-300 bg-gray-50 py-4">
          {isArray ? (
            signupPath?.map((path) => (
              <div key={path.type} className="flex items-center gap-2">
                {path.type === SignupPath.CORPORATE ? (
                  <p className="rounded bg-gray-900 px-2 py-[3px] text-xs font-semibold leading-[150%] text-white">
                    법인회원
                  </p>
                ) : (
                  <FallbackImage
                    src={`/icons/icon_${path.type}.png`}
                    alt={path.type}
                    width={20}
                    height={20}
                  />
                )}
                <p>{path.email}</p>
              </div>
            ))
          ) : (
            <div key={signupPath?.type} className="flex items-center gap-2">
              {signupPath?.type === SignupPath.CORPORATE ? (
                <p className="rounded bg-gray-900 px-2 py-[3px] text-xs font-semibold leading-[150%] text-white">
                  법인회원
                </p>
              ) : (
                <FallbackImage
                  src={`/icons/icon_${signupPath?.type}.png`}
                  alt="icon"
                  width={20}
                  height={20}
                />
              )}
              <p>{signupPath?.email}</p>
            </div>
          )}
        </div>

        <PrimaryButton
          type="button"
          className="mb-[10px] h-[44px] w-full text-sm font-semibold"
          onClick={handleOpen}
          text="확인"
        />
      </DialogContent>
    </Dialog>
  );
};
