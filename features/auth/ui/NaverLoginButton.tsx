import React from 'react';

import { SignType } from '@/entities/auth/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Button } from '@/shared/ui/shadcn/button';

import { useNaverLogin } from '../model/useNaverLogin';

interface NaverLoginButtonProps {
  type: SignType;
  text: string;
}

export const NaverLoginButton = ({ type, text }: NaverLoginButtonProps) => {
  const { handleNaverLogin } = useNaverLogin({ type });

  return (
    <Button
      onClick={handleNaverLogin}
      className="relative h-12 w-full rounded-lg bg-naver hover:bg-naver hover:text-gray-900"
    >
      <FallbackImage
        src="/logo/naver.png"
        alt="naver"
        width={18}
        height={18}
        className="absolute left-[22px] top-1/2 -translate-y-1/2"
      />
      <p className="text-base font-bold text-white">{text}</p>
    </Button>
  );
};
