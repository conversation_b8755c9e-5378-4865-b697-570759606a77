'use client';

import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form } from '@/shared/ui/shadcn/form';

import { SignUpFormData } from '../lib/signUpFormSchema';
import { SocialSignUpFormData } from '../lib/socialSignUpFormSchema';

interface SignUpNameFormProps {
  form: UseFormReturn<any>;
  onSignUp?: (data: SignUpFormData) => void;
  onSocialSignUp?: (data: SocialSignUpFormData) => void;
  isLoading: boolean;
}

export const SignUpNameForm = ({ form, onSignUp, isLoading }: SignUpNameFormProps) => {
  const { getFieldState, watch, handleSubmit } = form;

  return (
    <AuthFormContainer>
      <div>
        <h5 className="text-xl font-semibold leading-[36px] sm:text-2xl">
          이름을 입력해 주세요.{' '}
          <span className="text-base font-normal text-primary-500">(필수)</span>
        </h5>
        <Form {...form}>
          <div className="my-10 sm:my-16">
            <InputField
              form={form}
              type="text"
              name="name"
              placeholder="이름 입력"
              maxLength={16}
            />
          </div>
        </Form>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:px-0">
        <PrimaryButton
          type="button"
          disabled={isLoading || !(watch('name') && !getFieldState('name').invalid)}
          className="h-12 w-full text-base"
          text="다음"
          onClick={() => {
            if (onSignUp) {
              handleSubmit(onSignUp)();
            }
          }}
        />
      </div>
    </AuthFormContainer>
  );
};
