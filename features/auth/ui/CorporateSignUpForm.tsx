import { CheckCircleIcon, PaperClipIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { VerificationType } from '@/entities/verifications/types';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { FileInfoTooltop } from '@/shared/ui/FileInfoTooltop';
import { FileUploader } from '@/shared/ui/FileUploader';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';

import { BusinessSignUpFormData } from '../lib/businessSignUpFormSchema';
import { useVerification } from '../model/useVerification';
import { DuplicateConfirmDialog } from './DuplicateConfirmDialog';
import { TermsUnderLineButton } from './TermsUnderLineButton';

interface CorporateSignUpFormProps {
  form: UseFormReturn<BusinessSignUpFormData>;
  onSubmit: (data: BusinessSignUpFormData) => void;
  handleDeleteFile: (file: File) => void;
  handleUploadedFiles: (files: File[]) => void;
  ref: React.RefObject<HTMLInputElement | null>;
  handleFileUpload: () => void;
  isLoading: boolean;
}

export const CorporateSignUpForm = ({
  form,
  handleDeleteFile,
  handleUploadedFiles,
  ref,
  handleFileUpload,
  isLoading,
  onSubmit,
}: CorporateSignUpFormProps) => {
  const { watch, handleSubmit, register, getFieldState, setValue, setError, formState } = form;
  const router = useRouter();

  const {
    verifyContact,
    verifyCode,
    verificationTimerText,
    verificationTimer,
    isDuplicate,
    isEmailVerificationVisible,
    handleCloseEmailDuplicateConfirmDialog,
  } = useVerification(VerificationType.EMAIL);

  const {
    verifyContact: verifyMobileContact,
    verifyCode: verifyMobil,
    verificationTimerText: verificationMobileTimerText,
    verificationTimer: verificationMobileTimer,
    isDuplicate: isMobileDuplicate,
    isEmailVerificationVisible: isMobileEmailVerificationVisible,
  } = useVerification(VerificationType.SMS);

  const verifyMobileCode = async () => {
    try {
      const { data } = await verifyMobil(watch('managerMobileNumber'), watch('mobileVerifiedCode'));
      if (data.verified) {
        setValue('isMobileVerified', true, {
          shouldValidate: true,
        });
      }
    } catch (error) {
      setError('mobileVerifiedCode', {
        type: 'manual',
        message: '올바른 인증번호를 입력하세요.',
      });
    }
  };

  const verifyEmailCode = async () => {
    const { data } = await verifyCode(watch('email'), watch('emailVerifiedCode'));

    if (data.verified) {
      setValue('isEmailVerified', data.verified, {
        shouldValidate: true,
      });
    }
  };

  return (
    <motion.div
      className="flex h-auth-screen-mobile w-full max-w-[996px] flex-col justify-between rounded-[20px] border-gray-300 sm:h-auto sm:border"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="px-6 py-8 sm:px-8 sm:py-20 ml:p-20">
        <h3 className="text-24 hidden sm:block">법인 회원가입 신청</h3>
        <Form {...form}>
          <form className="mt-12 space-y-10">
            <div className="flex flex-col gap-5 sm:flex-row">
              <InputField
                label="법인명"
                requiredText
                form={form}
                name="companyName"
                placeholder="법인명 입력"
              />
              <InputField
                label="대표자명"
                requiredText
                form={form}
                name="representativeName"
                placeholder="대표자명 입력"
              />
            </div>
            <div className="flex flex-col gap-10 sm:flex-row sm:gap-5">
              <InputField
                label="법인등록번호"
                requiredText
                maxLength={13}
                form={form}
                name="brn"
                placeholder="(-)을 제외한 법인등록번호 13자리 입력"
              />
              <InputField
                label="사업자등록번호"
                requiredText
                maxLength={10}
                form={form}
                name="crn"
                placeholder="(-)을 제외한 사업자등록번호 10자리 입력"
              />
            </div>
            <div className="flex flex-col gap-10 sm:flex-row sm:gap-5">
              <InputField
                label="담당자명"
                requiredText
                form={form}
                name="managerName"
                placeholder="담당자명 입력"
              />
              <div className="flex w-full flex-col gap-2">
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="managerMobileNumber"
                    render={() => (
                      <FormItem className="w-full space-y-3">
                        <FormLabel className="font-semibold sm:text-base">
                          담당자 연락처
                          <strong className="font-semibold text-primary-500"> (필수)</strong>
                        </FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input
                              {...register('managerMobileNumber')}
                              placeholder="(-)를 제외한 휴대폰 번호 입력"
                              type="tel"
                              disabled={watch('isMobileVerified')}
                              className={`${formState.errors['managerMobileNumber'] && 'border-2 border-red-500'}`}
                            />
                          </FormControl>
                          <SecondaryButton
                            onClick={() => verifyMobileContact(watch('managerMobileNumber'))}
                            type="button"
                            disabled={
                              !(
                                watch('managerMobileNumber') &&
                                !getFieldState('managerMobileNumber').invalid
                              ) || watch('isMobileVerified')
                            }
                            text={verificationMobileTimer ? '재요청' : '인증하기'}
                            className="h-[44px] w-[90px] sm:h-12"
                          />
                        </div>
                        <FormMessage className="text-xs font-semibold sm:text-sm" />
                      </FormItem>
                    )}
                  />
                </div>
                {isMobileEmailVerificationVisible && (
                  <FormField
                    name="mobileVerifiedCode"
                    render={() => (
                      <FormItem>
                        <FormLabel />
                        <FormControl>
                          <div className="flex gap-2">
                            <div className="relative w-full">
                              <Input
                                {...register('mobileVerifiedCode')}
                                placeholder="인증번호 입력"
                                type="text"
                                readOnly={watch('isMobileVerified')}
                                className={`${
                                  !(
                                    watch('mobileVerifiedCode') &&
                                    !getFieldState('mobileVerifiedCode').invalid
                                  ) && 'border-2 border-red-500'
                                }`}
                              />
                              {verificationMobileTimer && (
                                <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                                  {verificationMobileTimerText}
                                </span>
                              )}
                            </div>
                            <SecondaryButton
                              type="button"
                              text="확인"
                              onClick={verifyMobileCode}
                              className="h-[44px] min-w-[84px] sm:h-12"
                              disabled={
                                !(
                                  verificationMobileTimer &&
                                  watch('mobileVerifiedCode') &&
                                  !getFieldState('mobileVerifiedCode').invalid
                                )
                              }
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                        {watch('isMobileVerified') && (
                          <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                            <CheckCircleIcon className="h-4 w-4" color="green" />
                            휴대전화번호 인증 완료
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </div>

            <div>
              <div className="flex gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={() => (
                    <FormItem className="w-full space-y-3">
                      <FormLabel className="font-semibold sm:text-base">
                        이메일
                        <strong className="font-semibold text-primary-500"> (필수)</strong>
                      </FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input
                            {...register('email')}
                            placeholder="이메일 입력"
                            type="email"
                            disabled={watch('isEmailVerified')}
                            className={`${formState.errors['email'] && 'border-2 border-red-500'}`}
                          />
                        </FormControl>
                        <SecondaryButton
                          onClick={() => verifyContact(watch('email'))}
                          type="button"
                          disabled={
                            !(watch('email') && !getFieldState('email').invalid) ||
                            watch('isEmailVerified')
                          }
                          text={
                            isEmailVerificationVisible && !watch('isEmailVerified')
                              ? '재요청'
                              : '인증하기'
                          }
                          className="min-w-[84px]"
                        />
                      </div>
                      <FormMessage className="text-xs font-semibold sm:text-sm" />
                    </FormItem>
                  )}
                />
              </div>
              {isEmailVerificationVisible && (
                <FormField
                  name="emailVerifiedCode"
                  render={() => (
                    <FormItem>
                      <FormLabel />
                      <FormControl>
                        <div className="flex gap-2">
                          <div className="relative w-full">
                            <Input
                              {...register('emailVerifiedCode')}
                              placeholder="인증번호 입력"
                              type="text"
                              readOnly={watch('isEmailVerified')}
                              className={`${
                                !(
                                  watch('emailVerifiedCode') &&
                                  !getFieldState('emailVerifiedCode').invalid
                                ) && 'border-2 border-red-500'
                              }`}
                            />
                            {verificationTimer && (
                              <span className="absolute right-3 top-6 -translate-y-1/2 text-xs text-primary-500">
                                {verificationTimerText}
                              </span>
                            )}
                          </div>
                          <SecondaryButton
                            type="button"
                            text="확인"
                            onClick={verifyEmailCode}
                            className="h-[44px] min-w-[84px] sm:h-12"
                            disabled={
                              !(
                                verificationTimer &&
                                watch('emailVerifiedCode') &&
                                !getFieldState('emailVerifiedCode').invalid
                              )
                            }
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                      {watch('isEmailVerified') && (
                        <div className="flex items-center gap-1 text-xs font-semibold text-green-500">
                          <CheckCircleIcon className="h-4 w-4" color="green" />
                          이메일 인증 완료
                        </div>
                      )}
                    </FormItem>
                  )}
                />
              )}
            </div>
            <FormField
              control={form.control}
              name="brc"
              render={() => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1 text-sm font-semibold sm:text-base">
                    제출서류등록 <span className="text-primary-500">(필수)</span>
                    <FileInfoTooltop />
                  </FormLabel>
                  <FormControl>
                    <div className="flex flex-col gap-2">
                      {watch('brc')?.map((file: File) => (
                        <div
                          className="flex justify-between rounded-lg border border-gray-300 px-4 py-3"
                          key={file.name}
                        >
                          <div className="flex items-center gap-2">
                            <PaperClipIcon color="blue" className="h-5 w-5" />
                            {file.name}
                          </div>
                          <button type="button" onClick={() => handleDeleteFile(file)}>
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        onClick={handleFileUpload}
                        className="h-[44px] w-full rounded-lg border border-gray-300 text-gray-500 shadow-none sm:h-12"
                      >
                        <PlusIcon className="h-5 w-5" />
                        추가할 파일 선택
                      </Button>
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
        <div className="mb-2 mt-10 space-y-4 sm:my-12">
          <CheckboxField
            label="약관 전체 동의"
            value="isAgreeAll"
            checked={watch('isTermsAgree') && watch('isPrivacyAgree')}
            onCheckedChange={(checked) => {
              form.setValue('isTermsAgree', checked, {
                shouldValidate: true,
              });
              form.setValue('isPrivacyAgree', checked, {
                shouldValidate: true,
              });
            }}
          />
          <div className="flex items-center justify-between">
            <CheckboxField
              label="뉴밋 이용약관 동의"
              value="isAgreeTerms"
              checked={watch('isTermsAgree')}
              onCheckedChange={() => {
                form.setValue('isTermsAgree', !watch('isTermsAgree'), {
                  shouldValidate: true,
                });
              }}
              required
            />
            <TermsUnderLineButton type="terms" />
          </div>
          <div className="flex items-center justify-between">
            <CheckboxField
              label="개인정보 수집 및 이용 동의"
              value="isAgreeAll"
              checked={watch('isPrivacyAgree')}
              required
              onCheckedChange={() => {
                form.setValue('isPrivacyAgree', !watch('isPrivacyAgree'), {
                  shouldValidate: true,
                });
              }}
            />
            <TermsUnderLineButton type="privacy" />
          </div>
        </div>
        <PrimaryButton
          text="가입 신청하기"
          disabled={!formState.isValid || isLoading}
          onClick={handleSubmit(onSubmit)}
          className="hidden w-full sm:block"
        />
      </div>
      <div className="space-y-4 rounded-b-[20px] bg-gray-50 p-10 pb-28 sm:pb-10">
        <div className="flex items-center gap-3">
          <h4 className="text-sm font-semibold">제출 서류 안내</h4>
          <button className="text-xs font-semibold text-gray-500 underline">
            *법인 회원가입 절차가 궁금하신가요?
          </button>
        </div>
        <ol className="list-inside list-disc text-sm">
          <li>사업자등록증 사본</li>
          <li>법인등기부 등본 사본 (발급일로부터 3개월 이내)</li>
          <li>법인인감증명서 사본 (발급일로부터 3개월 이내)</li>
          <li>법인명의의 통장 사본</li>
          <li>대표자 신분증 사본</li>
        </ol>
      </div>

      <div className="fixed bottom-0 z-20 w-full bg-white px-4 pb-8 pt-2 sm:hidden">
        <PrimaryButton
          text="가입하기"
          disabled={!formState.isValid || isLoading}
          onClick={handleSubmit(onSubmit)}
          className="w-full"
        />
      </div>
      <FileUploader ref={ref} uploadedFiles={handleUploadedFiles} multiple maxSizeMB={5} />
      <DuplicateConfirmDialog
        isOpen={isDuplicate.isOpen}
        handleOpen={handleCloseEmailDuplicateConfirmDialog}
        handleAction={handleCloseEmailDuplicateConfirmDialog}
        signupPath={isDuplicate.signupPath}
      />
    </motion.div>
  );
};
