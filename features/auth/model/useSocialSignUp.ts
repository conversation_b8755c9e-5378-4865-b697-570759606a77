'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Cookies from 'js-cookie';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { checkUserKyc } from '@/entities/auth/api/checkUserKyc';
import { registerKyc } from '@/entities/auth/api/registerKyc';
import { SocialSignUpSteps } from '@/entities/auth/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { SocialSignUpFormData, socialSignUpFormSchema } from '../lib/socialSignUpFormSchema';

export const useSocialSignUp = () => {
  const searchParams = useSearchParams();
  const type = searchParams.get('type') as 'naver' | 'kakao';
  const { successToast, errorToast } = useToast();
  const { routerPush } = useWebViewRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { step, handleStep, isStep } = useStep<SocialSignUpSteps>(SocialSignUpSteps.TERMS);
  const callbackUrl = Cookies.get('callback-url') || '/';
  const router = useRouter();

  const form = useForm<SocialSignUpFormData>({
    resolver: zodResolver(socialSignUpFormSchema),
    mode: 'onChange',
    defaultValues: {
      socialId: '',
      email: '',
      mobileNumber: '',
      name: '',
      gender: '',
      birthDate: '',
      isTermsAgree: false,
      isPrivacyAgree: false,
      isMarketingAgree: false,
      isEmailAgree: false,
      isSmsAgree: false,
      isEmailVerified: false,
      isMobileVerified: false,
      mobileVerifiedCode: '',
      emailVerifiedCode: '',
    },
  });

  const onSubmit = async (data: SocialSignUpFormData) => {
    setIsLoading(true);
    try {
      const response = await signIn('sns-signup', {
        socialId: data.socialId,
        email: data.email,
        name: data.name,
        mobileNumber: data.mobileNumber,
        gender: data.gender || '',
        birthDate: data.birthDate || '',
        isTermsAgree: data.isTermsAgree,
        isPrivacyAgree: data.isPrivacyAgree,
        isMarketingAgree: data.isMarketingAgree,
        isEmailAgree: data.isEmailAgree,
        isSmsAgree: data.isSmsAgree,
        type,
        redirect: false,
      });

      if (response?.error) {
        throw new Error(response?.error || '회원가입에 실패했습니다.');
      }

      const verifiedCustomer = JSON.parse(sessionStorage.getItem('verifiedCustomer') || '{}');

      if (verifiedCustomer) {
        const { data: kycData } = await checkUserKyc();

        if (!kycData) {
          await registerKyc({
            ci: verifiedCustomer.ci,
            result: verifiedCustomer.result,
            name: verifiedCustomer.name,
            birthDate: verifiedCustomer.birthDate,
          });
        }
        sessionStorage.removeItem('verifiedCustomer');
      }

      successToast({
        title: '회원가입 성공',
      });

      Cookies.remove('callback-url');

      router.replace(callbackUrl);
    } catch (error) {
      console.error(error);
      errorToast({
        title: '회원가입 실패',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onClick = () => {
    if (!form.watch('email')) {
      handleStep(SocialSignUpSteps.EMAIL);
      return;
    }

    if (!form.watch('mobileNumber')) {
      handleStep(SocialSignUpSteps.MOBILE);
      return;
    }

    if (!form.watch('name')) {
      handleStep(SocialSignUpSteps.NAME);
      return;
    }

    onSubmit(form.getValues());
  };

  return { form, onSubmit, step, isLoading, handleStep, isStep, onClick };
};
