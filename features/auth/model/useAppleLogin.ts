import { SignType } from '@/entities/auth/types';

import env from '@/shared/lib/env.schema';
import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

const APPLE_CLIENT_ID = env.NEXT_PUBLIC_APPLE_CLIENT_ID!;
const REDIRECT_URI = `${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback`;

export const useAppleLogin = ({ type }: { type: SignType }) => {
  const { isApp } = useWebViewRouter();
  const isIOS = typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);

  const loginWithApple = () => {
    if (isApp && isIOS) {
      WebViewMessage('openAppleLogin', {
        state: `apple-${type}`,
      });
    } else {
      const url =
        `https://appleid.apple.com/auth/authorize?` +
        `response_type=code&` +
        `client_id=${APPLE_CLIENT_ID}&` +
        `redirect_uri=${encodeURIComponent(REDIRECT_URI)}&` +
        `state=apple-${type}&`;

      window.location.href = url;
    }
  };

  return { loginWithApple };
};
