'use client';

import { UseFormReturn } from 'react-hook-form';

export const useSignUpTerms = (form: UseFormReturn<any>) => {
  const { watch, setValue } = form;

  const isAllAgree =
    watch('isTermsAgree') &&
    watch('isPrivacyAgree') &&
    watch('isEmailAgree') &&
    watch('isSmsAgree');

  const isAlarmAgree = watch('isEmailAgree') || watch('isSmsAgree');

  const handleAllAgree = () => {
    if (isAllAgree) {
      setValue('isTermsAgree', false);
      setValue('isPrivacyAgree', false);
      setValue('isEmailAgree', false);
      setValue('isSmsAgree', false);
    } else {
      setValue('isTermsAgree', true);
      setValue('isPrivacyAgree', true);
      setValue('isEmailAgree', true);
      setValue('isSmsAgree', true);
    }
  };

  const isDisabled = !(watch('isTermsAgree') && watch('isPrivacyAgree'));

  return { isAllAgree, isAlarmAgree, isDisabled, handleAllAgree };
};
