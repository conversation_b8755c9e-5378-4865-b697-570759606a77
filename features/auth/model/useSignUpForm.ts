import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { formSchema, SignUpFormData } from '../../../features/auth/lib/signUpFormSchema';

export const useSignUpForm = () => {
  const form = useForm<SignUpFormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      email: '',
      password: '',
      rePassword: '',
      isTermsAgree: false,
      isPrivacyAgree: false,
      isEmailAgree: false,
      isSmsAgree: false,
      isEmailVerified: false,
      isMobileVerified: false,
      mobileVerifiedCode: '',
      emailVerifiedCode: '',
    },
  });

  return {
    form,
  };
};
