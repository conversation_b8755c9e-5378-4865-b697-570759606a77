'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { findUserId } from '@/entities/auth/api/findUserId';

import { useToast } from '@/shared/model/useToast';

import { FindEmailFormData, findEmailFormSchema } from '../lib/findEmailFormSchema';

export const useFindEmail = () => {
  const { successToast, errorToast } = useToast();
  const [findedEmail, setFindedEmail] = useState<string>('');
  const form = useForm<FindEmailFormData>({
    resolver: zodResolver(findEmailFormSchema),
    mode: 'onChange',
  });

  const onSubmit = async (formData: FindEmailFormData) => {
    const { name, mobileNumber, apiKey } = formData;
    try {
      const response = await findUserId({
        name,
        mobileNumber,
        apiKey,
      });

      if (response.account) {
        setFindedEmail(response.account);
        successToast({
          title: '이메일 찾기 성공',
        });
        return true;
      }
      return false;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          errorToast({
            title: '등록된 아이디가 없습니다.',
            description: '회원가입을 진행해 주세요.',
            duration: 6000,
          });
        }
      }
    }
  };

  return { form, onSubmit, findedEmail };
};
