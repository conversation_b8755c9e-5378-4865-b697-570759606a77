'use client';

import { SignType } from '@/entities/auth/types';

import env from '@/shared/lib/env.schema';
import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useNaverLogin = ({ type }: { type: SignType }) => {
  const { isApp } = useWebViewRouter();

  const handleNaverLogin = () => {
    if (isApp) {
      WebViewMessage('openNaverLogin', {
        state: `naver-${type}`,
      });
    } else {
      const NaverIdLogin = (window as any).naver;

      const naverLogin = new NaverIdLogin.LoginWithNaverId({
        clientId: env.NEXT_PUBLIC_NAVER_CLIENT_ID,
        callbackUrl: `${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?state=naver-${type}`,
        state: `naver`,
        callbackHandle: true,
      });
      naverLogin.init();
      naverLogin.reprompt();
    }
  };

  return { handleNaverLogin };
};
