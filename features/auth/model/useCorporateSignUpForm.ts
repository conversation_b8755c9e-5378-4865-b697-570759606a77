import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import {
  BusinessSignUpFormData,
  businessSignUpFormSchema,
} from '../../../features/auth/lib/businessSignUpFormSchema';

export const useCorporateSignUpForm = () => {
  const form = useForm<BusinessSignUpFormData>({
    resolver: zodResolver(businessSignUpFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      email: '',
      isTermsAgree: false,
      isPrivacyAgree: false,
      isEmailVerified: false,
      emailVerifiedCode: '',
      brn: '',
      crn: '',
      representativeName: '',
      companyName: '',
      brc: [],
      managerMobileNumber: '',
    },
  });

  const handleDeleteFile = (file: File) => {
    form.setValue('brc', form.watch('brc')?.filter((f) => f.name !== file.name) || []);
  };

  const handleUploadedFiles = (files: File[]) => {
    form.setValue('brc', [...(form.watch('brc') || []), ...files]);
  };

  return {
    form,
    handleDeleteFile,
    handleUploadedFiles,
  };
};
