'use client';

import { SignType } from '@/entities/auth/types';

import env from '@/shared/lib/env.schema';
import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useKakaoLogin = ({ type }: { type: SignType }) => {
  const { isApp } = useWebViewRouter();

  const kakaoInit = () => {
    if (isApp) {
      WebViewMessage('openKakaoLogin', {
        state: `kakao-${type}`,
      });
      return;
    } else {
      const kakao = (window as any).Kakao;

      if (!kakao.isInitialized()) {
        kakao.init(env.NEXT_PUBLIC_KAKAO_API_KEY);
      }

      return kakao;
    }
  };

  const kakaoLogin = async () => {
    const kakao = kakaoInit();

    if (!kakao) return;

    kakao.Auth.authorize({
      redirectUri: `${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback`,
      state: `kakao-${type}`,
      prompts: 'login',
    });
  };

  return { kakaoLogin };
};
