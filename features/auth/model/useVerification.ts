import { usePathname } from 'next/navigation';
import { useRef, useState } from 'react';

import { checkEmailDuplication } from '@/entities/auth/api/checkEmailDuplication';
import { checkMobileNumberDuplication } from '@/entities/auth/api/checkMobileNumberDuplication';
import { requestVerification } from '@/entities/verifications/api/requestVerification';
import { verificationVerify } from '@/entities/verifications/api/verificationVerify';
import { VerificationType } from '@/entities/verifications/types';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

export const useVerification = (type: VerificationType) => {
  const [verificationTimer, setVerificationTimer] = useState<number | null>(null);
  const [isDuplicate, setIsDuplicate] = useState({
    isOpen: false,
    signupPath: [],
  });
  const { isVisible: isEmailVerificationVisible, openToggle: openEmailVerification } =
    useVisibility();
  const pathname = usePathname();

  const { successToast, errorToast } = useToast();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const verificationTimerText =
    verificationTimer !== null
      ? `${Math.floor(verificationTimer / 60)
          .toString()
          .padStart(2, '0')}:${(verificationTimer % 60).toString().padStart(2, '0')}`
      : '00:00';

  const handleOpenEmailDuplicateConfirmDialog = () => {
    setIsDuplicate((prev) => ({ ...prev, isOpen: !prev.isOpen }));
  };

  const handleVerificationTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // 새로운 interval 설정
    intervalRef.current = setInterval(() => {
      setVerificationTimer((prev) => {
        if (prev === 1) {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          return null;
        }
        return prev! - 1;
      });
    }, 1000);
  };

  const verifyContact = async (contact: string) => {
    try {
      if (type === VerificationType.EMAIL) {
        const { data } = await checkEmailDuplication(contact);
        if (data.isExist) {
          setIsDuplicate({
            isOpen: true,
            signupPath: data.signupPath,
          });
          return;
        }
      } else if (type === VerificationType.SMS && pathname.includes('signup')) {
        const { data } = await checkMobileNumberDuplication(contact);
        if (data.isExist) {
          setIsDuplicate({
            isOpen: true,
            signupPath: data.signupPath,
          });
          return;
        }
      }
      await requestVerification({ contact, type });

      successToast({
        title: type === VerificationType.EMAIL ? '이메일 인증 요청' : '휴대폰 인증 요청',
        description:
          type === VerificationType.EMAIL
            ? '이메일 인증 요청이 완료되었습니다.'
            : '휴대폰 인증 요청이 완료되었습니다.',
      });
      openEmailVerification();
      setVerificationTimer(180);
      handleVerificationTimer();
    } catch (error) {
      errorToast({
        title: '인증 요청 실패',
        description: '인증 요청 실패',
      });
      console.error(error);
    }
  };

  const verifyCode = async (contact: string, code: string) => {
    if (!verificationTimer || verificationTimer === 0) {
      errorToast({
        title: '인증 시간이 만료되었습니다.',
        description: '인증 시간이 만료되었습니다.',
      });
      return;
    }

    try {
      const { data } = await verificationVerify({
        contact,
        type,
        code,
      });

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setVerificationTimer(null);

      return data;
    } catch (error) {
      console.error(error);
      errorToast({
        title: '인증 실패',
        description: '인증 번호를 확인해주세요.',
      });
    }
  };

  const handleCloseEmailDuplicateConfirmDialog = () => {
    setIsDuplicate((prev) => ({ ...prev, isOpen: false }));
  };

  return {
    verifyContact,
    verifyCode,
    verificationTimerText,
    verificationTimer,
    isEmailVerificationVisible,
    handleOpenEmailDuplicateConfirmDialog,
    isDuplicate,
    handleCloseEmailDuplicateConfirmDialog,
  };
};
