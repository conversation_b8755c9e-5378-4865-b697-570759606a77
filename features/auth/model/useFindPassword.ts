'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

import { sendGeneralResetLink } from '@/entities/auth/api/sendGeneralResetLink';

import { useToast } from '@/shared/model/useToast';

import { FindPasswordFormData, findPasswordFormSchema } from '../lib/findPasswordFormSchema';

export const useFindPassword = () => {
  const { successToast, errorToast } = useToast();
  const router = useRouter();
  const form = useForm<FindPasswordFormData>({
    resolver: zodResolver(findPasswordFormSchema),
    mode: 'onChange',
  });

  const onSubmit = async (formData: FindPasswordFormData) => {
    const { email, name } = formData;
    try {
      await sendGeneralResetLink({
        name,
        account: email,
      });

      successToast({
        title: '비밀번호 재설정 링크가 이메일로 발송되었습니다.',
      });
      router.replace('/sign-in');
      return true;
    } catch (error) {
      errorToast({
        title: '비밀번호 재설정 링크 발송 실패',
      });
      return false;
    }
  };

  return { form, onSubmit };
};
