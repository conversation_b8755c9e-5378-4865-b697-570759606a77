'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Cookies from 'js-cookie';
import { useForm } from 'react-hook-form';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

import { formSchema, SignInFormData } from '../lib/signInFormSchema';
import { signinWithEmailCredential } from '../model/signinWithEmailCredential';
import { signinCorporateCredential } from './signinCorporateCredential';

export const useSignIn = ({ isCorporate = false }: { isCorporate?: boolean }) => {
  const { successToast, errorToast } = useToast();
  const initialSaveId = !!Cookies.get('saveId');
  const { isVisible: isSaveId, toggleVisibility: toggleSaveId } = useVisibility(initialSaveId);

  const form = useForm<SignInFormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      account: Cookies.get('saveId') || '',
    },
  });

  const onSubmit = async (data: SignInFormData) => {
    const callbackUrl = Cookies.get('callback-url') || '/';

    Cookies.remove('callback-url');

    try {
      if (isSaveId) {
        Cookies.set('saveId', data.account, { expires: 90 });
      } else {
        Cookies.remove('saveId');
      }

      if (isCorporate) {
        await signinCorporateCredential(data);
      } else {
        await signinWithEmailCredential(data);
      }

      successToast({
        title: '로그인 성공',
      });
      window.location.href = callbackUrl;
    } catch (error) {
      errorToast({
        title: '로그인 실패',
        description: '아이디 또는 비밀번호를 확인해 주세요.',
      });
    }
  };

  return { form, onSubmit, isSaveId, toggleSaveId };
};
