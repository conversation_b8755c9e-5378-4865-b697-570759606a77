import { PropensityAnswer, PropensityTypeEnum } from '@/entities/investor/types';

import { propensityQuestions } from '../config';

// 투자 성향 점수 계산 함수
export const calculatePropensityScore = (answers: PropensityAnswer): number => {
  const calculateFinalScore = (totalScore: number): number => {
    if (totalScore <= 80) return 1;
    if (totalScore <= 120) return 2;
    return 3;
  };

  let totalScore = 0;
  let multiV2Score = 0;

  for (const answer of Object.values(answers)) {
    const question = propensityQuestions.find((q) => q.questionNumber === answer.questionNumber);
    if (!question) continue;

    const { selectionMode, selection, score } = question;

    if (selectionMode === 'single' && answer.selectionMode === 'single') {
      const selectionIndex = selection.findIndex(
        (item) => Object.keys(item)[0] === answer.selectedKey,
      );
      totalScore += score[selectionIndex] ? parseFloat(score[selectionIndex]) : 0;
    } else if (selectionMode === 'multi' && answer.selectionMode === 'multi') {
      for (const key of answer.selectedKeys) {
        const selectionIndex = selection.findIndex((item) => Object.keys(item)[0] === key);
        totalScore += score[selectionIndex] ? parseFloat(score[selectionIndex]) : 0;
      }
    } else if (selectionMode === 'multi_v2' && answer.selectionMode === 'multi_v2') {
      for (const { key, value } of answer.selected) {
        const selectionIndex = selection.findIndex((item) => Object.keys(item)[0] === key);
        const percent = parseInt(value.replace('%', ''), 10);
        const weight = score[selectionIndex] ? parseFloat(score[selectionIndex]) : 0;
        multiV2Score += (percent / 100) * weight;
      }
    }
  }

  return totalScore + calculateFinalScore(multiV2Score);
};

// // 투자 성향 유형 결정 함수
export const determinePropensityType = (
  score: number,
  convertedScore: number,
): PropensityTypeEnum => {
  const result = Math.min(convertedScore, score);

  if (result > 80) {
    return PropensityTypeEnum.AGGRESSIVE; // 공격투자형
  } else if (result > 60) {
    return PropensityTypeEnum.ACTIVE; // 적극투자형
  } else if (result > 40) {
    return PropensityTypeEnum.NEUTRAL; // 위험중립형
  } else if (result > 20) {
    return PropensityTypeEnum.STABLE; // 안정추구형
  } else {
    return PropensityTypeEnum.CONSERVATIVE; // 안정형
  }
};
