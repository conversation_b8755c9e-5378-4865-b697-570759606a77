'use client';

import { z } from 'zod';

import { InvestorQualificationType } from '@/entities/investor/types';

export const applyInvestorTypeFormSchema = z.object({
  investorQualificationType: z.nativeEnum(InvestorQualificationType),

  attachFiles: z.array(z.any()).min(1, { message: '최소 1개 이상의 파일을 첨부해주세요.' }),
});

export type ApplyInvestorTypeFormData = z.infer<typeof applyInvestorTypeFormSchema>;
