import { PropensityTypeEnum } from '@/entities/investor/types';

export const propensityTypeDescription = (type: PropensityTypeEnum) => {
  switch (type) {
    case PropensityTypeEnum.AGGRESSIVE:
      return '높은 수준의 투자 수익을 추구하며, 자산의 높은 손실 위험을 적극 수용합니다.';
    case PropensityTypeEnum.ACTIVE:
      return '투자원금의 보전보다는 위험을 감내하더라도 높은 수준의 투자수익을 추구하는 성향입니다.';
    case PropensityTypeEnum.NEUTRAL:
      return '투자에 상응하는 투자위험이 있음을 충분히 인식하고 있습니다.';
    case PropensityTypeEnum.STABLE:
      return '원금 손실 위험을 최소화하고, 배당 수준의 안정적 투자를 목표로 합니다.';
    case PropensityTypeEnum.CONSERVATIVE:
      return '투자원금의 손실이 발생하는 것을 원하지 않으며, 안전한 투자를 목표로 합니다.';
  }
};
