import { PropensityTypeEnum } from '@/entities/investor/types';

export const propensityTypeColor = (type: PropensityTypeEnum) => {
  switch (type) {
    case PropensityTypeEnum.AGGRESSIVE:
      return 'text-pink-500 outline-pink-500 bg-pink-00';
    case PropensityTypeEnum.ACTIVE:
      return 'text-deep-purple-500 outline-deep-purple-500 bg-deep-purple-00';
    case PropensityTypeEnum.NEUTRAL:
      return 'text-primary-500 outline-primary-500 bg-primary-00';
    case PropensityTypeEnum.CONSERVATIVE:
      return 'text-cyan-500 outline-cyan-500 bg-cyan-00';
    case PropensityTypeEnum.STABLE:
      return 'text-green-500 outline-green-500 bg-green-00';
    default:
      return 'outline-gray-300 bg-gray-50';
  }
};
