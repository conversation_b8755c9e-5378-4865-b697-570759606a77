import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { PropensityTypeEnum } from '@/entities/investor/types';

import { propensityTypeDescription } from '../lib';
import { propensityTypeColor } from '../lib/propensityTypeColor';

interface PropensityTypeButtonProps {
  label: string;
  isActive: boolean;
  value: PropensityTypeEnum;
  onSelectedType: (type: PropensityTypeEnum) => void;
}

export const PropensityTypeButton = ({
  label,
  isActive,
  value,
  onSelectedType,
}: PropensityTypeButtonProps) => {
  return (
    <button
      className={`w-full rounded-lg px-5 py-4 text-sm font-semibold sm:p-8 sm:text-lg ${isActive ? `outline outline-2 ${propensityTypeColor(value)}` : 'bg-gray-50 outline outline-gray-300 hover:outline-gray-500'}`}
      onClick={() => onSelectedType(value)}
    >
      <div className="flex items-center justify-between">
        {label}
        <ChevronDownIcon
          className={`h-5 w-5 text-gray-900 duration-300 sm:h-6 sm:w-6 ${isActive ? 'rotate-180' : ''}`}
          strokeWidth={2}
        />
      </div>

      {isActive && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{
            duration: 0.3,
          }}
        >
          <p className="mt-4 text-left text-xs font-normal text-gray-900 sm:text-base">
            {propensityTypeDescription(value)}
          </p>
        </motion.div>
      )}
    </button>
  );
};
