import React from 'react';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface PropensityTermsButtonProps {
  text: string;
  isActive: boolean;
  onClick: () => void;
}

export const PropensityTermsButton = ({ text, isActive, onClick }: PropensityTermsButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={`flex h-9 w-full items-center justify-center gap-2 rounded-lg text-xs sm:h-[44px] sm:text-base ${isActive ? 'text-primary-500 outline outline-2 outline-primary-500' : 'outline outline-gray-300'} bg-gray-50 text-sm font-semibold`}
    >
      {isActive && <FallbackImage src="/icons/checked.png" alt="check" width={16} height={16} />}
      {text}
    </button>
  );
};
