import { TrashIcon } from '@heroicons/react/24/outline';
import { PaperClipIcon, PlusIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { InvestorQualificationType } from '@/entities/investor/types';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { FileInfoTooltop } from '@/shared/ui/FileInfoTooltop';
import { FileUploader } from '@/shared/ui/FileUploader';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Checkbox } from '@/shared/ui/shadcn/checkbox';

import { useApplyInvestorTypeForm } from '../model/useApplyInvestorTypeForm';

export const ApplyInvestorTypeForm = ({
  isQualificationApplying,
}: {
  isQualificationApplying: boolean;
}) => {
  const {
    form,
    handleFileUpload,
    isLoading,
    ref,
    handleDeleteFile,
    handleUploadedFiles,
    onSubmit,
    isApply,
    toggleApply,
  } = useApplyInvestorTypeForm();
  const { user } = useFetchUser();

  const { watch, setValue, handleSubmit } = form;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.3 }}
      className="space-y-10 border-b border-t border-gray-300 bg-blue-gray-00 p-8"
    >
      <div className="flex items-center justify-between">
        <div className="text-20 flex items-center gap-4">
          <p>투자자 유형 변경 신청</p>
          {isQualificationApplying && (
            <span className="rounded bg-blue-50 px-2 py-[3px] text-xs font-semibold text-blue-500">
              투자자 유형 변경 신청이 완료되었으며 심사가 진행 중입니다.
            </span>
          )}
        </div>
        <button className="text-xs font-semibold text-gray-500 underline">
          투자자 유형 변경 안내
        </button>
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="investorType" className="font-semibold">
          변경 유형
        </label>
        <div className="flex gap-8">
          <div className="flex items-center gap-2">
            <Checkbox
              value={InvestorQualificationType.QUALIFIED}
              disabled={
                user?.investorQualification?.current?.qualificationType ===
                  InvestorQualificationType.QUALIFIED || isQualificationApplying
              }
              onCheckedChange={(checked) => {
                setValue(
                  'investorQualificationType',
                  checked ? InvestorQualificationType.QUALIFIED : InvestorQualificationType.GENERAL,
                );
              }}
              checked={watch('investorQualificationType') === InvestorQualificationType.QUALIFIED}
              className="h-5 w-5 rounded-full bg-white text-white shadow-none data-[state=checked]:bg-primary-500"
            />
            <p className="text-sm">적격투자자</p>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              value={InvestorQualificationType.PROFESSIONAL}
              onCheckedChange={(checked) => {
                setValue(
                  'investorQualificationType',
                  checked
                    ? InvestorQualificationType.PROFESSIONAL
                    : InvestorQualificationType.GENERAL,
                );
              }}
              disabled={isQualificationApplying}
              checked={
                watch('investorQualificationType') === InvestorQualificationType.PROFESSIONAL
              }
              className="h-5 w-5 rounded-full bg-white text-white shadow-none data-[state=checked]:bg-primary-500"
            />
            <p className="text-sm">전문투자자</p>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <p className="flex items-center gap-1 font-semibold">
          제출 서류 등록 <strong className="font-semibold text-primary-500">(필수)</strong>{' '}
          <FileInfoTooltop />
        </p>
        {watch('attachFiles')?.map((file) => (
          <div
            className="flex justify-between rounded-lg border border-gray-300 bg-white px-4 py-3"
            key={file.name}
          >
            <div className="flex items-center gap-2">
              <PaperClipIcon color="blue" className="h-5 w-5" />
              {file.name}
            </div>
            <button type="button" onClick={() => handleDeleteFile(file)}>
              <TrashIcon className="h-5 w-5" />
            </button>
          </div>
        ))}
        <Button
          type="button"
          disabled={isQualificationApplying}
          onClick={handleFileUpload}
          className="h-[44px] w-full rounded-lg border border-gray-300 bg-white text-gray-500 shadow-none sm:h-12"
        >
          <PlusIcon className="h-5 w-5" />
          추가할 파일 선택
        </Button>
      </div>
      {!isQualificationApplying && (
        <PrimaryButton
          disabled={!form.formState.isValid || isLoading}
          text="신청하기"
          className="w-40"
          onClick={toggleApply}
        />
      )}
      <FileUploader
        ref={ref}
        uploadedFiles={handleUploadedFiles}
        multiple
        maxSizeMB={5}
        acceptType=".pdf,.jpg,.png"
      />
      <ConfirmDialog
        isOpen={isApply}
        handleAction={handleSubmit(onSubmit)}
        handleOpen={toggleApply}
        title="투자자 유형 변경 신청"
        description="투자자 유형 변경 신청을 하시겠습니까?"
      />
    </motion.div>
  );
};
