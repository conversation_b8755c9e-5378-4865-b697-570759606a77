import React from 'react';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SuitabilityTestButtonProps {
  icon: 'A' | 'B';
  label: string;
  isActive?: boolean;
  onSelect: () => void;
}

export const SuitabilityTestButton = ({
  label,
  icon,
  isActive = true,
  onSelect,
}: SuitabilityTestButtonProps) => {
  return (
    <button
      className={`flex w-full items-center justify-between gap-3 rounded-lg px-5 py-4 sm:p-8 ${isActive ? 'border-2 border-primary-500 bg-primary-00 font-bold text-primary-500' : 'border border-gray-300 bg-gray-50 hover:border-gray-500'}`}
      onClick={onSelect}
    >
      <div className="flex items-center gap-3">
        <span
          className={`flex !h-[30px] !w-[30px] flex-shrink-0 items-center justify-center rounded font-semibold ${isActive ? 'bg-primary-500 text-white' : 'border border-gray-300 bg-white'}`}
        >
          {icon}
        </span>
        <span className="break-keep text-left text-sm font-semibold sm:text-base sm:font-normal">
          {label}
        </span>
      </div>
      {isActive && (
        <FallbackImage src="/icons/checked.png" alt="arrow-right" width={24} height={24} />
      )}
    </button>
  );
};
