import React from 'react';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface PropensityCommonButtonProps {
  text: string;
  isActive: boolean;
  onClick: () => void;
  description?: string;
}

export const PropensityCommonButton = ({
  text,
  onClick,
  isActive,
  description,
}: PropensityCommonButtonProps) => {
  return (
    <button
      className={`w-full rounded-lg bg-gray-50 px-5 py-4 text-sm font-semibold sm:px-8 sm:py-[30px] sm:text-lg sm:font-normal ${isActive ? 'font-bold text-primary-500 outline outline-2 outline-primary-500 hover:outline-primary-500' : 'font-normal outline outline-gray-300 hover:outline-gray-400'}`}
      onClick={onClick}
    >
      <div className="flex w-full items-center justify-between text-left tracking-tight">
        <div className="flex flex-col gap-2">
          <span className={`${description && 'font-bold'}`}>{text}</span>
          {description && <span className="text-base">{description}</span>}
        </div>
        {isActive && <FallbackImage src="/icons/checked.png" alt="check" width={24} height={24} />}
      </div>
    </button>
  );
};
