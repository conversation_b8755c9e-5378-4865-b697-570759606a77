import React from 'react';

import { PropensityTermsButton } from './PropensityTermsButton';

interface PropensityTermsItemProps {
  title: string;
  description: string;
  isAgree: boolean | undefined;
  onAgree: (isAgree: boolean) => void;
}

export const PropensityTermsItem = ({
  title,
  description,
  isAgree,
  onAgree,
}: PropensityTermsItemProps) => {
  return (
    <div className="space-y-6 rounded-lg border border-gray-300 p-5 sm:space-y-8 sm:p-8">
      <div className="space-y-2">
        <h4 className="font-semibold sm:text-lg">
          {title}
          <strong className="font-semibold text-primary-500">*</strong>
        </h4>
        <p className="text-xs sm:text-base">{description}</p>
      </div>
      <div className="flex gap-2">
        <PropensityTermsButton
          text="예"
          isActive={isAgree === true}
          onClick={() => onAgree(true)}
        />
        <PropensityTermsButton
          text="아니오"
          isActive={isAgree === false}
          onClick={() => onAgree(false)}
        />
      </div>
    </div>
  );
};
