import React from 'react';

import { InvestorTestButtonSkeleton } from '@/entities/investor/ui/InvestorTestButtonSkeleton';

import { SuitabilityTestButton } from './SuitabilityTestButton';

interface SuitabilityTestFormProps {
  question: string;
  options: { label: 'A' | 'B'; text: string }[];
  onSelect: (label: 'A' | 'B') => void;
  selectedTest?: { questionNumber: number; answer: 'A' | 'B' };
  isSelected: (questionNumber: number) => 'A' | 'B' | null;
}

export const SuitabilityTestForm = ({
  question,
  options,
  onSelect,
  selectedTest,
  isSelected,
}: SuitabilityTestFormProps) => {
  const isEmpty = !options || options.length === 0;

  return (
    <div className="my-6 w-full sm:my-20">
      <h4 className="text-18 sm:text-28">{question}</h4>
      <div className="mt-6 space-y-3 sm:mt-10">
        {isEmpty ? (
          <>
            <InvestorTestButtonSkeleton icon />
            <InvestorTestButtonSkeleton icon />
          </>
        ) : (
          options?.map((option) => (
            <SuitabilityTestButton
              key={option.label}
              label={option.text}
              icon={option.label}
              isActive={isSelected(selectedTest?.questionNumber as number) === option.label}
              onSelect={() => onSelect(option.label)}
            />
          ))
        )}
      </div>
    </div>
  );
};
