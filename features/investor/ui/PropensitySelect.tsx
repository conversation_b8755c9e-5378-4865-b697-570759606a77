'use client';

import { CheckIcon } from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import React, { useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { useVisibility } from '@/shared/model/useVisibility';
import { ScrollArea } from '@/shared/ui/shadcn/scroll-area';

interface PropensitySelectProps {
  placeholder?: string;
  value: any;
  onChange: (value: string) => void;
  options: string[];
  className?: string;
  props?: React.HTMLAttributes<HTMLButtonElement>;
  id: string;
}

export const PropensitySelect = ({
  placeholder,
  value,
  onChange,
  options,
  className,
  id,
  ...props
}: PropensitySelectProps) => {
  const { isVisible, toggleVisibility } = useVisibility();
  const selectRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
      if (isVisible) {
        toggleVisibility();
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible]);

  const dataTestId = (props as any)?.['data-testid'];

  return (
    <div className="relative" ref={selectRef}>
      <div className="flex w-full flex-1 items-center justify-between">
        <button
          type="button"
          {...props}
          onClick={toggleVisibility}
          className={`flex h-[40px] w-full min-w-[86px] items-center justify-start rounded-lg border border-gray-300 bg-white px-4 text-lg font-bold text-gray-500 hover:border-gray-500 sm:h-[50px] sm:min-w-[112px] ${className}`}
        >
          {value ? (
            <div className="flex w-full items-center justify-between text-sm sm:text-lg">
              {options.find((option) => option === value)}
              <ChevronDownIcon className="h-4 w-4 text-gray-500 sm:h-5 sm:w-5" />
            </div>
          ) : (
            <div
              className={`flex w-full items-center text-sm sm:text-lg ${placeholder ? 'justify-between' : 'justify-end'}`}
            >
              {placeholder}
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            </div>
          )}
        </button>
      </div>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.1 }}
          className="absolute left-0 top-14 z-40 w-full min-w-[86px] rounded-md border border-gray-300 bg-white p-1 shadow sm:min-w-[112px]"
        >
          <ScrollArea className="flex max-h-[200px] flex-col items-start">
            {options.map((option) => (
              <button
                {...(dataTestId && {
                  'data-testid': `${dataTestId}-option-${option}`,
                })}
                key={`${id}-${uuidv4()}`}
                type="button"
                onClick={() => {
                  onChange(option);
                  toggleVisibility();
                }}
                className="flex w-full items-center justify-between gap-2 rounded p-2 text-gray-700 hover:bg-gray-100"
              >
                {option}
                {value === option && <CheckIcon className="h-4 w-4" />}
              </button>
            ))}
          </ScrollArea>
        </motion.div>
      )}
    </div>
  );
};
