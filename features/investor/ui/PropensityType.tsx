import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { PropensityTypeEnum } from '@/entities/investor/types';

import { PropensityTypeButton } from './PropensityTypeButton';

interface PropensityTypeProps {
  selectedType: PropensityTypeEnum | undefined;
  onSelectedType: (type: PropensityTypeEnum) => void;
}

export const PropensityType = ({ selectedType, onSelectedType }: PropensityTypeProps) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="max-w-screen-test mx-auto mb-20 mt-6 px-6 sm:my-20 sm:px-8 ml:px-0"
      >
        <div className="space-y-2 sm:space-y-4">
          <PropensityTypeButton
            onSelectedType={onSelectedType}
            label="공격투자형"
            isActive={selectedType === PropensityTypeEnum.AGGRESSIVE}
            value={PropensityTypeEnum.AGGRESSIVE}
          />
          <PropensityTypeButton
            onSelectedType={onSelectedType}
            label="적극투자형"
            isActive={selectedType === PropensityTypeEnum.ACTIVE}
            value={PropensityTypeEnum.ACTIVE}
          />
          <PropensityTypeButton
            onSelectedType={onSelectedType}
            label="위험중립형"
            isActive={selectedType === PropensityTypeEnum.NEUTRAL}
            value={PropensityTypeEnum.NEUTRAL}
          />
          <PropensityTypeButton
            onSelectedType={onSelectedType}
            label="안정추구형"
            isActive={selectedType === PropensityTypeEnum.CONSERVATIVE}
            value={PropensityTypeEnum.CONSERVATIVE}
          />
          <PropensityTypeButton
            onSelectedType={onSelectedType}
            label="안정형"
            isActive={selectedType === PropensityTypeEnum.STABLE}
            value={PropensityTypeEnum.STABLE}
          />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
