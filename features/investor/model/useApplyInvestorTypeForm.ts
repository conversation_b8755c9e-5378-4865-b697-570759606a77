import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef } from 'react';
import { useForm } from 'react-hook-form';

import { queries } from '@/features/lib/queries';

import { applyInvestorTypeChange } from '@/entities/investor/api/applyInvestorTypeChange';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import {
  ApplyInvestorTypeFormData,
  applyInvestorTypeFormSchema,
} from '../lib/applyInvestorTypeFormSchema';

export const useApplyInvestorTypeForm = () => {
  const ref = useRef<HTMLInputElement>(null);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const router = useRouter();
  const queryClient = useQueryClient();
  const { successToast, errorToast } = useToast();
  const { isVisible: isLoading, toggleVisibility: toggleLoading } = useVisibility();
  const {
    isVisible: isApply,
    toggleVisibility: toggleApply,
    closeToggle: closeApply,
  } = useVisibility();

  const form = useForm<ApplyInvestorTypeFormData>({
    resolver: zodResolver(applyInvestorTypeFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      attachFiles: [],
    },
  });

  const onSubmit = async (data: ApplyInvestorTypeFormData) => {
    toggleLoading();
    closeApply();
    try {
      await applyInvestorTypeChange({
        qualificationType: data.investorQualificationType,
        files: data.attachFiles,
      });
      await queryClient.invalidateQueries({
        queryKey: queries.investor._def,
      });
      await queryClient.invalidateQueries({
        queryKey: queries.user._def,
      });
      successToast({
        title: '투자자 유형 변경 신청이 완료되었습니다.',
      });
      form.reset();
      if (callbackUrl) {
        router.replace(callbackUrl);
        return;
      }
    } catch (error) {
      errorToast({
        title: '투자자 유형 변경 신청에 실패했습니다.',
      });
    } finally {
      toggleLoading();
    }
  };

  const handleFileUpload = () => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const handleDeleteFile = (file: File) => {
    form.setValue(
      'attachFiles',
      form.watch('attachFiles')?.filter((f) => f.name !== file.name) || [],
      {
        shouldValidate: true,
      },
    );
  };

  const handleUploadedFiles = (files: File[]) => {
    form.setValue('attachFiles', [...(form.watch('attachFiles') || []), ...files], {
      shouldValidate: true,
    });
  };

  return {
    form,
    handleFileUpload,
    handleDeleteFile,
    ref,
    handleUploadedFiles,
    onSubmit,
    isLoading,
    isApply,
    toggleApply,
  };
};
