import { useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { queries } from '@/features/lib/queries';

import { investorPropensityRegister } from '@/entities/investor/api/investorPropensityRegister';
import {
  MultiAnswer,
  MultiRatioAnswer,
  PropensityAnswer,
  PropensityLevels,
  PropensityScore,
  PropensitySteps,
  PropensityTerms,
  PropensityTypeEnum,
  SingleAnswer,
} from '@/entities/investor/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';

import { fetchPropensityTest } from '../api/fetchPropensityTest';
import { propensityQuestions } from '../config';
import { calculatePropensityScore, determinePropensityType } from '../lib/calculatePropensityScore';

export const useInvestorPropensity = ({ onboarding = false }: { onboarding?: boolean }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const [selectedType, setSelectedType] = useState<PropensityTypeEnum>();
  const [terms, setTerms] = useState<PropensityTerms>({
    isHopeInvestment: undefined,
    isInfoAgree: undefined,
    isVulnerable: undefined,
  });
  const [answers, setAnswers] = useState<PropensityAnswer>({});
  const { step, handleStep, isStep } = useStep<PropensitySteps>(PropensitySteps.NOTICE);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const { successToast, errorToast } = useToast();
  const [propensityScore, setPropensityScore] = useState<PropensityScore>({
    score: 0,
    convertedScore: 0,
    type: undefined,
  });
  const queryClient = useQueryClient();
  const pathname = usePathname();

  const isOnboarding = pathname.includes('onboarding');

  const { data: propensityTest, isLoading: isLoadingPropensityTest } = fetchPropensityTest();

  const handlePropensityScore = () => {
    const score = calculatePropensityScore(answers);
    const convertedScore = (score / 29) * 100;
    const type = determinePropensityType(score, convertedScore);

    setPropensityScore({
      score,
      convertedScore,
      type,
    });
  };

  useEffect(() => {
    if (isStep(PropensitySteps.RESULT)) {
      handlePropensityScore();
    }
  }, [step]);

  const handleSingleAnswer = (questionNumber: number, key: string): void => {
    setAnswers((prev) => ({
      ...prev,
      [questionNumber]: {
        questionNumber,
        selectionMode: 'single',
        selectedKey: key,
      } as SingleAnswer,
    }));
  };

  // 다중 선택 답변 처리 (일반 다중 선택)
  const handleMultiAnswer = (questionNumber: number, key: string): void => {
    setAnswers((prev) => {
      const currentAnswer = prev[questionNumber] as MultiAnswer | undefined;
      const selectedKeys = currentAnswer?.selectedKeys || [];

      return {
        ...prev,
        [questionNumber]: {
          selectionMode: 'multi',
          selectedKeys: selectedKeys.includes(key)
            ? selectedKeys.filter((k) => k !== key)
            : [...selectedKeys, key],
        } as MultiAnswer,
      };
    });
  };

  // 다중 선택 답변 처리 (비율 선택)
  const handleMultiRatioAnswer = (questionNumber: number, key: string, value: string): void => {
    setAnswers((prev) => {
      const currentAnswer = prev[questionNumber] as MultiRatioAnswer | undefined;
      const selected = currentAnswer?.selected || [];

      return {
        ...prev,
        [questionNumber]: {
          questionNumber,
          selectionMode: 'multi_v2',
          selected: selected.some((item) => item.key === key)
            ? selected.map((item) => (item.key === key ? { ...item, value } : item))
            : [...selected, { key, value }],
        } as MultiRatioAnswer,
      };
    });
  };

  const handleSelectedType = (type: PropensityTypeEnum) => {
    if (type === selectedType) {
      setSelectedType(undefined);
    } else {
      setSelectedType(type);
    }
  };

  const isDisabled = () => {
    if (isStep(PropensitySteps.TYPE)) {
      return !selectedType;
    }

    if (isStep(PropensitySteps.TERMS)) {
      return !terms.isHopeInvestment || !terms.isInfoAgree || terms.isVulnerable === undefined;
    }

    if (isStep(PropensitySteps.RESULT)) {
      return false;
    }

    // 현재 문제 번호
    const currentQuestionNumber = currentQuestion + 1;

    // 문제 데이터가 로드되지 않았거나 현재 문제 번호가 유효하지 않은 경우
    if (
      propensityQuestions.length === 0 ||
      currentQuestionNumber < 1 ||
      currentQuestionNumber > propensityQuestions.length
    ) {
      return true;
    }

    // 현재 문제에 대한 답변 확인
    const currentAnswer = answers[currentQuestionNumber];
    if (!currentAnswer) return true;

    // 답변 유형에 따른 유효성 검사
    if (currentAnswer.selectionMode === 'single') {
      return !(currentAnswer as SingleAnswer).selectedKey;
    } else if (currentAnswer.selectionMode === 'multi') {
      return (currentAnswer as MultiAnswer).selectedKeys.length === 0;
    } else if (currentAnswer.selectionMode === 'multi_v2') {
      const multiRatioAnswer = currentAnswer as MultiRatioAnswer;
      if (!multiRatioAnswer.selected || multiRatioAnswer.selected.length === 0) return true;

      // 모든 선택된 값의 합이 100%인지 확인
      const total = multiRatioAnswer.selected.reduce((sum, item) => {
        const value = parseInt(item.value.replace('%', ''), 10);
        return sum + value;
      }, 0);

      return total !== 100;
    }

    return true;
  };

  const handleTerms = (key: keyof PropensityTerms, value: boolean) => {
    setTerms({ ...terms, [key]: value });
  };

  const handleNext = async () => {
    const lastQuestion = propensityQuestions.length - 1;

    if (isStep(PropensitySteps.TYPE)) {
      handleStep(PropensitySteps.TERMS);
      return;
    }

    if (isStep(PropensitySteps.TERMS)) {
      handleStep(PropensitySteps.QUESTION);
      return;
    }

    if (isStep(PropensitySteps.QUESTION)) {
      if (currentQuestion === lastQuestion) {
        handleStep(PropensitySteps.RESULT);
      } else {
        setCurrentQuestion(currentQuestion + 1);
      }
      return;
    }

    if (isStep(PropensitySteps.RESULT)) {
      try {
        await investorPropensityRegister({
          report: JSON.stringify({
            id: propensityTest?.data[0].id,
            questions: propensityQuestions,
            answers,
          }),
          consent: true,
          score: propensityScore.score,
          convertedScore: propensityScore.convertedScore,
          grade: PropensityLevels[propensityScore.type as PropensityTypeEnum],
        });

        await queryClient.invalidateQueries({
          queryKey: queries.user._def,
        });

        successToast({
          title: '테스트 완료',
        });

        if (!onboarding) {
          router.replace(callbackUrl || '/');
        }
      } catch (error) {
        console.error(error);
        errorToast({
          title: '테스트 등록 실패',
        });
      }
      return;
    }
  };

  const handlePrev = () => {
    if (isStep(PropensitySteps.RESULT)) {
      setSelectedType(undefined);
      setTerms({
        isHopeInvestment: undefined,
        isInfoAgree: undefined,
        isVulnerable: undefined,
      });
      setAnswers({});
      handleStep(PropensitySteps.TYPE);
      return;
    }

    if (isStep(PropensitySteps.QUESTION)) {
      if (currentQuestion === 0) {
        handleStep(PropensitySteps.TYPE);
        return;
      }
      setCurrentQuestion(currentQuestion - 1);
      return;
    }

    if (isStep(PropensitySteps.TERMS)) {
      handleStep(PropensitySteps.TYPE);
      return;
    }

    if (isStep(PropensitySteps.TYPE)) {
      router.back();
      return;
    }
  };

  return {
    selectedType,
    terms,
    answers,
    handlePrev,
    handleSingleAnswer,
    handleMultiAnswer,
    handleMultiRatioAnswer,
    handleSelectedType,
    handleTerms,
    isDisabled,
    isStep,
    currentQuestion,
    step,
    handleNext,
    handleStep,
    propensityScore,
    isLoadingPropensityTest,
    isOnboarding,
  };
};
