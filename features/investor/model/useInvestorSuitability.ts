'use client';

import { useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { queries } from '@/features/lib/queries';

import { investorSuitabilityTestRegister } from '@/entities/investor/api/investorSuitabilityTestRegister';
import { InvestorSuitabilityTestStep, Question } from '@/entities/investor/types';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

import { fetchSuitabilityTest } from '../api/fetchSuitabilityTest';

export const useInvestorSuitability = () => {
  const [isEndTest, setIsEndTest] = useState(false);
  const [selectedTest, setSelectedTest] = useState<{ questionNumber: number; answer: 'A' | 'B' }[]>(
    [],
  );
  const { step, isStep, handleStep } = useStep<InvestorSuitabilityTestStep>(
    InvestorSuitabilityTestStep.NOTICE,
  );
  const { isVisible: isWrongAnswerVisible, toggleVisibility: toggleWrongAnswerVisibility } =
    useVisibility();
  const { isVisible: isSuccessVisible, toggleVisibility: toggleSuccessVisibility } =
    useVisibility();
  const [testIndex, setTestIndex] = useState(0);
  const router = useRouter();
  const [questions, setQuestions] = useState<Question[]>([]);
  const { successToast, errorToast } = useToast();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const queryClient = useQueryClient();
  const { data: suitabilityTest, isLoading: isLoadingSuitabilityTest } = fetchSuitabilityTest();
  const pathname = usePathname();
  const isOnboarding = pathname.includes('onboarding');

  useEffect(() => {
    if (suitabilityTest) {
      setQuestions(suitabilityTest.data[0].questions);
    }
  }, [suitabilityTest]);

  const isLastTest = testIndex === questions.length - 1;

  const isDisabled = !selectedTest[testIndex]?.answer;

  const handleNextTest = () => {
    if (isLastTest) {
      setIsEndTest(true);
    } else {
      setTestIndex(testIndex + 1);
    }
  };

  useEffect(() => {
    if (!isEndTest) return;

    // 틀린 문제들만 필터링
    const wrongQuestions = questions.filter((question) => {
      const selectedAnswer = selectedTest.find(
        (test) => test.questionNumber === question.questionNumber,
      );

      // 선택된 답이 없거나 정답과 다르면 틀린 문제
      return !selectedAnswer || selectedAnswer.answer !== question.correctAnswer;
    });

    if (wrongQuestions.length > 0) {
      setQuestions(wrongQuestions);
      toggleWrongAnswerVisibility();
    } else if (wrongQuestions.length === 0) {
      toggleSuccessVisibility();
      handleRegisterTest();
    }
  }, [isEndTest]);

  const handleRegisterTest = async () => {
    try {
      await investorSuitabilityTestRegister({
        investorSuitabilityQuestionId: suitabilityTest.data[0].id,
        consent: true,
      });
      await queryClient.invalidateQueries({
        queryKey: queries.user._def,
      });
      successToast({
        title: '테스트 완료',
      });
      handleStep(InvestorSuitabilityTestStep.RESULT);
    } catch (error) {
      console.error(error);
      errorToast({
        title: '테스트 등록 실패',
      });
    }
  };

  const handleSelectTest = ({
    questionNumber,
    answer,
  }: {
    questionNumber: number;
    answer: 'A' | 'B';
  }) => {
    setSelectedTest((prev) =>
      prev.some((test) => test.questionNumber === questionNumber)
        ? prev.map((test) => (test.questionNumber === questionNumber ? { ...test, answer } : test))
        : [...prev, { questionNumber, answer }],
    );
  };

  const handlePreviousTest = () => {
    if (testIndex === 0) {
      router.back();
    } else {
      setTestIndex(testIndex - 1);
    }
  };
  const handleRetakeTest = () => {
    setTestIndex(0);
    setSelectedTest([]);
    setIsEndTest(false);
    toggleWrongAnswerVisibility();
  };

  const isSelected = (questionNumber: number): 'A' | 'B' | null => {
    const currentQuestion = questions.find(
      (question) => question.questionNumber === questionNumber,
    );

    if (!currentQuestion) return null;

    const selectedAnswer = selectedTest.find(
      (test) => test.questionNumber === currentQuestion.questionNumber,
    );
    if (!selectedAnswer) return null;

    return selectedAnswer.answer;
  };

  const goToCallbackUrl = () => {
    router.replace(callbackUrl || '/');
  };

  return {
    selectedTest,
    isSelected,
    questions,
    testIndex,
    handleSelectTest,
    handlePreviousTest,
    handleRetakeTest,
    isWrongAnswerVisible,
    isSuccessVisible,
    handleNextTest,
    toggleWrongAnswerVisibility,
    isDisabled,
    isLoadingSuitabilityTest,
    isEndTest,
    step,
    isStep,
    handleStep,
    goToCallbackUrl,
    isOnboarding,
  };
};
