import {
  Description,
  Dialog,
  DialogBackdrop,
  Dialog<PERSON>anel,
  DialogTitle,
  Transition,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import { Fragment } from 'react';

import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';
import { MySubscriptionCardSkeleton } from '@/entities/subscriptions/ui/MySubscriptionCardSkeleton';
import { SubscriptionApplyInfoItem } from '@/entities/subscriptions/ui/SubscriptionApplyItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { ScrollArea } from '@/shared/ui/shadcn/scroll-area';

import { fetchMySubscription } from '../api/fetchMySubscription';

interface MyApplySubscriptionDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  title?: string;
  description?: string;
  caption?: string;
  handleAction?: () => void;
  text?: string;
  isCancelButton?: boolean;
  subscriptionId: number;
  isLoading: boolean;
}

export const MyApplySubscriptionDialog = ({
  isOpen,
  handleOpen,
  handleAction,
  subscriptionId,
  isLoading,
}: MyApplySubscriptionDialogProps) => {
  const { data: mySubscription, isLoading: isSubscriptionLoading } = fetchMySubscription(
    subscriptionId.toString(),
  );

  const { CASHCOMMA, YYYYMMDD, DAYFROMNOW } = utilFormats();

  const applyQuantity = mySubscription?.applyQuantity || 0;
  const offeringPrice = mySubscription?.subscriptionInfo.offeringPrice || 0;
  const applyAmount = applyQuantity * offeringPrice;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog open={isOpen} onClose={handleOpen} className="relative z-50">
        <DialogBackdrop className="fixed inset-0 bg-black/80" />
        <div className="fixed inset-0 flex items-center justify-center px-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <DialogPanel className="w-full max-w-[540px] items-center rounded-[16px] border-none bg-white px-6 pb-[22px] data-[closed]:duration-500 data-[open]:duration-700 data-[open]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[open]:fade-in-0 sm:rounded-[30px]">
              <div className="flex justify-end">
                <button
                  onClick={handleOpen}
                  className="flex h-5 w-5 items-center justify-center rounded-full sm:h-9 sm:w-9 sm:p-0"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              <div className="mb-6 flex flex-col gap-2 px-3 sm:items-center sm:gap-4 sm:px-[55px] ml:px-[88px]">
                <DialogTitle className="text-20 whitespace-pre-line text-center">
                  나의 청약 신청 내역
                </DialogTitle>
                <Description className="text-center text-base">
                  {mySubscription?.subscriptionInfo.securities.securitiesName} <br />{' '}
                  <strong className="font-bold text-primary-500">
                    {DAYFROMNOW(mySubscription?.subscriptionInfo.allotAt)}일{' '}
                  </strong>
                  후 배정 예정이에요.
                </Description>
              </div>
              <ScrollArea className="max-h-[362px] overflow-y-auto">
                {isSubscriptionLoading ? (
                  <MySubscriptionCardSkeleton />
                ) : (
                  <div className="space-y-4 rounded-lg border border-gray-300 p-5 sm:p-8">
                    <SubscriptionApplyInfoItem
                      label="상품명"
                      value={mySubscription?.subscriptionInfo.securities.securitiesName || ''}
                      direction="column"
                    />
                    <SubscriptionApplyInfoItem
                      label="청약 신청 금액(수량)"
                      value={`${CASHCOMMA(applyAmount)}원 (${applyQuantity}좌)`}
                      direction="column"
                    />
                    <SubscriptionApplyInfoItem
                      label="청약 신청일"
                      value={YYYYMMDD(mySubscription?.subscriptionInfo.beginAt) || ''}
                      direction="column"
                    />
                    <SubscriptionApplyInfoItem
                      label="배정 예정일"
                      value={YYYYMMDD(mySubscription?.subscriptionInfo.allotAt) || ''}
                      direction="column"
                    />
                  </div>
                )}
                <ol className="mt-6 list-outside list-disc space-y-1 pl-4 text-sm leading-[170%]">
                  <li>모집 실패 시 청약 신청 금액은 예치금 계좌로 전체 반환됩니다.</li>
                  <li>
                    청약 신청 취소는 청약 종료일 전까지 가능하며, 취소 시 청약 신청 금액은 예치금
                    계좌로 반환됩니다.
                  </li>
                  <li>
                    청약 신청 및 취소하기는 청약 거래 약관에 따라 24시간 기준 10번으로 제한하며,
                    같은 상품 내 신청 및 취소를 20번 초과했을 경우 더 이상 해당 상품에 투자하실수
                    없습니다.
                  </li>
                </ol>
              </ScrollArea>

              <div className="mt-10 flex justify-center gap-2">
                {mySubscription?.subscriptionApplyStatus === SubscriptionApplyStatusList.APPLY && (
                  <SecondaryButton
                    type="button"
                    disabled={isLoading}
                    className="!h-[44px] w-full border-gray-300"
                    onClick={handleAction}
                    text="청약 신청 취소하기"
                  />
                )}
                <PrimaryButton
                  type="button"
                  className="!h-[44px] w-full font-semibold"
                  onClick={handleOpen}
                  text="닫기"
                />
              </div>
            </DialogPanel>
          </motion.div>
        </div>
      </Dialog>
    </Transition>
  );
};
