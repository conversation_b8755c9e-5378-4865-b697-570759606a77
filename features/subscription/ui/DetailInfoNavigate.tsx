import { Separator } from '@/shared/ui/shadcn/separator';

import { infoNavigateItems } from '../config';
import { useInfoNavigate } from '../model/useInfoNavigate';

export const DetailInfoNavigate = () => {
  const { handleClick, activeSection } = useInfoNavigate();

  return (
    <div className="absolute left-[-180px] top-0 hidden h-[310px] min-w-[150px] flex-col rounded-[10px] border border-gray-300 bg-white p-5 shadow-md ml:flex">
      <h4 className="font-semibold">투자 정보</h4>
      <Separator orientation="horizontal" className="mb-2 mt-4 bg-gray-200" />
      <ul className="text-sm leading-[150%]">
        {infoNavigateItems.map((item) => (
          <li
            key={item.id}
            onClick={() => handleClick(item.id)}
            className={`cursor-pointer py-2 transition-colors hover:text-primary-500 ${
              activeSection === item.id ? 'font-semibold text-primary-500' : 'text-gray-600'
            }`}
          >
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  );
};
