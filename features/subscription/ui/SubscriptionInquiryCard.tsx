import { TrashIcon } from '@heroicons/react/24/outline';

import { InquiryAnswerStatus, InquiryCategory, InquiryHistory } from '@/entities/inquiries/types';
import { InquiryAnswerStatusBadge } from '@/entities/inquiries/ui/InquiryAnswerStatusBadge';
import { InquiryCategoryBadge } from '@/entities/inquiries/ui/InquiryCategoryBadge';

import { utilFormats } from '@/shared/lib/utilformats';
import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SubscriptionInquiryCardProps {
  inquiry: InquiryHistory;
  handleOpenDeleteDialog: (inquiryId: string) => void;
}

export const SubscriptionInquiryCard = ({
  inquiry,
  handleOpenDeleteDialog,
}: SubscriptionInquiryCardProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div>
      <div className="flex items-center gap-4 border-b border-gray-200 p-6 text-sm">
        <InquiryAnswerStatusBadge
          className="min-w-[90px] basis-1/12"
          answerStatus={inquiry.answerStatus as InquiryAnswerStatus}
        />
        <InquiryCategoryBadge
          className="min-w-[60px] basis-1/12"
          category={inquiry.category as InquiryCategory}
        />
        <p className="flex-1 font-semibold">{inquiry.title}</p>
        <p className="min-w-[100px] basis-1/12 text-right">{YYYYMMDD(inquiry.createdAt)}</p>

        <div className="flex min-w-[60px] basis-1/12 justify-end">
          {inquiry.answerStatus === InquiryAnswerStatus.REQUEST && (
            <button onClick={() => handleOpenDeleteDialog(inquiry.id)}>
              <TrashIcon className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>
      {inquiry.answer && (
        <div className="flex gap-6 bg-blue-gray-00 p-6 text-sm">
          <div className="flex w-[90px] gap-2">
            <FallbackImage
              src="/icons/ic_include.png"
              alt="include"
              className="h-4 w-4"
              width={16}
              height={16}
            />
            <span className="h-6 rounded bg-gray-900 px-2 py-[3px] text-xs font-semibold text-white">
              답변
            </span>
          </div>
          <div className="flex-1">{inquiry.answer}</div>
          <p className="">{YYYYMMDD(inquiry.updatedAt)}</p>
        </div>
      )}
    </div>
  );
};
