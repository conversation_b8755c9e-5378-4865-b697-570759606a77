import React, { useEffect, useState } from 'react';

import { SubscriptionCmsDetail } from '@/entities/subscriptions/types';

import { ListResponse } from '@/shared/interface';

interface DetailMobileInfoTabProps {
  subscriptionCmsDetail?: ListResponse<SubscriptionCmsDetail>;
}

export const DetailMobileInfoTabs = ({ subscriptionCmsDetail }: DetailMobileInfoTabProps) => {
  const [activeTab, setActiveTab] = useState('summary');

  const tabs = [
    {
      visible: subscriptionCmsDetail?.data[0]?.summaryVisible,
      label: '투자상품 요약',
      id: 'summary',
    },
    {
      visible: subscriptionCmsDetail?.data[0]?.investmentPointVisible,
      label: '투자 포인트',
      id: 'points',
    },
    {
      visible: subscriptionCmsDetail?.data[0]?.assetDescribeVisible,
      label: '기초자산 소개',
      id: 'assets',
    },
    {
      visible: subscriptionCmsDetail?.data[0]?.profitStructureVisible,
      label: '수익구조',
      id: 'profit',
    },
    {
      visible: subscriptionCmsDetail?.data[0]?.operatorCompetitivenessVisible,
      label: '운용사업자 경쟁력',
      id: 'competitiveness',
    },
    {
      visible: subscriptionCmsDetail?.data[0]?.investmentConsiderationVisible,
      label: '투자 유의 사항',
      id: 'caution',
    },
  ].filter(({ visible }) => visible);

  console.log('tabs', tabs);

  // 스크롤 위치에 따른 활성 탭 감지
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 150; // 100px 오프셋 + 여유분

      // 각 섹션의 위치를 확인하여 현재 보이는 섹션 찾기
      for (let i = tabs.length - 1; i >= 0; i--) {
        const element = document.getElementById(tabs[i].id);
        if (element) {
          const elementTop = element.offsetTop;
          if (scrollPosition >= elementTop) {
            setActiveTab(tabs[i].id);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 초기 실행

    return () => window.removeEventListener('scroll', handleScroll);
  }, [tabs]);

  return (
    <div className="sticky top-[44px] z-50 bg-white sm:hidden">
      <div className="flex gap-2 overflow-x-auto bg-white py-3">
        {tabs.map(({ label, id }) => (
          <DetailMobileInfoTab key={id} label={label} id={id} isActive={activeTab === id} />
        ))}
      </div>
    </div>
  );
};

const DetailMobileInfoTab = ({
  label,
  id,
  isActive,
}: {
  label: string;
  id: string;
  isActive: boolean;
}) => {
  const handleClick = () => {
    const element = document.getElementById(id);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - 170;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'instant',
      });
    }
  };

  return (
    <button
      key={label}
      onClick={handleClick}
      className={`flex-shrink-0 rounded-[40px] px-[18px] py-[12px] text-sm font-semibold transition-colors ${
        isActive ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
    >
      {label}
    </button>
  );
};
