import React from 'react';

import { SubscriptionCmsDetail } from '@/entities/subscriptions/types';

import { ListResponse } from '@/shared/interface';

interface DetailMobileInfoTabProps {
  subscriptionCmsDetail?: ListResponse<SubscriptionCmsDetail>;
}

export const DetailMobileInfoTabs = ({ subscriptionCmsDetail }: DetailMobileInfoTabProps) => {
  return (
    <div className="sticky top-[44px] z-50 bg-white sm:hidden">
      <div className="flex gap-2 overflow-x-auto bg-white py-3">
        {[
          {
            visible: subscriptionCmsDetail?.data[0]?.summaryVisible,
            label: '투자상품 요약',
            id: 'summary',
          },
          {
            visible: subscriptionCmsDetail?.data[0]?.investmentPointVisible,
            label: '투자 포인트',
            id: 'points',
          },
          {
            visible: subscriptionCmsDetail?.data[0]?.assetDescribeVisible,
            label: '기초자산 소개',
            id: 'assets',
          },
          {
            visible: subscriptionCmsDetail?.data[0]?.profitStructureVisible,
            label: '수익구조',
            id: 'profit',
          },
          {
            visible: subscriptionCmsDetail?.data[0]?.operatorCompetitivenessVisible,
            label: '운용사업자 경쟁력',
            id: 'competitiveness',
          },
          {
            visible: subscriptionCmsDetail?.data[0]?.investmentConsiderationVisible,
            label: '투자 유의 사항',
            id: 'caution',
          },
        ]
          .filter(({ visible }) => visible)
          .map(({ label, id }) => (
            <DetailMobileInfoTab key={id} label={label} id={id} isActive={false} />
          ))}
      </div>
    </div>
  );
};

const DetailMobileInfoTab = ({
  label,
  id,
  isActive,
}: {
  label: string;
  id: string;
  isActive: boolean;
}) => {
  const handleClick = () => {
    const element = document.getElementById(id);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - 170;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'instant',
      });
    }
  };

  return (
    <button
      key={label}
      onClick={handleClick}
      className="flex-shrink-0 rounded-[40px] bg-gray-100 px-[18px] py-[12px] text-sm font-semibold text-gray-700"
    >
      {label}
    </button>
  );
};
