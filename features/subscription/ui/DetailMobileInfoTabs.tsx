import React, { useEffect, useState } from 'react';

import { SubscriptionCmsDetail } from '@/entities/subscriptions/types';

import { ListResponse } from '@/shared/interface';

import { useDetailMobileInfoTabs } from '../model/useDetailMobileInfoTabs';

interface DetailMobileInfoTabProps {
  subscriptionCmsDetail?: ListResponse<SubscriptionCmsDetail>;
}

export const DetailMobileInfoTabs = ({ subscriptionCmsDetail }: DetailMobileInfoTabProps) => {
  const { activeTab, tabs } = useDetailMobileInfoTabs(subscriptionCmsDetail);

  return (
    <div className="sticky top-[45px] z-50 bg-white sm:hidden">
      <div className="flex gap-2 overflow-x-auto bg-white py-3">
        {tabs.map(({ label, id }) => (
          <DetailMobileInfoTab key={id} label={label} id={id} isActive={activeTab === id} />
        ))}
      </div>
    </div>
  );
};

const DetailMobileInfoTab = ({
  label,
  id,
  isActive,
}: {
  label: string;
  id: string;
  isActive: boolean;
}) => {
  const handleClick = () => {
    const element = document.getElementById(id);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - 200;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'instant',
      });
    }
  };

  return (
    <button
      key={label}
      onClick={handleClick}
      className={`flex-shrink-0 rounded-[40px] px-[18px] py-[12px] text-sm font-semibold transition-colors ${
        isActive ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
    >
      {label}
    </button>
  );
};
