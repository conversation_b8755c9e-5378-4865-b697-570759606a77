import { Dialog, DialogBackdrop, DialogPanel, DialogTitle, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import { Fragment, useState } from 'react';

import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Checkbox } from '@/shared/ui/shadcn/checkbox';
import { Label } from '@/shared/ui/shadcn/label';
import { ScrollArea } from '@/shared/ui/shadcn/scroll-area';

interface SubscriptionApplyNoticeDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  handleAction?: () => void;
}

export const SubscriptionApplyNoticeDialog = ({
  isOpen,
  handleOpen,
  handleAction,
}: SubscriptionApplyNoticeDialogProps) => {
  const [isChecked, setIsChecked] = useState(false);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog open={isOpen} onClose={handleOpen} className="relative z-50">
        <DialogBackdrop className="fixed inset-0 bg-black/80" />
        <div className="fixed inset-0 flex items-center justify-center px-6 sm:px-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <DialogPanel className="relative w-full max-w-[540px] items-center rounded-[16px] border-none bg-white px-6 pb-[22px] pt-8 data-[closed]:duration-500 data-[open]:duration-700 data-[open]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[open]:fade-in-0">
              <div className="absolute right-[10px] top-[10px]">
                <button
                  onClick={handleOpen}
                  className="flex h-7 w-7 items-center justify-center rounded-full sm:h-9 sm:w-9 sm:p-0"
                >
                  <XMarkIcon className="h-5 w-5 sm:h-6 sm:w-6" />
                </button>
              </div>
              <div className="mb-8 flex flex-col gap-2 px-3 sm:items-center sm:gap-4 sm:px-[55px] ml:px-[88px]">
                <DialogTitle className="text-20 whitespace-pre-line text-center">
                  투자 안내사항
                </DialogTitle>
              </div>
              <ScrollArea className="mb-8 max-h-[422px] overflow-y-auto leading-[170%]">
                <h4 className="font-semibold">투자 전 꼭 확인하세요.</h4>

                <ol className="mt-6 list-outside list-decimal space-y-1 pl-4 text-sm leading-[170%]">
                  <li>
                    뉴밋은 온라인 소액 투자 중개업자로서 다양한 투자에 대한 '중개' 역할을 합니다.
                    증권이 발행되면 뉴밋에서는 더 이상 투자 상품에 관여하지 않습니다.
                  </li>
                  <li>
                    해당 투자는 투자 원금의 전부 또는 손실 위험이 있는 고위험 투자입니다. 꼼꼼하게
                    모든 내욜을 확인 후 신중하게 투자에 참여하셔야 합니다.
                  </li>
                  <li>투자 전 투자위험 고지 전문과 투자 유의 사항을 꼭 확인하시길 바랍니다.</li>
                </ol>
              </ScrollArea>

              <div className="mb-10 flex items-center gap-2 rounded-lg border border-gray-300 bg-gray-50 p-6">
                <Checkbox
                  checked={isChecked}
                  onCheckedChange={(checked) =>
                    setIsChecked(checked === 'indeterminate' ? false : checked)
                  }
                  className="h-5 w-5 bg-white shadow-none"
                />
                <Label className="text-sm">
                  모든 내용을 확인하고 충분히 이해 했습니다.{' '}
                  <span className="text-primary-500">(필수)</span>
                </Label>
              </div>

              <div className="flex justify-center gap-2">
                <PrimaryButton
                  type="button"
                  disabled={!isChecked}
                  className="!h-[44px] w-full font-semibold"
                  onClick={handleAction}
                  text="확인완료"
                />
              </div>
            </DialogPanel>
          </motion.div>
        </div>
      </Dialog>
    </Transition>
  );
};
