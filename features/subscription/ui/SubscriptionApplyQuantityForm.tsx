import React, { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { fetchInvestorLimit } from '@/features/investor/api/fetchInvestorLimit';

import { Subscription } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/shared/ui/shadcn/form';
import { Input } from '@/shared/ui/shadcn/input';
import { Separator } from '@/shared/ui/shadcn/separator';

import { SubscriptionApplyFormData } from '../lib/SubscriptionApplySchema';

interface SubscriptionApplyQuantityFormProps {
  form: UseFormReturn<SubscriptionApplyFormData>;
  subscription?: Subscription;
  isProfessionalUser: boolean;
}

export const SubscriptionApplyQuantityForm = ({
  form,
  subscription,
  isProfessionalUser,
}: SubscriptionApplyQuantityFormProps) => {
  const { data: investorLimit } = fetchInvestorLimit(subscription?.issuer.id.toString());

  const limitAmount = investorLimit?.issuerInvestLimitAmount || 0;

  const { control, formState, watch, setValue, setError, clearErrors } = form;
  const offeringPrice = subscription?.offeringPrice || 0;
  const isLimitOver = watch('quantity') * offeringPrice > limitAmount;
  const totalAmount = watch('quantity') * offeringPrice;
  const { CASHCOMMA } = utilFormats();

  useEffect(() => {
    if (isProfessionalUser) {
      clearErrors('quantity');
      return;
    }

    if (isLimitOver) {
      setError('quantity', { message: '청약 가능 수량이 아닙니다. 다시 확인해 주세요.' });
    } else {
      clearErrors('quantity');
    }
  }, [isLimitOver]);

  return (
    <div className="space-y-5 px-6 sm:space-y-3 sm:px-8 ml:px-0">
      <h4 className="sm:text-20 text-base font-semibold">청약 수량</h4>
      <div className="rounded-lg border-gray-300 sm:border">
        <div className="flex items-center sm:p-8">
          <div className="flex w-full items-center justify-between">
            <span className="hidden font-semibold sm:block">청약 수량</span>
            <div className="w-full sm:w-auto">
              <Form {...form}>
                <div className="relative w-full sm:w-[360px]">
                  <FormField
                    control={control}
                    name="quantity"
                    render={() => (
                      <FormItem className="w-full space-y-3">
                        <FormControl>
                          <Input
                            value={watch('quantity')}
                            onChange={(e) => setValue('quantity', Number(e.target.value))}
                            placeholder="청약 수량을 입력해 주세요."
                            type="number"
                            className={`pr-16 ${formState.errors['quantity'] && 'border-2 border-red-500'}`}
                          />
                        </FormControl>
                        <FormMessage className="text-xs font-semibold sm:text-sm" />
                      </FormItem>
                    )}
                  />
                  <div
                    className={`absolute right-4 ${formState.errors['quantity'] ? 'top-[-16px]' : 'top-1/2 -translate-y-1/2'} flex h-full w-10 items-center justify-center gap-4`}
                  >
                    <Separator orientation="vertical" className="h-6 bg-gray-300" />
                    <p className="text-gray-700">좌</p>
                  </div>
                </div>
              </Form>
            </div>
          </div>
        </div>
        <Separator orientation="horizontal" className="my-7 bg-gray-200 sm:hidden" />
        <div className="rounded-b-lg sm:bg-gray-50 sm:px-8 sm:py-6">
          <div className="flex w-full items-center justify-between">
            <span className="font-semibold">총 청약 증거금</span>
            <span className="sm:text-24 text-lg font-bold text-primary-500 sm:text-gray-900">
              {CASHCOMMA(totalAmount)}원
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
