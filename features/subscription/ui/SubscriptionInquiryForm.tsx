import React from 'react';

import { InquiryCategorySearchOptions } from '@/entities/inquiries/config';
import { InquiryCategory } from '@/entities/inquiries/types';
import { Subscription } from '@/entities/subscriptions/types';

import { CommonSelect } from '@/shared/ui/CommonSelect';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';
import { TextareaField } from '@/shared/ui/TextareaField';

import { useSubscriptionInquiry } from '../model/useSubscriptionInquiry';

interface SubscriptionInquiryFormProps {
  securitiesId: string;
  handleClose: () => void;
  subscription?: Subscription;
}

export const SubscriptionInquiryForm = ({
  securitiesId,
  handleClose,
  subscription,
}: SubscriptionInquiryFormProps) => {
  const { form, onSubmit } = useSubscriptionInquiry({ securitiesId, handleClose, subscription });
  const { control, handleSubmit, watch, formState } = form;

  return (
    <form className="my-6 space-y-8 sm:my-12 sm:space-y-10">
      <Form {...form}>
        <FormField
          control={control}
          name="category"
          render={() => (
            <FormItem className="w-full sm:w-1/2">
              <FormLabel className="text-sm font-semibold sm:text-base">
                문의 분류 <strong className="font-semibold text-primary-500"> (필수)</strong>
              </FormLabel>
              <FormControl>
                <CommonSelect
                  value={watch('category')}
                  onChange={(value) => form.setValue('category', value as InquiryCategory)}
                  options={InquiryCategorySearchOptions}
                  placeholder="문의 분류를 선택해 주세요."
                />
              </FormControl>
            </FormItem>
          )}
        />
        <InputField
          form={form}
          label="제목"
          requiredText
          name="title"
          placeholder="제목을 입력해 주세요."
        />
        <TextareaField
          form={form}
          label="내용"
          requiredText
          name="content"
          placeholder="문의 내용을 입력해 주세요."
        />
      </Form>
      <PrimaryButton
        type="button"
        disabled={!formState.isValid}
        onClick={handleSubmit(onSubmit)}
        text="등록하기"
        className="mt-2 w-full sm:w-40"
      />
    </form>
  );
};
