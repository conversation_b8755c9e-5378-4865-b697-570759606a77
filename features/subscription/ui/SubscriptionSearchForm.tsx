'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { SubscriptionSortType } from '@/entities/subscriptions/types';

import { CommonSelect } from '@/shared/ui/CommonSelect';

const CategoryTabs = [
  {
    label: '최신순',
    value: 'LATEST',
  },
  {
    label: '마감임박',
    value: 'DEADLINE',
  },
  {
    label: '모집금액순',
    value: 'APPLY_AMOUNT',
  },
];

export const SubscriptionSearchForm = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [sortType, setSortType] = useState<SubscriptionSortType>(
    (searchParams.get('sortType') as SubscriptionSortType) || SubscriptionSortType.LATEST,
  );

  const handleSortType = (value: SubscriptionSortType) => {
    setSortType(value);
  };

  useEffect(() => {
    const params = new URLSearchParams(searchParams);

    if (!sortType || sortType === SubscriptionSortType.LATEST) {
      params.delete('sortType');
    } else {
      params.set('sortType', sortType);
    }

    // history API를 사용하여 URL만 변경
    window.history.replaceState({}, '', `${pathname}?${params.toString()}`);
  }, [sortType]);

  return (
    <form className="container mb-20 mt-8 flex h-12 flex-col justify-end sm:my-20 sm:flex-row">
      {/* <div className="mb-4 flex space-x-2 sm:mb-6 ml:mb-0">
        {CategoryTabs.map((tab) => (
          <CommonTabButton
            key={tab.value}
            label={tab.label}
            value={tab.value as string}
            isSelected={selectedCategory === tab.value}
            handleAction={() => setSelectedCategory(tab.value)}
          />
        ))}
      </div> */}
      <div className="flex flex-col gap-2 sm:flex-row">
        <CommonSelect
          placeholder="전체"
          value={sortType}
          onChange={(value) => handleSortType(value as SubscriptionSortType)}
          options={CategoryTabs}
        />
      </div>
    </form>
  );
};
