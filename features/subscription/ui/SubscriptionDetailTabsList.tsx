import { useEffect, useRef } from 'react';

import { Subscription, SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

interface SubscriptionDetailTabsListProps {
  goToAttachFiles: () => void;
  subscription: Subscription;
}

export const SubscriptionDetailTabsList = ({
  goToAttachFiles,
  subscription,
}: SubscriptionDetailTabsListProps) => {
  const isEnded = !(
    subscription.bizStatus === SubscriptionBizStatus.SUB_WAIT ||
    subscription.bizStatus === SubscriptionBizStatus.SUB_WIP
  );

  const tabsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const header = document.querySelector('header');
    const tabsElement = tabsRef.current;

    if (!header || !tabsElement) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // 탭이 화면에 보이면 헤더 숨기기
          header.style.visibility = 'hidden';
          header.style.opacity = '0';
          header.style.transition = 'opacity 0.3s ease';
        } else {
          // 탭이 화면에서 사라지면 헤더 보이기
          header.style.visibility = 'visible';
          header.style.opacity = '1';
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px 0px 0px',
      },
    );

    observer.observe(tabsElement);

    return () => {
      observer.disconnect();
      // 컴포넌트 언마운트 시 헤더 복원
      header.style.visibility = 'visible';
      header.style.opacity = '1';
    };
  }, []);

  return (
    <div className="sticky top-0 z-50 border-b border-gray-300 bg-white">
      <TabsList
        ref={tabsRef}
        onClick={() => {
          const tabsContainer = document.querySelector('[role="tablist"]');

          console.log('tabsContainer', tabsContainer);
          if (tabsContainer) {
            tabsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }}
        className="mx-auto flex h-[44px] w-full max-w-[800px] gap-[26px] overflow-x-auto overflow-y-hidden px-6 shadow-sm sm:h-[50px] sm:px-8 ml:max-w-screen-contents ml:px-0"
      >
        <DetailTabItem value="info" label="투자정보" />
        <TabsTrigger
          value="info"
          onClick={goToAttachFiles}
          className="sm:text-18 h-[44px] flex-1 rounded-none px-0 text-base font-semibold text-gray-300 duration-300 data-[state=active]:border-black data-[state=active]:text-gray-900 sm:h-[50px]"
        >
          첨부자료
        </TabsTrigger>
        <DetailTabItem value="inquiry" label="문의하기" />
        <DetailTabItem value="news" label="새소식" />
        {isEnded && <DetailTabItem value="disclosures" label="공시" />}
      </TabsList>
    </div>
  );
};

const DetailTabItem = ({ value, label }: { value: string; label: string }) => {
  return (
    <TabsTrigger
      value={value}
      className="sm:text-18 h-[44px] flex-1 rounded-none px-0 text-base font-semibold text-gray-300 transition-all duration-300 data-[state=active]:border-b-[3px] data-[state=active]:border-black data-[state=active]:pt-[8px] data-[state=active]:text-gray-900 sm:h-[50px]"
    >
      {label}
    </TabsTrigger>
  );
};
