import { TrashIcon } from '@heroicons/react/24/outline';
import { PaperClipIcon, PlusIcon } from '@heroicons/react/24/solid';
import { UseFormReturn } from 'react-hook-form';

import { CheckboxField } from '@/shared/ui/CheckboxField';
import { FileInfoTooltop } from '@/shared/ui/FileInfoTooltop';
import { FileUploader } from '@/shared/ui/FileUploader';
import { InputField } from '@/shared/ui/InputField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/shared/ui/shadcn/form';
import { TextareaField } from '@/shared/ui/TextareaField';

import { SubscriptionRequestFormData } from '../lib/SubscriptionRequestSchema';

interface SubscriptionRequestFormProps {
  form: UseFormReturn<SubscriptionRequestFormData>;
  ref: React.RefObject<HTMLInputElement | null>;
  onSubmit: (data: SubscriptionRequestFormData) => void;
  handleFileUpload: () => void;
  handleUploadedFiles: (files: File[]) => void;
  handleDeleteFile: (file: File) => void;
}

export const SubscriptionRequestForm = ({
  form,
  ref,
  onSubmit,
  handleFileUpload,
  handleUploadedFiles,
  handleDeleteFile,
}: SubscriptionRequestFormProps) => {
  const { control, watch, setValue, handleSubmit, formState } = form;

  return (
    <Form {...form}>
      <form className="mt-8 space-y-10 pb-16 sm:mt-12 sm:pb-0 ml:mt-[120px]">
        <InputField
          form={form}
          label="기업명"
          requiredText
          name="companyName"
          placeholder="기업명을 입력해 주세요."
        />
        <InputField
          form={form}
          label="신청인"
          requiredText
          name="managerName"
          placeholder="이름을 입력해 주세요."
        />
        <InputField
          form={form}
          label="이메일 주소"
          requiredText
          type="email"
          name="managerEmail"
          placeholder="이메일 주소를 입력해 주세요."
        />
        <TextareaField
          form={form}
          label="투자 의뢰 상품 소개"
          requiredText
          name="introduction"
          placeholder="투자 의뢰를 원하는 상품에 대하여 소개해주세요. (최대 500자)"
        />
        <FormField
          control={control}
          name="attachFiles"
          render={() => (
            <FormItem>
              <div className="flex items-center gap-1">
                <FormLabel className="text-sm font-semibold sm:text-base">
                  파일 등록 <span className="text-gray-500">(선택)</span>
                </FormLabel>
                <FileInfoTooltop />
              </div>
              <FormControl>
                <div className="flex flex-col gap-2">
                  {watch('attachFiles')?.map((file) => (
                    <div
                      className="flex justify-between rounded-lg border border-gray-300 px-4 py-3"
                      key={file.name}
                    >
                      <div className="flex items-center gap-2">
                        <PaperClipIcon className="h-5 w-5 text-primary-500" />
                        {file.name}
                      </div>
                      <button type="button" onClick={() => handleDeleteFile(file)}>
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    onClick={handleFileUpload}
                    className="h-[44px] w-full rounded-lg border border-gray-300 text-gray-500 shadow-none sm:h-12"
                  >
                    <PlusIcon className="h-5 w-5" />
                    추가할 파일 선택
                  </Button>
                </div>
              </FormControl>
            </FormItem>
          )}
        />
        <CheckboxField
          label="개인정보 수집 및 이용 동의"
          required
          value="terms"
          checked={watch('terms') as boolean}
          onCheckedChange={(e: boolean) => {
            setValue('terms', e, {
              shouldValidate: true,
            });
          }}
        />
        <FileUploader ref={ref} uploadedFiles={handleUploadedFiles} multiple />
      </form>
      <div className="fixed bottom-0 left-0 mt-10 w-full bg-white px-6 py-2 sm:static">
        <PrimaryButton
          type="button"
          disabled={!formState.isValid}
          onClick={handleSubmit(onSubmit)}
          text="제출하기"
          className="!h-12 w-full text-base sm:w-40"
        />
      </div>
    </Form>
  );
};
