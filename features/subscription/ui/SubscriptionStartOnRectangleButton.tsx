import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/24/solid';

import { Button } from '@/shared/ui/shadcn/button';

interface SubscriptionStartOnRectangleButtonProps {
  handleClick: () => void;
}

export const SubscriptionStartOnRectangleButton = ({
  handleClick,
}: SubscriptionStartOnRectangleButtonProps) => {
  return (
    <Button onClick={handleClick} className="h-12 w-12 rounded-lg border border-gray-300">
      <ArrowRightStartOnRectangleIcon className="h-6 w-6 text-gray-500" />
    </Button>
  );
};
