import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/24/solid';
import { useSearchParams } from 'next/navigation';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { Button } from '@/shared/ui/shadcn/button';

export const SubscriptionStartOnRectangleButton = () => {
  const searchParams = useSearchParams();
  const subscriptionId = searchParams.get('subscriptionId');

  const { routerPush, routerBack } = useWebViewRouter();

  const handleClick = () => {
    if (subscriptionId) {
      routerPush(`/subscriptions/${subscriptionId}`);
    } else {
      routerBack();
    }
  };

  return (
    <Button onClick={handleClick} className="h-12 w-12 rounded-lg border border-gray-300">
      <ArrowRightStartOnRectangleIcon className="h-6 w-6 text-gray-500" />
    </Button>
  );
};
