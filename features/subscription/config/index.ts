import { OcrSteps } from '@/features/ocr/type';

import { DepositSteps } from '@/entities/assets/types';
import { PropensitySteps } from '@/entities/investor/types';
import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';

export const ocrSideOptions = [
  { label: '신분증 업로드', step: OcrSteps.UPLOAD },
  { label: '진위여부 확인', step: OcrSteps.VERIFY },
  { label: '신분증 인증 완료', step: OcrSteps.COMPLETE },
];

export const accountSideOptions = [
  { label: '예치금 계좌 개설 발급 동의', step: DepositSteps.TERMS },
  { label: '출금계좌 등록', step: DepositSteps.FORM },
  { label: '출금계좌 인증 완료', step: DepositSteps.CONFIRM },
  { label: '예치금 계좌 개설 발급 완료', step: DepositSteps.COMPLETE },
];

export const propensitySideOptions = [
  { label: '투자 성향 진단 시작', step: PropensitySteps.TYPE },
  { label: '투자 성향 진단 진행', step: PropensitySteps.TERMS },
  { label: '투자 성향 진단 종료', step: PropensitySteps.RESULT },
];

export const suitabiltySideOptions = [
  { label: '투자 적합성 테스트 시작', step: 1 },
  { label: '투자 적합성 테스트 진행', step: 2 },
  { label: '투자 적합성 테스트 종료', step: 3 },
];

export const infoNavigateItems = [
  { id: 'summary', label: '투자상품 요약' },
  { id: 'points', label: '투자 포인트' },
  { id: 'assets', label: '기초자산 소개' },
  { id: 'profit', label: '수익 구조' },
  { id: 'competitiveness', label: '운용 사업자 경쟁력' },
  { id: 'caution', label: '투자 유의 사항' },
] as const;

export const SubscriptionHistoryCategoryTabs: {
  label: string;
  value: SubscriptionApplyStatusList | 'ALL';
}[] = [
  {
    label: '전체',
    value: 'ALL',
  },
  {
    label: '배정 완료',
    value: SubscriptionApplyStatusList.ALLOT,
  },
  {
    label: '미배정',
    value: SubscriptionApplyStatusList.NOT_ALLOT,
  },
  {
    label: '청약 철회',
    value: SubscriptionApplyStatusList.WITHDRAW,
  },
  {
    label: '공모 미달',
    value: SubscriptionApplyStatusList.REFUND_WAIT,
  },
];

export const SubscriptionCurrentCategoryTabs: {
  label: string;
  value: SubscriptionApplyStatusList | 'ALL';
}[] = [
  {
    label: '전체',
    value: 'ALL',
  },
  {
    label: '청약신청',
    value: SubscriptionApplyStatusList.APPLY,
  },
  {
    label: '배정대상',
    value: SubscriptionApplyStatusList.ALLOT_TARGET,
  },
  {
    label: '배정중',
    value: SubscriptionApplyStatusList.WAIT,
  },
];

export const SubscriptionAssetCategoryTabs: {
  label: string;
  value: SubscriptionApplyStatusList | 'ALL';
}[] = [
  {
    label: '전체',
    value: 'ALL',
  },
  {
    label: '배정완료',
    value: SubscriptionApplyStatusList.ALLOT,
  },
];
