import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { SubscriptionsRequest, SubscriptionsResponse } from '@/entities/subscriptions/interface';

export const fetchSubscriptions = ({
  filters,
  subscriptions,
}: {
  filters: SubscriptionsRequest;
  subscriptions?: SubscriptionsResponse;
}) => {
  return useQuery({
    ...queries.subscriptions.list(filters),
    initialData: subscriptions,
    placeholderData: keepPreviousData,
  });
};
