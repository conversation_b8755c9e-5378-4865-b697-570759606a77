import { useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

export const useFetchCheckSubscriptionAllotment = ({
  subscriptionInfoId,
  isEnabled,
}: {
  subscriptionInfoId?: string;
  isEnabled: boolean;
}) => {
  return useQuery({
    ...queries.subscriptions.checkSubscriptionAllotment(subscriptionInfoId as string),
    enabled: !!subscriptionInfoId && isEnabled,
  });
};
