import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { MySubscriptionsRequest } from '@/entities/subscriptions/interface';

export const fetchMySubscriptions = (filters: MySubscriptionsRequest, enabled?: boolean) => {
  return useQuery({
    ...queries.subscriptions.myList(filters),
    enabled,
    placeholderData: keepPreviousData,
  });
};
