'use client';

import { z } from 'zod';

import { InquiryCategory } from '@/entities/inquiries/types';

export const subscriptionInquiryFormSchema = z.object({
  title: z.string().min(1, '제목을 입력해주세요.').max(20, '제목은 최대 20자입니다.'),
  content: z.string().min(1, '내용을 입력해주세요.').max(500, '내용은 최대 500자입니다.'),
  category: z
    .nativeEnum(InquiryCategory)
    .refine((val) => val !== undefined, { message: '문의분류 선택은 필수입니다.' }),
});

export type SubscriptionInquiryFormData = z.infer<typeof subscriptionInquiryFormSchema>;
