import { AllocationTypeLabel } from '@/entities/subscriptions/types';

export const getHighestAllocationTypeWithLabel = (allocation: {
  fifo_percent: number;
  proportional_percent: number;
  equal_percent: number;
  issuer_percent: number;
}) => {
  const maxKey = Object.entries(allocation).reduce(
    (max, [key, value]) => {
      return value > max.value ? { key, value } : max;
    },
    { key: '', value: -1 },
  ).key;

  return {
    key: max<PERSON><PERSON>,
    label: AllocationTypeLabel[maxKey as keyof typeof AllocationTypeLabel],
    value: allocation[maxKey as keyof typeof allocation],
  };
};
