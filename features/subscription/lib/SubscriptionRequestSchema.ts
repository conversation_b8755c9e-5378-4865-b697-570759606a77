'use client';

import { z } from 'zod';

export const subscriptionRequestFormSchema = z.object({
  companyName: z.string().min(1, '회사명을 입력해주세요.').max(20, '회사명은 최대 20자입니다.'),
  managerName: z.string().min(1, '담당자명을 입력해주세요.').max(20, '담당자명은 최대 20자입니다.'),
  managerEmail: z
    .string()
    .min(1, '담당자 이메일을 입력해주세요.')
    .max(20, '담당자 이메일은 최대 20자입니다.'),
  introduction: z
    .string()
    .min(1, '투자 의뢰 내용을 입력해주세요.')
    .max(500, '투자 의뢰 내용은 최대 500자입니다.'),
  attachFiles: z.array(z.instanceof(File)).optional(),
  terms: z.boolean().refine((data) => data, {
    message: '개인정보 수집 및 이용 동의를 체크해주세요.',
  }),
});

export type SubscriptionRequestFormData = z.infer<typeof subscriptionRequestFormSchema>;
