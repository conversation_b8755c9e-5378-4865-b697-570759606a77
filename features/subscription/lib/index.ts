import { Subscription } from '@/entities/subscriptions/types';

export const isOpen = (subscription: Subscription) => {
  const beginDate = new Date(subscription.beginAt);
  const month = beginDate.getMonth() + 1;
  return {
    isOpen: beginDate < new Date(),
    openText: `${month}월 오픈 예정`,
  };
};

export const isEnded = (subscription: Subscription) => {
  const endDate = new Date(subscription.endAt);
  return endDate < new Date();
};
