import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import { queries } from '@/features/lib/queries';

import { withdrawSubscription } from '@/entities/subscriptions/api/withdrawSubscription';

import { useToast } from '@/shared/model/useToast';

export const useWithdrawSubscription = () => {
  const queryClient = useQueryClient();
  const { successToast, errorToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleWithdraw = async (subscriptionApplyId: number) => {
    setIsLoading(true);
    try {
      await withdrawSubscription(subscriptionApplyId.toString());

      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queries.subscriptions._def }),
        queryClient.invalidateQueries({ queryKey: queries.assets._def }),
        queryClient.invalidateQueries({
          queryKey: queries.user._def,
        }),
        queryClient.invalidateQueries({
          queryKey: queries.investor._def,
        }),
      ]);
      successToast({
        title: '청약 신청이 취소되었습니다.',
      });
    } catch (error) {
      errorToast({
        title: '청약 신청 취소에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { handleWithdraw, isWithdrawLoading: isLoading };
};
