'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef } from 'react';
import { useForm } from 'react-hook-form';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { DepositChangeSteps } from '@/entities/assets/types';
import { requestSubscription } from '@/entities/subscriptions/api/requestSubscription';

import { useStep } from '@/shared/model/useStep';
import { useToast } from '@/shared/model/useToast';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import {
  SubscriptionRequestFormData,
  subscriptionRequestFormSchema,
} from '../lib/SubscriptionRequestSchema';

export const useSubscriptionRequest = () => {
  const { successToast, errorToast } = useToast();
  const { step, handleStep, isStep } = useStep<DepositChangeSteps>(DepositChangeSteps.FORM);
  const ref = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const { routerPush } = useWebViewRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');

  const form = useForm<SubscriptionRequestFormData>({
    resolver: zodResolver(subscriptionRequestFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const goToCallbackUrl = () => {
    router.replace(callbackUrl || '/');
  };

  const { user, userId } = useFetchUser();

  const handleFileUpload = () => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const handleUploadedFiles = (files: File[]) => {
    form.setValue('attachFiles', [...(form.watch('attachFiles') || []), ...files]);
  };

  const handleDeleteFile = (file: File) => {
    form.setValue(
      'attachFiles',
      form.watch('attachFiles')?.filter((f) => f.name !== file.name) || [],
    );
  };

  const onSubmit = async (data: SubscriptionRequestFormData) => {
    if (!userId) {
      errorToast({
        title: '로그인 후 이용해주세요.',
      });

      routerPush('/sign-in');
      return;
    }

    try {
      await requestSubscription({
        status: 'published',
        companyName: data.companyName,
        applicantName: data.managerName,
        email: data.managerEmail,
        investmentDescription: data.introduction,
        attachFiles: data.attachFiles || [],
      });
      successToast({
        title: '투자 의뢰 신청 성공',
      });

      handleStep(DepositChangeSteps.COMPLETE);
    } catch (error) {
      errorToast({
        title: '문의 등록 실패',
      });
    }
  };

  return {
    form,
    ref,
    onSubmit,
    handleFileUpload,
    handleUploadedFiles,
    handleDeleteFile,
    step,
    isStep,
    goToCallbackUrl,
  };
};
