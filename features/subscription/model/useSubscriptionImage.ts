import { useMemo } from 'react';

import { fetchSubscriptionCmsDetail } from '../api/fetchSubscriptionCmsDetail';

export const useSubscriptionMainImage = (securitiesId: string) => {
  const { data: subscriptionCmsDetail, isLoading } = fetchSubscriptionCmsDetail(securitiesId);

  const mainImage = useMemo(() => {
    return subscriptionCmsDetail?.data[0]?.imageList?.find((image) => image.isMain);
  }, [subscriptionCmsDetail]);

  const imageList = useMemo(() => {
    return subscriptionCmsDetail?.data[0]?.imageList?.filter((image) => !image.isMain);
  }, [subscriptionCmsDetail]);

  return {
    mainImage,
    imageList,
    isLoading,
    subscriptionCmsDetail,
  };
};
