import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import {
  SubscriptionApplyFormData,
  subscriptionApplySchema,
} from '@/features/subscription/lib/SubscriptionApplySchema';

export const useSubscriptionApplyForm = () => {
  const form = useForm<SubscriptionApplyFormData>({
    resolver: zodResolver(subscriptionApplySchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    defaultValues: {
      quantity: 0,
      isVerified: false,
      transactionId: '',
    },
  });

  const handleVerify = (transactionId: string) => {
    form.setValue('isVerified', true);
    form.setValue('transactionId', transactionId);
  };

  return {
    form,
    handleVerify,
  };
};
