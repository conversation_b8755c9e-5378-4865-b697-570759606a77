import { useEffect, useMemo, useState } from 'react';

import { SubscriptionCmsDetail } from '@/entities/subscriptions/types';

import { ListResponse } from '@/shared/interface';

export const useDetailMobileInfoTabs = (
  subscriptionCmsDetail?: ListResponse<SubscriptionCmsDetail>,
) => {
  const [activeTab, setActiveTab] = useState('summary');

  const tabs = useMemo(() => {
    return [
      {
        visible: subscriptionCmsDetail?.data[0]?.summaryVisible,
        label: '투자상품 요약',
        id: 'summary',
      },
      {
        visible: subscriptionCmsDetail?.data[0]?.investmentPointVisible,
        label: '투자 포인트',
        id: 'points',
      },
      {
        visible: subscriptionCmsDetail?.data[0]?.assetDescribeVisible,
        label: '기초자산 소개',
        id: 'assets',
      },
      {
        visible: subscriptionCmsDetail?.data[0]?.profitStructureVisible,
        label: '수익구조',
        id: 'profit',
      },
      {
        visible: subscriptionCmsDetail?.data[0]?.operatorCompetitivenessVisible,
        label: '운용사업자 경쟁력',
        id: 'competitiveness',
      },
      {
        visible: subscriptionCmsDetail?.data[0]?.investmentConsiderationVisible,
        label: '투자 유의 사항',
        id: 'caution',
      },
    ].filter(({ visible }) => visible);
  }, [subscriptionCmsDetail]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveTab(entry.target.id);
          }
        });
      },
      {
        rootMargin: '-50% 0px -50% 0px', // 화면 중앙에 있을 때만 활성화
      },
    );

    tabs.forEach(({ id }) => {
      const element = document.getElementById(id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [tabs]);

  return { activeTab, tabs };
};
