import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';

import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { createIssuerInquiry } from '@/entities/inquiries/api/createIssuerInquiry';
import { InquiryAnswerStatus } from '@/entities/inquiries/types';
import { Subscription } from '@/entities/subscriptions/types';

import { useToast } from '@/shared/model/useToast';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import {
  SubscriptionInquiryFormData,
  subscriptionInquiryFormSchema,
} from '../lib/SubscriptionInquirySchema';

interface SubscriptionInquiryProps {
  securitiesId: string;
  subscription?: Subscription;
  handleClose: () => void;
}

export const useSubscriptionInquiry = ({
  securitiesId,
  handleClose,
  subscription,
}: SubscriptionInquiryProps) => {
  const form = useForm<SubscriptionInquiryFormData>({
    resolver: zodResolver(subscriptionInquiryFormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const { userId } = useFetchUser();
  const { successToast, errorToast } = useToast();
  const { routerPush } = useWebViewRouter();
  const queryClient = useQueryClient();

  const onSubmit = async (data: SubscriptionInquiryFormData) => {
    if (!userId) {
      errorToast({
        title: '로그인 후 이용해주세요.',
      });

      routerPush('/sign-in');
      return;
    }

    try {
      await createIssuerInquiry({
        ...data,
        userId,
        securitiesId,
        status: 'published',
        issuerName: subscription?.issuer.name || '',
        answerStatus: InquiryAnswerStatus.REQUEST,
      });
      successToast({
        title: '문의가 등록되었습니다.',
      });

      await queryClient.invalidateQueries({
        queryKey: queries.inquiries._def,
      });

      handleClose();
      form.reset();
    } catch (error) {
      errorToast({
        title: '문의 등록에 실패했습니다.',
      });
    }
  };

  return { form, onSubmit };
};
