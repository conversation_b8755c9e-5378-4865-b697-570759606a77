'use client';

import { useEffect, useState } from 'react';

import { infoNavigateItems } from '../config';

const SCROLL_OFFSET = 300; // 화면 상단에서 300px 지점

export const useInfoNavigate = () => {
  const [activeSection, setActiveSection] = useState<string>('summary');

  useEffect(() => {
    const handleScroll = () => {
      const sections = infoNavigateItems.map((item) => document.getElementById(item.id));
      const scrollPosition = window.scrollY + SCROLL_OFFSET; // 300px 지점 기준으로 계산

      sections.forEach((section, index) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionBottom = sectionTop + section.offsetHeight;

          // if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
          //   setActiveSection(infoNavigateItems[index].id);
          // }
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleClick = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - SCROLL_OFFSET;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
      setActiveSection(id);
    }
  };
  return {
    handleClick,
    activeSection,
  };
};
