import { Notification, RecipientStatus } from '@/entities/notifications/types';
import { ReadNotificationCard } from '@/entities/notifications/ui/ReadNotificationCard';
import { UnreadNotificationCard } from '@/entities/notifications/ui/UnreadNotificationCard';

import { InfiniteScrollResponse } from '@/shared/interface';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { ScrollArea } from '@/shared/ui/shadcn/scroll-area';
import { Separator } from '@/shared/ui/shadcn/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

interface NotificationListProps {
  notifications?: InfiniteScrollResponse<Notification>;
  ref: () => void;
  isMobile?: boolean;
}

export const NotificationList = ({ notifications, ref, isMobile }: NotificationListProps) => {
  return (
    <>
      {!isMobile && (
        <FallbackImage
          src="/images/notification_edge.png"
          alt="notification_edge"
          width={24}
          height={14}
          className="absolute right-[14px] top-[-14px] sm:right-[38px]"
        />
      )}
      <Tabs defaultValue="my">
        <TabsList className="grid h-[60px] w-full grid-cols-2 text-[17px] font-bold">
          <TabsTrigger
            value="my"
            className="h-[50px] rounded-none border-b border-gray-300 font-semibold text-gray-300 data-[state=active]:border-b-2 data-[state=active]:border-black data-[state=active]:text-gray-900"
          >
            내 소식
          </TabsTrigger>
          <TabsTrigger
            value="service"
            disabled
            className="h-[50px] rounded-none border-b border-gray-300 font-semibold text-gray-300 data-[state=active]:border-b-2 data-[state=active]:border-black data-[state=active]:text-gray-900"
          >
            서비스 소식
          </TabsTrigger>
        </TabsList>
        <TabsContent value="my" className="">
          {notifications?.pages[0].totalCount === 0 && (
            <div
              className={`${isMobile ? 'h-[calc(100dvh-160px)]' : 'min-h-[530px]'} flex flex-col items-center justify-center`}
            >
              <div className="flex flex-col items-center gap-5">
                <FallbackImage
                  src="/icons/main_alarm.png"
                  alt="main_alarm"
                  width={60}
                  height={60}
                />
                <div className="space-y-2 text-center">
                  <p className="font-semibold"> 새로운 알람이 없습니다.</p>
                  <p className="text-sm">
                    뉴밋 서비스와 다양한 알림을 <br /> 이곳에서 모아볼 수 있습니다.
                  </p>
                </div>
              </div>
            </div>
          )}
          <ScrollArea className={`h-[${isMobile ? 'calc(100vh-160px)' : '530px'}] overflow-y-auto`}>
            {notifications?.pages?.map((page) => {
              return page.data.map((notification, index) => {
                const isRead = notification.recipients[0].status === RecipientStatus.READ;
                if (isRead) {
                  return (
                    <div key={notification.id}>
                      <ReadNotificationCard key={notification.id} notification={notification} />
                      {index !== page.data.length - 1 && (
                        <Separator orientation="horizontal" className="mx-5 my-6 bg-gray-200" />
                      )}
                    </div>
                  );
                } else {
                  return (
                    <div key={notification.id}>
                      <UnreadNotificationCard key={notification.id} notification={notification} />
                      {index !== page.data.length - 1 && (
                        <Separator orientation="horizontal" className="mx-5 my-6 bg-gray-200" />
                      )}
                    </div>
                  );
                }
              });
            })}
            <div ref={ref} className="h-12" />
          </ScrollArea>
        </TabsContent>
        <TabsContent value="service" className="my-0 border-b border-gray-300">
          support
        </TabsContent>
      </Tabs>
      {Boolean(notifications?.pages[0].totalCount && notifications?.pages[0].totalCount > 0) && (
        <div className="my-4 flex justify-center text-xs font-semibold text-gray-400">
          최근 30일 동안 받은 소식을 표시합니다.
        </div>
      )}
    </>
  );
};
