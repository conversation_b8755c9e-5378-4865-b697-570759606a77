import { BellIcon } from '@heroicons/react/24/outline';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';

interface NotificationButtonProps {
  notificationButtonRef: React.RefObject<HTMLButtonElement | null>;
  toggleVisibility: () => void;
  isVisible: boolean;
  unreadCount: number;
}

export const NotificationButton = ({
  notificationButtonRef,
  toggleVisibility,
  isVisible,
  unreadCount,
}: NotificationButtonProps) => {
  const { screenSize } = useScreenStore();
  const { routerPush } = useWebViewRouter();

  const handleClick = () => {
    if (screenSize === ScreenSize.MOBILE) {
      routerPush('/mobile/notifications');
      return;
    }
    toggleVisibility();
  };

  return (
    <button
      ref={notificationButtonRef}
      onClick={handleClick}
      className={`relative flex h-9 w-9 items-center justify-center rounded-md hover:bg-[#607D8B]/15 ${isVisible && 'bg-[#607D8B]/15'}`}
    >
      <BellIcon className="h-6 w-6" />
      {unreadCount > 0 && (
        <div className="absolute left-1/2 top-0 z-10 min-w-[20px] translate-x-[calc(-50%+9px)] rounded-[10px] bg-pink-400 px-[6px] py-[1px] text-xs font-medium leading-[150%] text-white">
          {unreadCount}
        </div>
      )}
    </button>
  );
};
