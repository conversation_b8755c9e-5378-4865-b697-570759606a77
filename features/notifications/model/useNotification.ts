import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useRef } from 'react';
import { useInView } from 'react-intersection-observer';

import { queries } from '@/features/lib/queries';
import { fetchInfiniteNotifications } from '@/features/notifications/api/fetchNotifications';

import { readNotification } from '@/entities/notifications/api/readNotification';
import { RecipientStatus } from '@/entities/notifications/types';

import { useVisibility } from '@/shared/model/useVisibility';

export const useNotification = (userId: string) => {
  const { isVisible, toggleVisibility, closeToggle } = useVisibility();
  const notificationRef = useRef<HTMLDivElement>(null);
  const notificationButtonRef = useRef<HTMLButtonElement>(null);
  const queryClient = useQueryClient();

  const {
    data: notifications,
    fetchNextPage,
    hasNextPage,
    isLoading,
  } = fetchInfiniteNotifications(userId, {
    perPage: 5,
    category: 'TEST',
  });

  const [ref, inView] = useInView({
    delay: 100,
    threshold: 0.5,
  });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage]);

  const unreadCount = useMemo(() => {
    if (!notifications?.pages) return 0;

    return notifications.pages.reduce((total, page) => {
      const unreadInPage =
        page.data?.filter(
          (notification) => notification.recipients[0].status === RecipientStatus.SENT,
        ).length || 0;
      return total + unreadInPage;
    }, 0);
  }, [notifications]);

  const handleReadAll = async () => {
    try {
      // 모든 읽지 않은 알림들을 찾아서 병렬로 처리
      const unreadNotifications =
        notifications?.pages.flatMap((page) =>
          page.data.filter(
            (notification) => notification.recipients[0].status === RecipientStatus.SENT,
          ),
        ) || [];

      // 모든 읽음 처리를 병렬로 실행하고 완료될 때까지 기다림
      await Promise.all(
        unreadNotifications.map((notification) => readNotification(notification.id)),
      );

      // 모든 읽음 처리가 완료된 후 쿼리 무효화
      await queryClient.invalidateQueries({ queryKey: queries.notifications._def });
    } catch (error) {
      console.error(error);
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      notificationRef.current &&
      !notificationRef.current.contains(event.target as Node) &&
      !notificationButtonRef.current?.contains(event.target as Node)
    ) {
      closeToggle();
    }
  };

  useEffect(() => {
    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      if (unreadCount > 0) {
        handleReadAll();
      }
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, closeToggle, unreadCount]);

  return {
    notificationRef,
    notificationButtonRef,
    isVisible,
    toggleVisibility,
    unreadCount,
    notifications,
    ref,
    isLoading,
    handleReadAll,
  };
};
