import { useInfiniteQuery, UseInfiniteQueryResult } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { NotificationRequest } from '@/entities/notifications/interface';
import { Notification } from '@/entities/notifications/types';

import { InfiniteScrollResponse, ScrollApiResponse } from '@/shared/interface';

// export const useFetchNotifications = (userId: string, params: NotificationRequest) => {
//   return useQuery({
//     ...queries.notifications.list(userId, params),
//   });
// };

export const fetchInfiniteNotifications = (
  userId: string,
  params: NotificationRequest,
): UseInfiniteQueryResult<InfiniteScrollResponse<Notification>> => {
  return useInfiniteQuery({
    ...queries.notifications.infinite(userId, params),
    initialPageParam: 1,
    getNextPageParam: (lastPage: ScrollApiResponse<Notification>) => {
      if (!lastPage.isLast) return lastPage.nextPage;
      return null;
    },
  });
};
