{
  "parser": "@typescript-eslint/parser",
  "extends": ["airbnb", "airbnb-typescript", "plugin:@typescript-eslint/recommended", "prettier"],
  "parserOptions": {
    "project": "./tsconfig.json"
  },
  "rules": {
    // React를 import 하지 않아도 JSX를 사용할 수 있게 함
    // React 17+ 버전에서는 자동으로 처리되므로 off로 설정
    "react/react-in-jsx-scope": "off",

    // 함수형 컴포넌트의 정의 방식을 제한하지 않음
    // (화살표 함수, 일반 함수 선언 등 모두 허용)
    "react/function-component-definition": "off",

    // props의 기본값(defaultProps) 설정을 강제하지 않음
    // TypeScript를 사용할 때는 보통 필요하지 않음
    "react/require-default-props": "off",

    // import 문에서 파일 확장자 생략 허용
    "import/extensions": "off",

    // devDependencies에서 import를 허용
    // (테스트 파일 등에서 필요한 설정)
    "import/no-extraneous-dependencies": "off",

    // 화살표 함수의 본문 스타일을 제한하지 않음
    // (한 줄 return이나 중괄호 사용을 자유롭게)
    "arrow-body-style": "off",

    "import/prefer-default-export": "off",
    "@typescript-eslint/no-unused-vars": "off"
  },
  "plugins": ["prettier"]
}
