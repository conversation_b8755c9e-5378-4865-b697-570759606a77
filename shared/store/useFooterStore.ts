import { create } from 'zustand';

import { ScreenSize } from '../types';

interface FooterStore {
  isFooterVisible: boolean;
  // 라우트와 화면 크기 기반 초기화
  initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => void;
  setIsFooterVisible: (isVisible: boolean) => void;
}

export const useFooterStore = create<FooterStore>((set) => ({
  isFooterVisible: false, // 기본값은 숨김

  initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => {
    // 모바일에서만 메인 페이지일 때만 표시
    if (screenSize === ScreenSize.MOBILE) {
      set({ isFooterVisible: pathname === '/' });
    } else {
      // 데스크톱에서는 항상 표시
      set({ isFooterVisible: true });
    }
  },

  setIsFooterVisible: (isVisible) => set({ isFooterVisible: isVisible }),
}));
