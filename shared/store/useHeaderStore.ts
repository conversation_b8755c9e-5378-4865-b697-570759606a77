import { ReactNode } from 'react';
import { create } from 'zustand';

import { getLayoutConfigByPathAndScreen } from '../config/layoutConfig';
import { HeaderMode, ScreenSize } from '../types';

interface HeaderStore {
  mode: HeaderMode;
  title?: string;
  subTitle?: string;
  isBackButtonVisible: boolean;
  rightComponent?: ReactNode;
  // 라우트와 화면 크기 기반 초기화
  initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => void;
  setMode: (
    mode?: HeaderMode,
    options?: {
      title?: string;
      subTitle?: string;
      rightComponent?: ReactNode;
    },
  ) => void;
  setRightComponent: (component: ReactNode) => void;
  clearRightComponent: () => void;
  // 동적 업데이트 (기존 usePageHeader 대체)
  updateHeader: (options: {
    mode?: HeaderMode;
    title?: string;
    subTitle?: string;
    rightComponent?: ReactNode;
  }) => void;
}

// SSR 안전한 초기 상태 설정
const getInitialState = () => {
  return {
    mode: HeaderMode.MAIN,
    title: undefined,
    subTitle: undefined,
    rightComponent: undefined,
  };
};

export const useHeaderStore = create<HeaderStore>((set, get) => {
  const initialHeader = getInitialState();

  return {
    mode: initialHeader.mode,
    title: initialHeader.title,
    subTitle: initialHeader.subTitle,
    isBackButtonVisible:
      initialHeader.mode === HeaderMode.LIST || initialHeader.mode === HeaderMode.DETAIL,
    rightComponent: initialHeader.rightComponent,

    initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => {
      const config = getLayoutConfigByPathAndScreen(pathname, screenSize);
      if (!config) return; // 데스크톱에서는 업데이트하지 않음

      const { mode, title, subTitle, rightComponent } = config.header;
      const isBackButtonVisible = mode === HeaderMode.LIST || mode === HeaderMode.DETAIL;

      set({
        mode,
        title,
        subTitle,
        rightComponent,
        isBackButtonVisible,
      });
    },

    setMode: (mode, options = {}) => {
      if (!mode) return;

      const isBackButtonVisible = mode === HeaderMode.LIST || mode === HeaderMode.DETAIL;
      set({
        mode,
        title: options.title,
        subTitle: options.subTitle,
        rightComponent: options.rightComponent,
        isBackButtonVisible,
      });
    },

    updateHeader: (options) => {
      const currentState = get();
      const mode = options.mode ?? currentState.mode;
      const isBackButtonVisible = mode === HeaderMode.LIST || mode === HeaderMode.DETAIL;

      set({
        mode,
        title: options.title ?? currentState.title,
        subTitle: options.subTitle ?? currentState.subTitle,
        rightComponent: options.rightComponent ?? currentState.rightComponent,
        isBackButtonVisible,
      });
    },

    setRightComponent: (component) => set({ rightComponent: component }),
    clearRightComponent: () => set({ rightComponent: undefined }),
  };
});
