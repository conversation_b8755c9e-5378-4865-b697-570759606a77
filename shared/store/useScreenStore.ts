import { create } from 'zustand';

import { ScreenSize } from '../types';

interface ScreenStore {
  screenSize: ScreenSize;
  isHydrated: boolean; // SSR/CSR 상태 추적
  setScreenSize: (screenSize: ScreenSize) => void;
  setHydrated: (isHydrated: boolean) => void;
}

export const useScreenStore = create<ScreenStore>((set) => ({
  screenSize: ScreenSize.DESKTOP, // SSR 안전한 기본값
  isHydrated: false,
  setScreenSize: (screenSize) => set({ screenSize }),
  setHydrated: (isHydrated) => set({ isHydrated }),
}));
