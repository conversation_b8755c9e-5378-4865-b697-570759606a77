import { create } from 'zustand';

import { getLayoutConfigByPathAndScreen } from '../config/layoutConfig';
import { ScreenSize } from '../types';

interface GNBStore {
  isGNBVisible: boolean;
  // 라우트와 화면 크기 기반 초기화
  initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => void;
  setIsGNBVisible: (isGNBVisible: boolean) => void;
}

// SSR 안전한 초기 상태 설정
const getInitialGNBState = () => {
  return false; // 기본값은 숨김 (모바일에서만 표시)
};

export const useGNBStore = create<GNBStore>((set) => ({
  isGNBVisible: getInitialGNBState(),

  initializeFromPathAndScreen: (pathname: string, screenSize: ScreenSize) => {
    const config = getLayoutConfigByPathAndScreen(pathname, screenSize);
    if (!config) return; // 데스크톱에서는 업데이트하지 않음
    set({ isGNBVisible: config.gnb.isVisible });
  },

  setIsGNBVisible: (isGNBVisible) => set({ isGNBVisible }),
}));
