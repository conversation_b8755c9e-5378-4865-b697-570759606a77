'use client';

import { useEffect } from 'react';

import { useWebViewRouter } from './useWebViewRouter';

export const useWebviewRouteListener = () => {
  const { routerPush } = useWebViewRouter();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        const parsed = JSON.parse(event.data);

        if (parsed.type === 'DEEP_LINK' && typeof parsed.data === 'string') {
          routerPush(parsed.data);
        }
      } catch (e) {
        console.warn('Invalid message format from RN:', event.data);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [routerPush]);
};
