'use client';

import { useState } from 'react';

export const useStep = <T>(initialStep: T) => {
  const [step, setStep] = useState<T>(initialStep);

  const handleStep = (nextStep: T) => {
    setStep(nextStep);
  };

  // 첫 단계로 이동
  const handleFirstStep = () => {
    setStep(initialStep);
  };

  // 첫 단계인지 확인
  const isFirstStep = () => {
    return step === initialStep;
  };

  // 특정 단계인지 확인
  const isStep = (targetStep: T) => {
    return step === targetStep;
  };

  return { step, handleStep, handleFirstStep, isFirstStep, isStep };
};
