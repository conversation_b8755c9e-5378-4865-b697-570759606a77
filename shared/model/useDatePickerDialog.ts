'use client';

import { useEffect, useState } from 'react';

export const useDatePickerDialog = () => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState({
    startDate: false,
    endDate: false,
  });

  const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (isDatePickerOpen && !target.closest('.relative')) {
      setIsDatePickerOpen({ startDate: false, endDate: false });
    }
  };

  const handleDatePickerClick = (name: 'startDate' | 'endDate') => {
    setIsDatePickerOpen({
      startDate: name === 'startDate' ? !isDatePickerOpen.startDate : false,
      endDate: name === 'endDate' ? !isDatePickerOpen.endDate : false,
    });
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isDatePickerOpen]);

  return {
    isDatePickerOpen,
    handleDatePickerClick,
  };
};
