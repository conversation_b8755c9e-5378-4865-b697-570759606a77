'use client';

import { useEffect } from 'react';

import { useScreenStore } from '../store/useScreenStore';
import { ScreenSize } from '../types';

// 화면 크기 브레이크포인트 정의
const BREAKPOINTS = {
  mobile: 744, // 744px 이하: 모바일
  tablet: 1040, // 744px ~ 1280px: 태블릿
  desktop: 1280, // 1280px 이상: 데스크톱
};

export const useResponsive = () => {
  const { setScreenSize, setHydrated, isHydrated } = useScreenStore();

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      if (width <= BREAKPOINTS.mobile) {
        setScreenSize(ScreenSize.MOBILE);
      } else if (width <= BREAKPOINTS.tablet) {
        setScreenSize(ScreenSize.TABLET);
      } else {
        setScreenSize(ScreenSize.DESKTOP);
      }
    };

    handleResize();
    if (!isHydrated) {
      setHydrated(true);
    }

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [setScreenSize, setHydrated, isHydrated]);
};
