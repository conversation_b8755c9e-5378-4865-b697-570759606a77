'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { RN_API, WebViewMessage } from '@/shared/lib/webViewMessage';

export const useWebViewRouter = () => {
  const router = useRouter();
  const [isApp, setIsApp] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    // react native app 환경인지 판단
    setIsApp(!!window.ReactNativeWebView);
  }, []);

  // 뒤로가기 하는 경우
  const routerBack = () => {
    if (isApp) {
      WebViewMessage(RN_API.ROUTER_CHANGE, {
        value: 'back',
      });
    } else {
      router.back();
    }
  };

  // push 하는 경우
  const routerPush = (url: string, scroll: boolean = true) => {
    if (isApp) {
      WebViewMessage(RN_API.ROUTER_CHANGE, {
        value: url,
      });
    } else {
      router.push(url, {
        scroll,
      });
    }
  };

  const openExternalLink = (url: string) => {
    if (isApp) {
      // 웹뷰 환경에서는 네이티브 브릿지를 통해 외부 링크 열기
      window.ReactNativeWebView?.postMessage(
        JSON.stringify({
          type: 'OPEN_EXTERNAL_LINK',
          payload: { url },
        }),
      );
    } else {
      // 일반 웹 환경에서는 window.open 사용
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  return {
    isApp,
    routerBack,
    routerPush,
    openExternalLink,
  };
};
