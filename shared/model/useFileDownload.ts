'use client';

import { useSession } from 'next-auth/react';
import { useCallback } from 'react';

import { RequiredDocsDetail } from '@/entities/subscriptions/types';

import { useToast } from '@/shared/model/useToast';

export const useFileDownload = () => {
  const { errorToast } = useToast();
  const { data: session } = useSession();

  const handleFileDownload = useCallback(
    async (docs: RequiredDocsDetail) => {
      try {
        if (!docs.metadata?.mapId || !docs.metadata?.service || !docs.metadata?.code) {
          errorToast({
            title: '파일 정보가 불완전합니다.',
          });
          return;
        }

        // 다운로드 URL 생성 (파일명도 함께 전달)
        const downloadUrl = `/api/files/download?mapId=${docs.metadata.mapId}&service=${docs.metadata.service}&code=${docs.metadata.code}&fileName=${encodeURIComponent(docs.metadata?.title || docs.fileName || 'download')}`;

        // 파일 다운로드 실행
        const response = await fetch(downloadUrl, {
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '다운로드에 실패했습니다.');
        }

        // 파일 다운로드 처리
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = docs.metadata?.title || docs.fileName || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('파일 다운로드 오류:', error);
        errorToast({
          title: error instanceof Error ? error.message : '파일 다운로드에 실패했습니다.',
        });
      }
    },
    [errorToast],
  );

  return { handleFileDownload };
};
