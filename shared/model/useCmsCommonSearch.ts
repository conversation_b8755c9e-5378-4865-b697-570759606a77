import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useDatePickerDialog } from '@/shared/model/useDatePickerDialog';

export const useCmsCommonSearch = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [searchFormState, setSearchFormState] = useState({
    startDate: searchParams.get('createdAt')?.split(',')[0] || '',
    endDate: searchParams.get('createdAt')?.split(',')[1] || '',
    keyword: searchParams.get('keyword') || '',
    searchType: searchParams.get('searchType') || 'title',
  });

  const { isDatePickerOpen, handleDatePickerClick } = useDatePickerDialog();

  const handleformState = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchFormState({ ...searchFormState, [e.target.name]: e.target.value });
  };

  const handleStartDateChange = (value: string) => {
    setSearchFormState({ ...searchFormState, startDate: value });
  };

  const handleEndDateChange = (value: string) => {
    setSearchFormState({ ...searchFormState, endDate: value });
  };

  const handleSearchType = (value: string) => {
    setSearchFormState({ ...searchFormState, searchType: value });
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const searchParams = new URLSearchParams();
    if (searchFormState.startDate || searchFormState.endDate) {
      searchParams.set(
        'createdAt',
        `${searchFormState.startDate.toString()},${searchFormState.endDate.toString()}`,
      );
    }
    if (searchFormState.keyword) {
      searchParams.set('keyword', searchFormState.keyword);
    }
    if (searchFormState.searchType) {
      searchParams.set('searchType', searchFormState.searchType);
    }
    router.replace(`${pathname}?${searchParams.toString()}`);
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);

    if (searchFormState.startDate || searchFormState.endDate) {
      searchParams.set(
        'createdAt',
        `${searchFormState.startDate.toString()},${searchFormState.endDate.toString()}`,
      );
    }

    router.replace(`${pathname}?${searchParams.toString()}`);
  }, [searchFormState.startDate, searchFormState.endDate]);

  return {
    searchFormState,
    handleformState,
    handleSearch,
    handleSearchType,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
    isDatePickerOpen,
  };
};
