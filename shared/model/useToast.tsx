'use client';

import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

export const useToast = () => {
  const successToast = ({
    title,
    description,
    action,
    actionText,
    duration = 3000,
  }: {
    title?: string;
    description?: string;
    action?: () => void;
    actionText?: string;
    duration?: number;
  }) => {
    const position = window.innerWidth < 996 ? 'top-center' : 'bottom-center'; // 화면 크기에 따라 위치 설정
    return toast.custom(
      (t: any) => (
        <motion.div
          initial={{ opacity: 0, y: -20 }} // 초기 상태
          animate={{ opacity: 1, y: 0 }} // 애니메이션 상태
          exit={{ opacity: 0, y: 20 }} // 종료 애니메이션
          transition={{ duration: 0.3 }} // 애니메이션 지속 시간
          className={`${
            t.visible ? 'animate-enter' : 'animate-leave'
          } pointer-events-auto flex w-full max-w-[386px] cursor-pointer select-none justify-between rounded-lg bg-white px-5 py-4 shadow-lg ring-1 ring-black ring-opacity-5`}
          onClick={() => toast.dismiss(t.id)} // 클릭으로 토스트 닫기
          onTouchStart={(e) => {
            const touch = e.touches[0];
            const startY = touch.clientY;
            const startX = touch.clientX;

            const handleTouchMove = (e: TouchEvent) => {
              const touch = e.touches[0];
              const deltaY = touch.clientY - startY;
              const deltaX = touch.clientX - startX;

              // 수직 드래그가 수평 드래그보다 클 때만 처리
              if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 50) {
                toast.dismiss(t.id);
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
              }
            };

            const handleTouchEnd = () => {
              document.removeEventListener('touchmove', handleTouchMove);
              document.removeEventListener('touchend', handleTouchEnd);
            };

            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd);
          }}
        >
          <div className="flex flex-1 items-center">
            <div className="flex items-center gap-3">
              <CheckCircleIcon className="h-5 w-5" color="green" />
              <div>
                {title && <p className="font-semibold">{title}</p>}
                {description && <p className="text-sm">{description}</p>}
              </div>
            </div>
          </div>
          {action && <button onClick={action}>{actionText}</button>}
        </motion.div>
      ),
      {
        position,
        duration,
        removeDelay: duration,
      },
    );
  };

  const errorToast = ({
    title,
    description,
    action,
    actionText,
    duration = 3000,
  }: {
    title?: string;
    description?: string;
    action?: () => void;
    actionText?: string;
    duration?: number;
  }) => {
    const position = window.innerWidth < 996 ? 'top-center' : 'bottom-center'; // 화면 크기에 따라 위치 설정
    return toast.custom(
      (t: any) => (
        <motion.div
          initial={{ opacity: 0, y: -20 }} // 초기 상태
          animate={{ opacity: 1, y: 0 }} // 애니메이션 상태
          exit={{ opacity: 0, y: 20 }} // 종료 애니메이션
          transition={{ duration: 0.3 }} // 애니메이션 지속 시간
          className={`${
            t.visible ? 'animate-enter' : 'animate-leave'
          } pointer-events-auto flex w-full max-w-[386px] cursor-pointer select-none justify-between rounded-lg bg-white px-5 py-4 shadow-lg ring-1 ring-black ring-opacity-5`}
          onClick={() => toast.dismiss(t.id)} // 클릭으로 토스트 닫기
          onTouchStart={(e) => {
            const touch = e.touches[0];
            const startY = touch.clientY;
            const startX = touch.clientX;

            const handleTouchMove = (e: TouchEvent) => {
              const touch = e.touches[0];
              const deltaY = touch.clientY - startY;
              const deltaX = touch.clientX - startX;

              // 수직 드래그가 수평 드래그보다 클 때만 처리
              if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 50) {
                toast.dismiss(t.id);
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
              }
            };

            const handleTouchEnd = () => {
              document.removeEventListener('touchmove', handleTouchMove);
              document.removeEventListener('touchend', handleTouchEnd);
            };

            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd);
          }}
        >
          <div className="flex flex-1 items-center">
            <div className="flex items-center gap-3">
              <ExclamationCircleIcon className="h-5 w-5" color="red" />
              <div>
                {title && <p className="font-semibold">{title}</p>}
                {description && <p className="text-sm">{description}</p>}
              </div>
            </div>
          </div>
          {action && <button onClick={action}>{actionText}</button>}
        </motion.div>
      ),
      {
        position,
        duration,
        removeDelay: duration,
      },
    );
  };
  return { successToast, errorToast };
};
