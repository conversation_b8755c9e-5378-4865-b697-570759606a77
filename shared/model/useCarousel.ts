'use client';

import { useState } from 'react';
import { SwiperClass } from 'swiper/react';

export const useCarousel = () => {
  const [swiper, setSwiper] = useState<SwiperClass>();
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    swiper?.slidePrev();
  };
  const handleNext = () => {
    swiper?.slideNext();
  };

  const handleSlideChange = (swiperInstance: SwiperClass) => {
    setCurrentIndex(swiperInstance.realIndex);
  };

  return {
    swiper,
    setSwiper,
    currentIndex,
    handlePrev,
    handleNext,
    handleSlideChange,
  };
};
