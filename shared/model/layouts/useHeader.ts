'use client';

import { signOut } from 'next-auth/react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';

import { WebViewMessage } from '../../lib/webViewMessage';
import { useHeaderStore } from '../../store/useHeaderStore';
import { useVisibility } from '../useVisibility';
import { useWebViewRouter } from '../useWebViewRouter';

export const useHeader = () => {
  const { user, isGeneral, username } = useFetchUser();

  const { isApp } = useWebViewRouter();
  const { isVisible: isLogoutVisible, toggleVisibility: toggleLogoutVisibility } = useVisibility();
  const { mode, subTitle, title } = useHeaderStore();
  const { screenSize } = useScreenStore();

  const handleConfirm = async () => {
    if (isApp) {
      await signOut();
      WebViewMessage('logout', {});
    } else {
      await signOut({ redirect: true, callbackUrl: '/' });
    }
  };
  return {
    isLogoutVisible,
    toggleLogoutVisibility,
    mode,
    subTitle,
    title,
    isMobile: screenSize === ScreenSize.MOBILE,
    handleConfirm,
    user,
    isApp,
    isGeneral,
    username,
  };
};
