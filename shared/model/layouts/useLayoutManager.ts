'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

import { useFooterStore } from '../../store/useFooterStore';
import { useGNBStore } from '../../store/useGNBStore';
import { useHeaderStore } from '../../store/useHeaderStore';
import { useScreenStore } from '../../store/useScreenStore';

/**
 * 라우트 변경과 화면 크기 변경을 감지하고 자동으로 레이아웃을 업데이트하는 훅
 * 이 훅은 앱의 최상위 레벨에서 한 번만 사용되어야 합니다.
 */
export const useLayoutManager = () => {
  const pathname = usePathname();
  const { screenSize, isHydrated } = useScreenStore();
  const { initializeFromPathAndScreen: initializeHeader } = useHeaderStore();
  const { initializeFromPathAndScreen: initializeGNB } = useGNBStore();
  const { initializeFromPathAndScreen: initializeFooter } = useFooterStore();

  useEffect(() => {
    if (!isHydrated) return;

    initializeHeader(pathname, screenSize);
    initializeGNB(pathname, screenSize);
    initializeFooter(pathname, screenSize);
  }, [pathname, screenSize, isHydrated, initializeHeader, initializeGNB, initializeFooter]);
};
