import { useParams, usePathname } from 'next/navigation';

import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';

import { useVisibility } from '../useVisibility';
import { useWebViewRouter } from '../useWebViewRouter';

export const useUserMobileTab = () => {
  const params = useParams();
  const pathname = usePathname();

  const isHide = params.id || pathname.includes('/withdrawal');
  const { screenSize } = useScreenStore();
  const { isVisible: isVisibleAccountDialog, toggleVisibility: toggleVisibilityAccountDialog } =
    useVisibility();
  const { routerPush } = useWebViewRouter();

  const isMy = pathname.includes('/my');
  const isSupport = pathname.includes('/support');
  const isAssets = pathname.includes('/assets');

  const goToAccountRegister = () => {
    routerPush(`/deposit/account-register?callbackUrl=/user/assets/deposit`);
  };

  const mobileTabValue = () => {
    switch (true) {
      case isMy:
        return 'my';
      case isSupport:
        return 'support';
      case isAssets:
        return 'assets';
      default:
        return 'my';
    }
  };

  return {
    isHide,
    isMobile: screenSize === ScreenSize.MOBILE,
    isMy,
    isSupport,
    isAssets,
    mobileTabValue,
    isVisibleAccountDialog,
    toggleVisibilityAccountDialog,
    goToAccountRegister,
  };
};
