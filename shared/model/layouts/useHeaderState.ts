'use client';

import { useEffect } from 'react';

import { useHeaderStore } from '../../store/useHeaderStore';

export const useHeaderState = (title: string, main?: boolean) => {
  const { headerTitle, setHeaderTitle, isMain, setIsMain } = useHeaderStore();

  useEffect(() => {
    setHeaderTitle(title);
    setIsMain(main ?? false);
  }, [title, setHeaderTitle, main, setIsMain]);

  return { headerTitle, isMain };
};
