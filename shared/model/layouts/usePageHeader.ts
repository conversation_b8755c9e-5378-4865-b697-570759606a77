import { ReactNode, useEffect } from 'react';

import { useHeaderStore } from '../../store/useHeaderStore';
import { HeaderMode } from '../../types';

interface UsePageHeaderProps {
  mode?: HeaderMode;
  title?: string;
  subTitle?: string;
  rightComponent?: ReactNode;
}

export const usePageHeader = ({ mode, title, subTitle, rightComponent }: UsePageHeaderProps) => {
  const setMode = useHeaderStore((state) => state.setMode);
  const setRightComponent = useHeaderStore((state) => state.setRightComponent);
  const clearRightComponent = useHeaderStore((state) => state.clearRightComponent);

  useEffect(() => {
    setMode(mode, { title, subTitle });

    if (rightComponent) {
      setRightComponent(rightComponent);
    }

    // 컴포넌트가 언마운트될 때 정리
    return () => {
      clearRightComponent();
    };
  }, [mode, title, subTitle, rightComponent]); // 함수들을 의존성에서 제외
};
