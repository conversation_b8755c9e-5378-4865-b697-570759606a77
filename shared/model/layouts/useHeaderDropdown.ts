'use client';

import debounce from 'lodash/debounce';
import { useCallback, useState } from 'react';

export const useHeaderDropdown = () => {
  const [isContentsDropdownOpen, setIsContentsDropdownOpen] = useState(false);
  const [isSupportDropdownOpen, setIsSupportDropdownOpen] = useState(false);

  const debouncedSetContentsOpen = useCallback(
    debounce((value: boolean) => {
      setIsContentsDropdownOpen(value);
    }, 150),
    [],
  );

  const debouncedSetSupportOpen = useCallback(
    debounce((value: boolean) => {
      setIsSupportDropdownOpen(value);
    }, 150),
    [],
  );

  // 마우스 이벤트 핸들러
  const handleContentsMouseEnter = useCallback(() => {
    debouncedSetSupportOpen(false); // 다른 드롭다운 닫기
    debouncedSetContentsOpen(true);
  }, [debouncedSetContentsOpen, debouncedSetSupportOpen]);

  const handleContentsMouseLeave = useCallback(() => {
    debouncedSetContentsOpen(false);
  }, [debouncedSetContentsOpen]);

  const handleSupportMouseEnter = useCallback(() => {
    debouncedSetContentsOpen(false); // 다른 드롭다운 닫기
    debouncedSetSupportOpen(true);
  }, [debouncedSetContentsOpen, debouncedSetSupportOpen]);

  const handleSupportMouseLeave = useCallback(() => {
    debouncedSetSupportOpen(false);
  }, [debouncedSetSupportOpen]);

  return {
    isContentsDropdownOpen,
    isSupportDropdownOpen,
    handleContentsMouseEnter,
    handleContentsMouseLeave,
    handleSupportMouseEnter,
    handleSupportMouseLeave,
  };
};
