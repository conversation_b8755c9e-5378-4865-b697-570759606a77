import { useEffect } from 'react';

import { useGNBStore } from '@/shared/store/useGNBStore';

interface UsePageGNBProps {
  isGNBVisible: boolean;
}

export const usePageGNB = ({ isGNBVisible }: UsePageGNBProps) => {
  const { isGNBVisible: currentIsGNBVisible, setIsGNBVisible } = useGNBStore();

  useEffect(() => {
    if (currentIsGNBVisible === isGNBVisible) return;

    setIsGNBVisible(isGNBVisible);
  }, [isGNBVisible, currentIsGNBVisible]);
};
