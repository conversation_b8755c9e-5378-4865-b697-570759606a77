import 'next-auth';

import { UserCat, UserRole } from '.';

declare module 'next-auth' {
  interface User {
    account: string;
    userId: string;
    accessToken: string;
    refreshToken: string;
    provider: string;
    accessTokenExpiredAt?: number;
    refreshTokenExpiredAt?: number;
    role: UserRole;
    userCat: UserCat;
  }

  interface Session {
    user: User;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    account: string;
    userId: string;
    accessToken: string;
    refreshToken: string;
    provider: string;
    accessTokenExpiredAt: number;
    refreshTokenExpiredAt: number;
    role: UserRole;
    userCat: UserCat;
  }
}
