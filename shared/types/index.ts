export interface AttachFile {
  fileId: string;
  name: string;
  url: string;
}

export enum NotificationType {
  SERVICE = 'SERVICE',
  NEWSLETTER = 'NEWSLETTER',
  MARKETING = 'MARKETING',
}

export enum NotificationChannel {
  SMS = 'SMS',
  EMAIL = 'EMAIL',
}

export enum HeaderMode {
  MAIN = 'main',
  CATEGORY = 'category',
  LIST = 'list',
  DETAIL = 'detail',
}

export enum UserRole {
  USR = 'USR',
  UIN = 'UIN',
  BIP = 'BIP',
  BIS = 'BIS',
  BRM = 'BRM',
  BRA = 'BRA',
  ISS = 'ISS',
  SY2 = 'SY2',
  SY1 = 'SY1',
  SYA = 'SYA',
}

export enum UserCat {
  GENERAL = 'GENERAL', // 일반 회원
  CORPORATE = 'CORPORATE', // 법인회원
  MINOR = 'MINOR', // 미성년자
}

export enum TwoStep {
  FIRST = 'FIRST',
  SECOND = 'SECOND',
}

export enum ScreenSize {
  MOBILE = 'mobile',
  TABLET = 'tablet',
  DESKTOP = 'desktop',
}
