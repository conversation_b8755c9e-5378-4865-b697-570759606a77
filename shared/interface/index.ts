export interface ListRequest {
  page?: number;
  perPage?: number;
}

export interface ListResponse<T> {
  data: T[];
  meta: {
    currentPage: number;
    lastPage: number;
    next: boolean;
    perPage: number;
    prev: boolean;
    total: number;
  };
}

export interface InfiniteScrollResponse<T> {
  pages: ScrollApiResponse<T>[];
  pageParams: number[];
}

export type ScrollApiResponse<T> = {
  data: T[];
} & {
  nextPage: number;
  isLast: boolean;
  totalCount: number;
};

export interface SuccessResponse<T> {
  message: string;
  statusCode: number;
  data: T;
}

export const emptyResponse = {
  data: [],
  meta: {
    total: 0,
    currentPage: 1,
    lastPage: 1,
    perPage: 10,
    prev: false,
    next: false,
  },
};

export const emptySubscriptionsResponse = {
  list: [],
  totalPage: 1,
  totalCount: 0,
};
