import React from 'react';

import { Button, ButtonProps } from './shadcn/button';

interface PrimaryButtonProps extends ButtonProps {
  className?: string;
  onClick?: () => void;
  text?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

export const PrimaryButton = ({
  className,
  onClick,
  text = '확인',
  type,
  disabled,
  ...props
}: PrimaryButtonProps) => {
  return (
    <Button
      className={`bg-gray-900 text-base font-semibold text-white disabled:border-none disabled:bg-gray-400 disabled:text-white sm:text-sm ${className}`}
      onClick={onClick}
      type={type}
      disabled={disabled}
      {...props}
    >
      {text}
    </Button>
  );
};
