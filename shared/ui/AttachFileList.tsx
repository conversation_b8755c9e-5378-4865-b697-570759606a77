import { DocumentTextIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import React from 'react';

import { AttachFile } from '../types';

interface AttachFileListProps {
  attachFiles: AttachFile[] | AttachFile;
}

export const AttachFileList = ({ attachFiles }: AttachFileListProps) => {
  const files = Array.isArray(attachFiles) ? attachFiles : [attachFiles];

  return (
    <div className="space-y-6">
      <p className="text-base font-semibold sm:text-lg">첨부파일</p>
      <div className="flex flex-col gap-4">
        {files?.map((attachFile) => (
          <Link
            href={attachFile.url}
            target="_blank"
            key={attachFile.fileId}
            className="flex items-center gap-4"
          >
            <div
              className="flex h-6 w-6 items-center justify-center rounded border sm:h-8 sm:w-8"
              style={{ borderColor: 'rgba(0, 0, 0, 0.1)' }}
            >
              <DocumentTextIcon className="h-4 w-4" />
            </div>
            <p className="text-sm text-gray-700 sm:text-base">{attachFile.name}</p>
          </Link>
        ))}
      </div>
    </div>
  );
};
