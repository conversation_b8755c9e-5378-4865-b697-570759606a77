'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/ui/shadcn/table';

import { useWebViewRouter } from '../model/useWebViewRouter';
import { CommonPagination } from './CommonPagination';

interface BaseData {
  id: string;
}

interface DataTableProps<TData extends BaseData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data?: {
    data: TData[];
    meta: {
      currentPage?: number;
      lastPage?: number;
      next?: boolean;
      perPage?: number;
      prev?: boolean;
      total: number;
    };
  };
  pagination?: boolean;
  className?: string;
  isDisclosure?: boolean;
}

export function DataTable<TData extends BaseData, TValue>({
  columns,
  data,
  pagination = true,
  className = 'py-10 px-6 sm:px-10',
  isDisclosure = false,
}: DataTableProps<TData, TValue>) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const page = searchParams.get('page') || 1;
  const { routerPush } = useWebViewRouter();

  const table = useReactTable<TData>({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleRowClick = (id: string) => {
    if (isDisclosure) {
      router.replace(`${pathname}?disclosureId=${id}`, {
        scroll: false,
      });
      return;
    } else {
      routerPush(`${pathname}/${id}`);
    }
  };

  return (
    <div className="space-y-6">
      {pagination && (
        <p className="">
          전체 <span className="fond-bold text-primary-500"> {data?.meta.total || 0}</span> 건
        </p>
      )}
      <div className="clear-start border-t border-t-gray-900 sm:!mb-12 ml:!mb-10">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="h-14 bg-gray-50 px-10">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="text-center"
                      style={{ width: `${header.getSize()}px` }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className={`${pagination && 'border-b border-gray-300'}`}>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => handleRowClick(row.original.id)}
                  className="cursor-pointer"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className={`${className} sm:py-8 ml:py-10`}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  데이터가 없습니다.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {pagination && (
        <CommonPagination
          page={Number(page)}
          pageSize={10}
          totalCount={data?.meta.total || 0}
          scroll={false}
        />
      )}
    </div>
  );
}
