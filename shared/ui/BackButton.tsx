'use client';

import React from 'react';

import { useWebViewRouter } from '../model/useWebViewRouter';
import { Button } from './shadcn/button';

interface BackButtonProps {
  href?: string;
  className?: string;
  text?: string;
  scroll?: boolean;
}

export const BackButton = ({
  href,
  className,
  text = '목록으로 돌아가기',
  scroll = true,
}: BackButtonProps) => {
  const { routerBack, routerPush } = useWebViewRouter();

  const handleClick = () => {
    if (!href) {
      routerBack();
    } else {
      routerPush(href, scroll);
    }
  };

  return (
    <Button
      className={`h-[44px] w-[171px] rounded-lg border border-gray-900 text-sm font-semibold shadow-none sm:h-[60px] sm:text-base ${className}`}
      onClick={handleClick}
    >
      {text}
    </Button>
  );
};
