'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

import { cn } from '../lib/utils';
import { useWebViewRouter } from '../model/useWebViewRouter';
import { FallbackImage } from './FallbackImage';
import { SecondaryButton } from './SecondaryButton';

interface EmptyListProps {
  title: string;
  buttonText?: string;
  className?: string;
  href?: string;
}

export const EmptyList = ({ title, buttonText, className, href }: EmptyListProps) => {
  const { routerPush, routerBack } = useWebViewRouter();

  return (
    <div
      className={cn(
        'flex h-[400px] flex-col items-center justify-center gap-8 sm:h-auto sm:justify-start sm:gap-14',
        className,
      )}
    >
      <div className="flex flex-col items-center gap-4">
        <FallbackImage
          src="/icons/img_empty.png"
          alt="empty-list"
          width={64}
          height={64}
          className="sm:block"
        />
        <p className="text-sm text-gray-400 sm:text-lg">{title}</p>
      </div>
      {buttonText && (
        <SecondaryButton
          onClick={() => {
            if (href) {
              routerPush(href);
            } else {
              routerBack();
            }
          }}
          className="h-[44px] w-[85px] sm:block sm:h-[60px] sm:w-[112px]"
          text={buttonText}
        />
      )}
    </div>
  );
};
