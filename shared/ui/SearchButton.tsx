import { MagnifyingGlassIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Button } from './shadcn/button';

interface SearchButtonProps {
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  text?: string;
  disabled?: boolean;
}

export const SearchButton = ({
  className,
  onClick,
  type = 'submit',
  text,
  disabled,
}: SearchButtonProps) => {
  return (
    <Button
      className={`h-[44px] min-w-[60px] rounded-lg bg-gray-900 text-white sm:h-12 ${className}`}
      onClick={onClick}
      type={type}
      disabled={disabled}
    >
      <MagnifyingGlassIcon className="!h-4 !w-4 sm:!h-5 sm:!w-5" />
      {text}
    </Button>
  );
};
