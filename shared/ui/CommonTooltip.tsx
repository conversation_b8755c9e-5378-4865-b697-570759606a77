import { InformationCircleIcon } from '@heroicons/react/24/solid';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/ui/shadcn/tooltip';

interface CommonTooltipProps {
  title: string;
  description: string;
  side?: 'top' | 'bottom' | 'left' | 'right';
}

export const CommonTooltip = ({ title, description, side = 'bottom' }: CommonTooltipProps) => {
  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger>
          <p className="flex items-center gap-1 text-gray-600">
            {title}
            <InformationCircleIcon className="h-4 w-4 text-gray-500" />
          </p>
        </TooltipTrigger>
        <TooltipContent
          side={side}
          className="max-w-[375px] bg-gray-900 px-[22px] py-4 text-sm text-white"
        >
          <p>
            {description}
            <a href="mailto:<EMAIL>"><EMAIL></a> 또는 고객지원{`>`}
            문의하기를 통해 문의해 주세요.
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
