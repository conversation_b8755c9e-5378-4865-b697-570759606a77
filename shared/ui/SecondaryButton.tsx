import React from 'react';

import { Button } from './shadcn/button';

interface PrimaryButtonProps {
  className?: string;
  onClick?: () => void;
  text?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

export const SecondaryButton = ({
  className,
  onClick,
  text = '확인',
  type,
  disabled,
}: PrimaryButtonProps) => {
  return (
    <Button
      className={`h-[44px] rounded-lg border border-gray-900 bg-white text-base font-semibold disabled:border-gray-300 disabled:text-gray-400 sm:h-12 sm:text-sm ${className}`}
      onClick={onClick}
      type={type}
      disabled={disabled}
    >
      {text}
    </Button>
  );
};
