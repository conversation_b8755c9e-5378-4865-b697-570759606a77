import React from 'react';

interface CommonTabButtonProps {
  label: string;
  value: string;
  isSelected: boolean;
  handleAction: (value: any) => void;
}

export const CommonTabButton = ({
  label,
  value,
  isSelected,
  handleAction,
}: CommonTabButtonProps) => {
  return (
    <button
      type="button"
      onClick={() => handleAction(value)}
      key={value}
      className={`h-9 rounded-[40px] px-5 text-xs font-semibold hover:bg-primary-500 hover:text-white sm:h-12 sm:min-w-[84px] sm:text-sm ${
        isSelected ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-700'
      }`}
    >
      {label}
    </button>
  );
};
