'use client';

import { logClientError } from '@artbloc/next-js-logger/client';
import { useQueryClient } from '@tanstack/react-query';
import { signOut } from 'next-auth/react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { isAdult } from '@/features/auth/lib/isAdult';
import { MinorRestrictionDialog } from '@/features/auth/ui/MinorRestrictionDialog';
import { useIdentityVerification } from '@/features/identity/model/useIdentityVerification';
import { RequiredIdentityDialog } from '@/features/identity/ui/RequiredIdentityDialog';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { registerKyc } from '@/entities/auth/api/registerKyc';
import { KycStatus } from '@/entities/users/types';

import { WebViewMessage } from '../lib/webViewMessage';
import { useToast } from '../model/useToast';
import { useVisibility } from '../model/useVisibility';
import { useWebViewRouter } from '../model/useWebViewRouter';

export const KycVefiryProvider = ({ children }: { children: React.ReactNode }) => {
  const { user } = useFetchUser();
  const { isVisible, toggleVisibility, closeToggle } = useVisibility();
  const {
    isVisible: isMinorRestrictionDialogVisible,
    toggleVisibility: toggleMinorRestrictionDialogVisibility,
  } = useVisibility();
  const queryClient = useQueryClient();
  const { successToast, errorToast } = useToast();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { routerPush, isApp } = useWebViewRouter();

  const { verifyIdentity } = useIdentityVerification();

  useEffect(() => {
    if (!user) return;

    if (user.kycStatus === KycStatus.NON_KYC) {
      toggleVisibility();
    }
  }, [user, pathname]);

  useEffect(() => {
    const identityVerificationId = searchParams.get('identityVerificationId'); // 포트원 인증 고유번호
    const identityVerificationTxId = searchParams.get('identityVerificationTxId'); // 요청 시 설정한 유니크 ID
    const transactionType = searchParams.get('transactionType'); // 인증 성공 여부
    const state = searchParams.get('state');

    if (state === 'verify-account-change' || state === 'verify-account-register') return;

    if (identityVerificationId && identityVerificationTxId) {
      fetch('/api/identity/verify', {
        method: 'POST',
        body: JSON.stringify({ identityVerificationId, identityVerificationTxId, transactionType }),
      })
        .then((res) => res.json())
        .then(async ({ data }) => {
          const verifiedResult = data.verifiedCustomer;
          if (verifiedResult) {
            const adult = isAdult(verifiedResult.birthDate);
            if (!adult) {
              toggleMinorRestrictionDialogVisibility();
              return;
            }

            await registerKyc({
              ci: verifiedResult.ci,
              result: data.status,
              name: verifiedResult.name,
              birthDate: verifiedResult.birthDate,
            });

            successToast({
              title: '본인인증 성공',
            });
            closeToggle();
            await queryClient.invalidateQueries({ queryKey: queries.user.profile().queryKey });
            router.replace(pathname);
          }
        })
        .catch((error) => {
          logClientError('kyc-verify-error', error);
          errorToast({
            title: '본인인증 실패',
          });
        });
    }
  }, [searchParams]);

  const handleIdentityVerification = async () => {
    toggleVisibility();
    const verifiedResult = await verifyIdentity();

    if (verifiedResult) {
      const { verifiedCustomer } = verifiedResult;
      try {
        const adult = isAdult(verifiedCustomer.birthDate);
        if (!adult) {
          toggleMinorRestrictionDialogVisibility();
          return;
        }

        await registerKyc({
          ci: verifiedCustomer.ci,
          result: verifiedResult.status,
          name: verifiedCustomer.name,
          birthDate: verifiedCustomer.birthDate,
        });

        await queryClient.invalidateQueries({ queryKey: queries.user.profile().queryKey });
        successToast({
          title: '본인인증 성공',
        });
        closeToggle();
        router.replace(pathname);
      } catch (error) {
        errorToast({
          title: '본인인증 실패',
        });
      }
    }
  };

  const handleOpen = async () => {
    toggleVisibility();
    if (isApp) {
      await signOut();
      WebViewMessage('logout', {});
      routerPush('/');
    } else {
      await signOut({ redirect: true, callbackUrl: '/' });
    }
  };

  return (
    <>
      {children}
      <RequiredIdentityDialog
        isOpen={isVisible}
        handleOpen={handleOpen}
        handleVerify={handleIdentityVerification}
      />
      <MinorRestrictionDialog
        isOpen={isMinorRestrictionDialogVisible}
        handleOpen={toggleMinorRestrictionDialogVisibility}
      />
    </>
  );
};
