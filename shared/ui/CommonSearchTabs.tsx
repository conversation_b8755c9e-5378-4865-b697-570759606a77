'use client';

import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';

interface CommonSearchTabsProps {
  items: {
    label: string;
    value: string;
  }[];
  className?: string;
  onChange: (value: string) => void;
  value: string;
}

export const CommonSearchTabs = ({ items, className, onChange, value }: CommonSearchTabsProps) => {
  return (
    <TabGroup className="w-full sm:w-auto">
      <TabList className={`flex rounded-lg border border-gray-300 ${className}`}>
        {items.map((item, index) => (
          <Tab
            key={item.value}
            className={`h-full flex-1 text-sm font-semibold ${value === item.value ? 'bg-gray-900 text-white' : ''} ${index === 0 ? 'rounded-l-lg' : 'border-l border-gray-300'} ${index === items.length - 1 && 'rounded-r-lg'}`}
            onClick={() => onChange(item.value)}
          >
            {item.label}
          </Tab>
        ))}
      </TabList>
    </TabGroup>
  );
};
