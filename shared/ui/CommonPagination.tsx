'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { type ReactNode, useCallback } from 'react';

import { cn } from '@/shared/lib/utils';

import { useWebViewRouter } from '../model/useWebViewRouter';
import { Button } from './shadcn/button';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from './shadcn/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './shadcn/select';

export interface PaginationWithLinksProps {
  pageSizeSelectOptions?: {
    pageSizeSearchParam?: string;
    pageSizeOptions: number[];
  };
  totalCount: number;
  pageSize: number;
  page: number;
  pageSearchParam?: string;
  params?: boolean;
  setPage?: (newPage: number) => void;
  scroll?: boolean;
}

export function CommonPagination({
  pageSizeSelectOptions,
  pageSize,
  totalCount,
  page,
  pageSearchParam,
  params = true,
  setPage,
  scroll = true,
}: PaginationWithLinksProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const totalPageCount = Math.ceil(totalCount / pageSize);

  const handlePage = (newPage: number) => {
    if (params) {
      const key = pageSearchParam || 'page';
      if (!searchParams)
        return router.replace(`${pathname}?${key}=${newPage}`, {
          scroll: false,
        });
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set(key, String(newPage));
      return router.replace(`${pathname}?${newSearchParams.toString()}`, {
        scroll: false,
      });
    } else {
      setPage?.(newPage);
    }
  };

  const navToPageSize = useCallback(
    (newPageSize: number) => {
      const key = pageSizeSelectOptions?.pageSizeSearchParam || 'pageSize';
      const newSearchParams = new URLSearchParams(searchParams || undefined);
      newSearchParams.set(key, String(newPageSize));
      router.replace(`${pathname}?${newSearchParams.toString()}`, {
        scroll: false,
      });
    },
    [searchParams, pathname],
  );

  const renderPageNumbers = () => {
    const items: ReactNode[] = [];
    const pagesPerGroup = 5; // 한 그룹당 페이지 수
    const totalGroups = Math.ceil(totalPageCount / pagesPerGroup); // 전체 그룹 수
    const currentGroup = Math.ceil(page / pagesPerGroup); // 현재 페이지가 속한 그룹

    // 현재 그룹의 시작 페이지와 끝 페이지 계산
    const startPage = (currentGroup - 1) * pagesPerGroup + 1;
    const endPage = Math.min(currentGroup * pagesPerGroup, totalPageCount);

    // 현재 그룹의 페이지 번호만 표시
    for (let i = startPage; i <= endPage; i++) {
      const isLastInGroup = i === endPage;

      items.push(
        <PaginationItem key={i}>
          <Button
            onClick={() => handlePage(i)}
            className={cn(
              'h-10 w-10 rounded-none sm:h-12 sm:w-12',
              page === i && 'bg-gray-900 text-white',
              !isLastInGroup && 'border-r border-gray-900', // 마지막 페이지가 아닌 경우에만 border-r 추가
            )}
          >
            {i}
          </Button>
        </PaginationItem>,
      );
    }

    return items;
  };

  return (
    <div className="flex w-full flex-col items-center gap-3 sm:flex-row">
      {pageSizeSelectOptions && (
        <div className="flex flex-1 flex-col gap-4">
          <SelectRowsPerPage
            options={pageSizeSelectOptions.pageSizeOptions}
            setPageSize={navToPageSize}
            pageSize={pageSize}
          />
        </div>
      )}
      <Pagination className={cn({ 'sm:justify-end': pageSizeSelectOptions })}>
        <PaginationContent className="h-10 gap-0 rounded-lg border border-gray-900 sm:h-12">
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePage(Math.max(page - 1, 1))}
              aria-disabled={page === 1}
              tabIndex={page === 1 ? -1 : undefined}
              className={`h-10 w-10 cursor-pointer rounded-none border-r border-gray-900 sm:h-12 sm:w-12 ${
                page === 1 ? 'pointer-events-none opacity-50' : undefined
              }`}
            />
          </PaginationItem>
          {renderPageNumbers()}
          <PaginationItem>
            <PaginationNext
              onClick={() => handlePage(Math.min(page + 1, totalPageCount))}
              aria-disabled={page === totalPageCount}
              tabIndex={page === totalPageCount ? -1 : undefined}
              className={`h-10 w-10 cursor-pointer rounded-none border-l border-gray-900 sm:h-12 sm:w-12 ${
                page === totalPageCount ? 'pointer-events-none opacity-50' : undefined
              }`}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}

function SelectRowsPerPage({
  options,
  setPageSize,
  pageSize,
}: {
  options: number[];
  setPageSize: (newSize: number) => void;
  pageSize: number;
}) {
  return (
    <div className="flex items-center gap-4">
      <span className="whitespace-nowrap text-sm">Rows per page</span>

      <Select value={String(pageSize)} onValueChange={(value) => setPageSize(Number(value))}>
        <SelectTrigger>
          <SelectValue placeholder="Select page size">{String(pageSize)}</SelectValue>
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option} value={String(option)}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
