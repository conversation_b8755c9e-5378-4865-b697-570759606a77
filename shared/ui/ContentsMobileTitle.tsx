'use client';

import React from 'react';

import { useWebViewRouter } from '../model/useWebViewRouter';

interface ContentsMobileTitleProps {
  title: string;
  href: string;
}

export const ContentsMobileTitle = ({ title, href }: ContentsMobileTitleProps) => {
  const { routerPush } = useWebViewRouter();
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-xl font-semibold">{title}</h3>
      <button onClick={() => routerPush(href)} className="text-sm font-semibold text-gray-500">
        전체보기
      </button>
    </div>
  );
};
