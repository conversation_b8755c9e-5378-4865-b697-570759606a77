import dayjs from 'dayjs';
import React from 'react';

import { cn } from '../lib/utils';

interface NewBadgeProps {
  size: 'sm' | 'md' | 'lg';
  date: string;
  className?: string;
}

export const NewBadge = ({ size, date, className }: NewBadgeProps) => {
  const isWithinWeek = dayjs().diff(dayjs(date), 'day') <= 7;

  if (!isWithinWeek) return null;

  if (size === 'sm') {
    return (
      <span
        className={cn(
          'flex h-5 w-5 items-center justify-center rounded-[10px] bg-pink-400 text-xs font-semibold text-white',
          className,
        )}
      >
        N
      </span>
    );
  }
  if (size === 'md') {
    return (
      <span
        className={cn(
          'flex h-5 w-10 items-center justify-center rounded-[20px] bg-pink-400 text-xs font-semibold text-white',
          className,
        )}
      >
        NEW
      </span>
    );
  }
  if (size === 'lg') {
    return (
      <span
        className={cn(
          'flex h-7 w-12 items-center justify-center rounded-md bg-pink-400 text-xs font-semibold text-white',
          className,
        )}
      >
        NEW
      </span>
    );
  }
};
