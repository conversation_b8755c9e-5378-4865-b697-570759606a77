import { motion } from 'framer-motion';
import React from 'react';

interface AuthFormContainerProps {
  children: React.ReactNode;
}

export const AuthFormContainer = ({ children }: AuthFormContainerProps) => {
  return (
    <motion.div
      className="flex w-full max-w-[640px] flex-col justify-between rounded-[20px] border-gray-300 px-6 py-4 sm:h-auto sm:border sm:px-[100px] sm:py-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
};
