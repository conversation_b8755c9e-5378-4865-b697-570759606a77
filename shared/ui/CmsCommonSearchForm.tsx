'use client';

import { defaultSearchOptions } from '@/shared/config/searchOptions';
import { CommonSelect } from '@/shared/ui/CommonSelect';
import { CustomDayPicker } from '@/shared/ui/CustomDayPicker';
import { SearchButton } from '@/shared/ui/SearchButton';
import { Input } from '@/shared/ui/shadcn/input';

import { useCmsCommonSearch } from '../model/useCmsCommonSearch';

interface CmsCommonSearchFormProps {
  searchOptions: { label: string; value: string }[];
}

export const CmsCommonSearchForm = ({ searchOptions }: CmsCommonSearchFormProps) => {
  const {
    searchFormState,
    handleformState,
    handleSearch,
    handleSearchType,
    handleStartDateChange,
    handleEndDateChange,
    handleDatePickerClick,
    isDatePickerOpen,
  } = useCmsCommonSearch();

  return (
    <form
      className="mb-16 flex flex-col justify-between gap-2 ml:flex-row ml:gap-0"
      onSubmit={handleSearch}
    >
      <div className="relative flex h-[44px] w-full items-center justify-between gap-4 rounded-lg border border-gray-300 px-4 py-[10px] sm:static sm:h-12 sm:w-[384px]">
        <CustomDayPicker
          isOpen={isDatePickerOpen.startDate}
          handleDatePickerClick={handleDatePickerClick}
          type="startDate"
          selectedDate={searchFormState.startDate}
          handleDateChange={handleStartDateChange}
          label="시작 날짜"
          maxDate={searchFormState.endDate}
        />
        <p>~</p>

        <CustomDayPicker
          isOpen={isDatePickerOpen.endDate}
          handleDatePickerClick={handleDatePickerClick}
          type="endDate"
          selectedDate={searchFormState.endDate}
          handleDateChange={handleEndDateChange}
          label="종료 날짜"
          minDate={searchFormState.startDate}
        />
      </div>
      <div className="flex flex-col gap-2 sm:flex-row ml:w-auto">
        <CommonSelect
          value={searchFormState.searchType}
          onChange={handleSearchType}
          options={searchOptions}
          className="sm:w-[180px]"
        />
        <div className="flex gap-2">
          <Input
            value={searchFormState.keyword}
            name="keyword"
            onChange={handleformState}
            className="h-[44px] rounded-lg border border-gray-300 sm:h-12 sm:w-full sm:min-w-[300px]"
            placeholder="검색어를 입력해주세요"
          />
          <SearchButton />
        </div>
      </div>
    </form>
  );
};
