import { motion } from 'framer-motion';
import React from 'react';

interface CommonMotionProviderProps {
  children: React.ReactNode;
  className?: string;
}

export const CommonMotionProvider = ({ children, className }: CommonMotionProviderProps) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      {children}
    </motion.div>
  );
};
