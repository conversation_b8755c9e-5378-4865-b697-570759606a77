'use client';

import { ArrowLeftIcon, XMarkIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useWebViewRouter } from '../model/useWebViewRouter';

export const AuthMobileBackButton = ({ isFirstStep }: { isFirstStep: boolean }) => {
  const { routerBack, routerPush } = useWebViewRouter();

  const handleBack = () => {
    if (isFirstStep) {
      routerPush('/sign-in');
    } else {
      routerBack();
    }
  };

  return (
    <div className={`flex h-[50px] ${isFirstStep && 'justify-end'} px-6 pt-2 sm:hidden`}>
      <button onClick={handleBack}>
        {isFirstStep ? (
          <XMarkIcon className="h-6 w-6" strokeWidth={2} />
        ) : (
          <ArrowLeftIcon className="h-6 w-6" strokeWidth={2} />
        )}
      </button>
    </div>
  );
};
