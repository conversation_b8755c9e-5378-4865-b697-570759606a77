'use client';

import React from 'react';
import DaumPostcode from 'react-daum-postcode';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/shadcn/dialog';

interface Props {
  isOpen: boolean;
  close: () => void;
  setValue: (address: string) => void;
}

export const PostCodeDialog = ({ isOpen, close, setValue }: Props) => {
  const style = {
    width: '350px',
    height: '500px',
    border: '1.4px solid #333333',
  };

  const completeHandler = (data: any) => {
    setValue(data.address);
    close();
  };

  return (
    <Dialog open={isOpen} onOpenChange={close}>
      <DialogContent className="h-[500px] w-[350px] border-none">
        <DialogHeader>
          <DialogTitle />
        </DialogHeader>
        <DaumPostcode style={style} onComplete={completeHandler} />
      </DialogContent>
    </Dialog>
  );
};
