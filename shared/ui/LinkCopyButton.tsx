'use client';

import { LinkIcon } from '@heroicons/react/24/outline';
import React from 'react';

import { useClipboard } from '../model/useClipboard';
import { Button } from './shadcn/button';

interface LinkCopyButtonProps {
  url?: string;
}

export const LinkCopyButton = ({ url }: LinkCopyButtonProps) => {
  const { handleCopy } = useClipboard();

  return (
    <Button
      className="!h-10 !w-10 rounded-full border border-gray-300 shadow-none"
      onClick={() => handleCopy(url)}
    >
      <LinkIcon />
    </Button>
  );
};
