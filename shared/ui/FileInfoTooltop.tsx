import { InformationCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './shadcn/tooltip';

export const FileInfoTooltop = () => {
  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger asChild>
          <InformationCircleIcon className="mt-[1px] h-5 w-5" />
        </TooltipTrigger>
        <TooltipContent side="bottom" className="bg-gray-900 px-[22px] py-4 text-sm text-white">
          <p>PNG, JPG, PDF 형식의 파일만 등록이 가능합니다. (5MB)</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
