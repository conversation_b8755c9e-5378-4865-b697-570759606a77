'use client';

import { ArrowPathIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Button } from './shadcn/button';

export const FormResetButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Button
      type="button"
      className="h-[44px] w-full border border-gray-900 bg-white font-semibold shadow-none sm:h-12 sm:w-[110px]"
      onClick={onClick}
    >
      <ArrowPathIcon className="!h-4 !w-4 sm:!h-5 sm:!w-5" />
      초기화
    </Button>
  );
};
