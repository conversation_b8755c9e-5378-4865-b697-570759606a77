'use client';

import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/solid';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from './shadcn/form';
import { Input } from './shadcn/input';

interface PasswordInputFieldProps {
  form: UseFormReturn<any>;
  name: string;
  placeholder?: string;
  label?: string;
  className?: string;
}

export const PasswordInputField = ({
  form,
  name,
  placeholder = '비밀번호 입력',
  label,
  className,
}: PasswordInputFieldProps) => {
  const [isShowPassword, setIsShowPassword] = useState(false);

  return (
    <FormField
      control={form.control}
      name={name}
      render={() => (
        <FormItem className="w-full space-y-3 ml:w-auto">
          {label && <FormLabel className="text-base font-semibold">{label}</FormLabel>}
          <FormControl>
            <div className="relative">
              <Input
                {...form.register(name)}
                placeholder={placeholder}
                className={`h-[44px] sm:h-12 ${className} ${form.formState.errors[name] && 'border-2 border-red-500'} `}
                type={isShowPassword ? 'text' : 'password'}
              />
              <button
                type="button"
                onClick={() => setIsShowPassword(!isShowPassword)}
                className={`absolute right-3 top-1/2 -translate-y-1/2 shadow-none`}
              >
                {isShowPassword ? (
                  <EyeSlashIcon className="h-4 w-4 sm:h-6 sm:w-6" />
                ) : (
                  <EyeIcon className="h-4 w-4 sm:h-6 sm:w-6" />
                )}
              </button>
            </div>
          </FormControl>
          <FormMessage className="font-semibold" />
        </FormItem>
      )}
    />
  );
};
