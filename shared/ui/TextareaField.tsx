import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from './shadcn/form';
import { Textarea } from './shadcn/textarea';

interface TextareaFieldProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  className?: string;
  requiredText?: boolean;
}

export const TextareaField = ({
  form,
  name,
  label,
  placeholder,
  requiredText,
  className,
}: TextareaFieldProps) => {
  const { control, register, formState } = form;

  return (
    <FormField
      control={control}
      name="description"
      render={() => (
        <FormItem className={className}>
          {label && (
            <FormLabel className="font-semibold sm:text-base">
              {label}
              {requiredText && <strong className="font-semibold text-primary-500"> (필수)</strong>}
            </FormLabel>
          )}
          <FormControl>
            <Textarea
              {...register(name)}
              placeholder={placeholder}
              className={`h-[146px] resize-none placeholder:text-sm sm:placeholder:text-base ${formState.errors[name] && 'border-2 border-red-500'}`}
            />
          </FormControl>
          {formState.errors[name] && (
            <FormMessage className="text-xs font-semibold sm:text-sm">
              {formState.errors[name]?.message as string}
            </FormMessage>
          )}
        </FormItem>
      )}
    />
  );
};
