'use client';

import Image, { ImageProps } from 'next/image';
import React, { useState } from 'react';

interface FallbackImageProps extends Omit<ImageProps, 'src'> {
  src?: string;
  fallbackSrc?: string;
}

const blurDataUrl =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJoAAAB2CAYAAAAweYruAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAE1SURBVHgB7dIxAQAgEAAhtX/Yb6ApvAkysGfmLvjsLAiIRkI0EqKREI2EaCREIyEaCdFIiEZCNBKikRCNhGgkRCMhGgnRSIhGQjQSopEQjYRoJEQjIRoJ0UiIRkI0EqKREI2EaCREIyEaCdFIiEZCNBKikRCNhGgkRCMhGgnRSIhGQjQSopEQjYRoJEQjIRoJ0UiIRkI0EqKREI2EaCREIyEaCdFIiEZCNBKikRCNhGgkRCMhGgnRSIhGQjQSopEQjYRoJEQjIRoJ0UiIRkI0EqKREI2EaCREIyEaCdFIiEZCNBKikRCNhGgkRCMhGgnRSIhGQjQSopEQjYRoJEQjIRoJ0UiIRkI0EqKREI2EaCREIyEaCdFIiEZCNBKikRCNhGgkRCMhGgnRSIhGQjQSopEQjcQDmXkEu5jIEjMAAAAASUVORK5CYII=';

export const FallbackImage = ({
  src,
  alt,
  width,
  height,
  fallbackSrc = '/images/exception_image.png',
  ...props
}: FallbackImageProps) => {
  const [imgError, setImgError] = useState(false);

  const handleError = () => setImgError(true);

  const imageSrc = imgError || !src ? fallbackSrc : src;

  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      onError={!imgError ? handleError : undefined}
      placeholder="blur"
      blurDataURL={blurDataUrl}
      {...props}
    />
  );
};
