import React from 'react';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';

import { FallbackImage } from './FallbackImage';

interface CompleteWidgetProps {
  children: React.ReactNode;
  titleBefore: string;
  titleHighlight?: string;
  titleAfter: string;
  description?: string;
  imageSrc: string;
}

export const CompleteWidget = ({
  children,
  titleBefore,
  titleHighlight,
  titleAfter,
  description,
  imageSrc,
}: CompleteWidgetProps) => {
  return (
    <AuthFormContainer>
      <div className="flex h-[calc(100dvh-80px)] flex-col items-center justify-center sm:h-auto sm:justify-start">
        <FallbackImage
          src={imageSrc}
          alt="complete"
          width={64}
          height={64}
          className="h-16 w-16 sm:h-20 sm:w-20"
        />

        <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">
          {titleBefore}
          {titleHighlight && <span className="text-primary-500">{titleHighlight}</span>}
          {titleAfter}
        </h3>
        {description && (
          <h6 className="whitespace-pre-line text-center text-base sm:text-lg">{description}</h6>
        )}
      </div>
      {children}
    </AuthFormContainer>
  );
};
