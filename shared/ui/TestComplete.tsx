'use client';

import React from 'react';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

import { FallbackImage } from './FallbackImage';

interface TestCompleteProps {
  goToCallbackUrl: () => void;
  title: string;
  description: string;
  isOnboarding?: boolean;
  imageSrc?: string;
  buttonText?: string;
}

export const TestComplete = ({
  goToCallbackUrl,
  title,
  description,
  isOnboarding = false,
  imageSrc = '/icons/complete.png',
  buttonText = '확인',
}: TestCompleteProps) => {
  return (
    <CommonMotionProvider
      className={`mx-auto flex ${isOnboarding ? 'h-[calc(100dvh-200px)]' : 'h-dvh'} max-w-screen-test flex-col justify-center sm:mt-[200px] sm:h-auto sm:items-center sm:justify-start`}
    >
      <div className="flex flex-col items-center justify-center">
        <FallbackImage
          src={imageSrc}
          alt="complete"
          width={64}
          height={64}
          className="h-16 w-16 sm:h-20 sm:w-20"
        />

        <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">{title}</h3>
        <h6 className="whitespace-pre-line text-center text-base sm:text-lg">{description}</h6>
      </div>

      <div className="fixed bottom-0 left-0 w-full px-6 py-2 sm:static sm:hidden sm:justify-center">
        <PrimaryButton
          text={buttonText}
          className="!h-12 w-full text-base sm:mb-[280px] sm:mt-16 sm:w-40"
          onClick={goToCallbackUrl}
        />
      </div>

      {!isOnboarding && (
        <PrimaryButton
          text={buttonText}
          className="hidden w-full sm:mb-[280px] sm:mt-16 sm:block sm:w-40"
          onClick={goToCallbackUrl}
        />
      )}
    </CommonMotionProvider>
  );
};
