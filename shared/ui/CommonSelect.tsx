'use client';

import { CheckIcon } from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';
import React, { useEffect, useRef } from 'react';

import { useVisibility } from '../model/useVisibility';
import { ScrollArea } from './shadcn/scroll-area';

interface CommonSelectProps {
  placeholder?: string;
  value: any;
  onChange: (value: string) => void;
  options: {
    label: string;
    value: any;
  }[];
  className?: string;
  props?: React.HTMLAttributes<HTMLButtonElement>;
}

export const CommonSelect = ({
  placeholder,
  value,
  onChange,
  options,
  className,
  ...props
}: CommonSelectProps) => {
  const { isVisible, toggleVisibility } = useVisibility();
  const selectRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
      if (isVisible) {
        toggleVisibility();
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible]);

  const dataTestId = (props as any)?.['data-testid'];

  return (
    <div className="relative" ref={selectRef}>
      <div className="flex w-full flex-1 items-center justify-between">
        <button
          type="button"
          {...props}
          onClick={toggleVisibility}
          className={`flex h-[44px] w-full min-w-[180px] items-center justify-start rounded-lg border border-gray-300 bg-white px-4 text-base sm:h-12 ${
            value ? 'text-gray-900' : 'text-gray-500'
          } ${className}`}
        >
          {value ? (
            <div className="flex w-full items-center justify-between">
              {options.find((option) => option.value === value)?.label}
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            </div>
          ) : (
            <div className="flex w-full items-center justify-between">
              {placeholder}
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            </div>
          )}
        </button>
      </div>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.1 }}
          className="absolute left-0 top-14 z-40 w-full min-w-[180px] rounded-md border border-gray-300 bg-white p-1 shadow"
        >
          <ScrollArea className="flex max-h-[200px] flex-col items-start">
            {options.map((option) => {
              const isSelected = value === option.value;
              return (
                <button
                  {...(dataTestId && {
                    'data-testid': `${dataTestId}-option-${option.value}`,
                  })}
                  key={option.value}
                  type="button"
                  onClick={() => {
                    onChange(option.value);
                    toggleVisibility();
                  }}
                  className="flex w-full items-center justify-between gap-2 rounded p-2 hover:bg-gray-100"
                >
                  {option.label}
                  {isSelected && <CheckIcon key={option.value} className="h-4 w-4" />}
                </button>
              );
            })}
          </ScrollArea>
        </motion.div>
      )}
    </div>
  );
};
