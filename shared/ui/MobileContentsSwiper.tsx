import React from 'react';
import 'swiper/css';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

import { ContentsMobileTitle } from './ContentsMobileTitle';

interface HorizontalSwiperProps<T> {
  title: string;
  href: string;
  items: T[];
  renderItem: (item: T) => React.ReactNode;
  itemWidth?: string;
  className?: string;
}

export function MobileContentsSwiper<T>({
  title,
  href,
  items,
  renderItem,
  itemWidth = '158px',
  className = '',
}: HorizontalSwiperProps<T>) {
  return (
    <div className={`space-y-3 ${className}`}>
      <ContentsMobileTitle title={title} href={href} />
      <div className="flex h-[180px] overflow-x-auto">
        <Swiper spaceBetween={10} slidesPerView="auto" modules={[Pagination]}>
          {items.map((item, index) => (
            <SwiperSlide
              key={index}
              style={{
                width: itemWidth,
              }}
            >
              {renderItem(item)}
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
