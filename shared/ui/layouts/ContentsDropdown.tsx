import { motion, useIsPresent } from 'framer-motion';
import Link from 'next/link';
import React from 'react';

import { useVisibility } from '@/shared/model/useVisibility';

export const ContentsDropdown = () => {
  const isPresent = useIsPresent();
  const { isVisible, openToggle } = useVisibility();

  return (
    <motion.div
      key="contents-dropdown"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.3,
        ease: 'easeOut',
      }}
      onAnimationComplete={() => {
        openToggle();
      }}
      className={`fixed left-0 z-20 w-screen border-b border-gray-300 bg-white py-6 ${
        isPresent && isVisible && 'border-t'
      }`}
    >
      <div className="mx-auto max-w-screen-lg">
        <ul className="rounded pl-[265px] ml:pl-[230px] lg:pl-[230px]">
          <li className="flex items-center gap-[6px] py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/contents/notices">공지사항</Link>
          </li>
          {/* <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/contents/disclosures">공시</Link>
          </li> */}
          <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/contents/news">언론자료</Link>
          </li>
          <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/contents/curations">콘텐츠</Link>
          </li>
          <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/contents/events">이벤트</Link>
          </li>
        </ul>
      </div>
    </motion.div>
  );
};
