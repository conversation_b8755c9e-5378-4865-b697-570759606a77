'use client';

import { DocumentTextIcon, HomeIcon, ShoppingBagIcon, UserIcon } from '@heroicons/react/24/solid';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useGNBStore } from '@/shared/store/useGNBStore';

export const MobileBottomGnb = () => {
  const pathname = usePathname();
  const router = useRouter();
  // const [isMounted, setIsMounted] = useState(false);

  const isActive = (path: string) => {
    switch (path) {
      case '/':
        return pathname === '/';
      case '/subscriptions':
        return pathname.includes(path);
      case '/contents':
        return pathname.includes(path);
      case '/user':
        return pathname.includes(path);
      default:
        return false;
    }
  };

  const { isGNBVisible } = useGNBStore();

  // // Hydration 안전장치
  // useEffect(() => {
  //   setIsMounted(true);
  // }, []);

  // // SSR 중에는 렌더링하지 않음
  // if (!isMounted) {
  //   return null;
  // }

  return (
    <nav
      className={`safe-area fixed bottom-0 left-0 right-0 z-30 flex w-full items-center justify-between border-t border-gray-100 bg-white sm:hidden ${
        isGNBVisible ? 'block' : 'hidden'
      }`}
      style={{
        height: 'calc(4rem + env(safe-area-inset-bottom))',
        paddingBottom: 'env(safe-area-inset-bottom)',
      }}
    >
      <ul className="grid w-full grid-cols-4">
        <BottomGnbItem
          onClick={() => router.replace('/')}
          icon={<HomeIcon className={`h-6 w-6 ${!isActive('/') && 'text-gray-300'}`} />}
          label="홈"
          isActive={!isActive('/')}
        />
        <BottomGnbItem
          onClick={() => router.replace('/subscriptions')}
          icon={
            <ShoppingBagIcon
              className={`h-6 w-6 ${!isActive('/subscriptions') && 'text-gray-300'}`}
            />
          }
          label="투자"
          isActive={!isActive('/subscriptions')}
        />
        <BottomGnbItem
          onClick={() => router.replace('/contents')}
          icon={
            <DocumentTextIcon className={`h-6 w-6 ${!isActive('/contents') && 'text-gray-300'}`} />
          }
          label="소식"
          isActive={!isActive('/contents')}
        />
        <BottomGnbItem
          onClick={() => router.replace('/user')}
          icon={<UserIcon className={`h-6 w-6 ${!isActive('/user') && 'text-gray-300'}`} />}
          label="MY"
          isActive={!isActive('/user')}
        />
      </ul>
    </nav>
  );
};

const BottomGnbItem = ({
  icon,
  label,
  isActive,
  onClick,
}: {
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick: () => void;
}) => {
  return (
    <li
      className={`flex flex-col items-center gap-1 ${isActive && 'text-gray-300'}`}
      onClick={onClick}
    >
      {icon}
      <p className="font-semiold text-xs">{label}</p>
    </li>
  );
};
