import { ArrowLeftIcon } from '@heroicons/react/24/solid';

import { Notification } from '@/widgets/notifications/ui/Notification';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useHeaderStore } from '@/shared/store/useHeaderStore';

export const MobileSubTitleHeader = () => {
  const { routerBack } = useWebViewRouter();
  const { subTitle, title, isBackButtonVisible, rightComponent } = useHeaderStore();
  const { user } = useFetchUser();

  return (
    <header
      className={`fixed left-0 top-0 z-30 flex h-[50px] w-full items-center bg-white/90 pt-2 backdrop-blur-[10px] sm:h-16 sm:pt-0 ml:h-[98px]`}
    >
      {title ? (
        <div className="relative mx-auto flex h-full w-full max-w-[1280px] items-center justify-between px-5 sm:px-8 ml:px-10">
          <h2 className="text-24">{title}</h2>
          {rightComponent || (user && <Notification userId={user.id} />)}
        </div>
      ) : (
        <div className="relative mx-auto flex h-full w-full max-w-[1280px] items-center justify-center px-5 sm:px-8 ml:px-10">
          {isBackButtonVisible && (
            <button onClick={routerBack} className="absolute left-5">
              <ArrowLeftIcon className="h-6 w-6" />
            </button>
          )}

          {subTitle && <h3 className="text-base font-semibold">{subTitle}</h3>}
          {rightComponent && (
            <div className="absolute right-5 top-1/2 -translate-y-1/2">{rightComponent}</div>
          )}
        </div>
      )}
    </header>
  );
};
