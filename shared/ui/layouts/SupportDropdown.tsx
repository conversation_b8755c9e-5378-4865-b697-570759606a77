import { motion, useIsPresent } from 'framer-motion';
import Link from 'next/link';

import { useVisibility } from '@/shared/model/useVisibility';

export const SupportDropdown = () => {
  const isPresent = useIsPresent();
  const { isVisible, openToggle } = useVisibility();

  return (
    <motion.div
      key="contents-dropdown"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.3,
        ease: 'easeOut',
      }}
      onAnimationComplete={() => {
        openToggle();
      }}
      className={`fixed left-0 z-20 h-[296px] w-screen border-b border-gray-300 bg-white py-6 ${
        isPresent && isVisible && 'border-t'
      }`}
    >
      <div className="mx-auto max-w-screen-lg">
        <ul className="rounded pl-[630px] ml:pl-[505px]">
          <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/supports/faqs">FAQ</Link>
          </li>
          <li className="py-4 text-xl font-bold leading-[30px] hover:text-primary-500">
            <Link href="/supports/inquiry">문의하기</Link>
          </li>
        </ul>
      </div>
    </motion.div>
  );
};
