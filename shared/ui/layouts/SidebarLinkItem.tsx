'use client';

import { usePathname } from 'next/navigation';
import React from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

interface SidebarLinkItemProps {
  href: string;
  text: string;
  toggleDialog: () => void;
}

export const SidebarLinkItem = ({ href, text, toggleDialog }: SidebarLinkItemProps) => {
  const pathname = usePathname();
  const isActive = pathname === href;
  const { routerPush } = useWebViewRouter();
  const { isExistAccount } = useFetchUser();

  return (
    <div
      className="cursor-pointer"
      onClick={() => {
        if (href === '/user/assets/deposit' && !isExistAccount) {
          toggleDialog();
          return;
        } else {
          routerPush(href);
        }
      }}
    >
      <li className={`${isActive && 'font-semibold text-primary-500'}`}>{text}</li>
    </div>
  );
};
