'use client';

import { UserCircleIcon } from '@heroicons/react/24/outline';
import { AnimatePresence } from 'framer-motion';
import Link from 'next/link';

import { Notification } from '@/widgets/notifications/ui/Notification';

import { useHeader } from '@/shared/model/layouts/useHeader';
import { useHeaderDropdown } from '@/shared/model/layouts/useHeaderDropdown';

import { ConfirmDialog } from '../ConfirmDialog';
import { FallbackImage } from '../FallbackImage';
import { Separator } from '../shadcn/separator';
import { ContentsDropdown } from './ContentsDropdown';
import { MobileNav } from './MobileNav';
import { MobileSubTitleHeader } from './MobileSubTitleHeader';
import { SupportDropdown } from './SupportDropdown';

export const Header = () => {
  const {
    isContentsDropdownOpen,
    isSupportDropdownOpen,
    handleContentsMouseEnter,
    handleContentsMouseLeave,
    handleSupportMouseEnter,
    handleSupportMouseLeave,
  } = useHeaderDropdown();

  const { mode, isMobile, handleConfirm, user, isLogoutVisible, username, toggleLogoutVisibility } =
    useHeader();

  const isMain = mode === 'main';

  if (!isMain && isMobile) {
    return <MobileSubTitleHeader />;
  }

  return (
    <header
      className={`fixed left-0 top-0 z-30 flex h-[50px] w-full items-center pt-2 backdrop-blur-[10px] sm:h-16 sm:pt-0 ml:h-[98px] ${isContentsDropdownOpen || isSupportDropdownOpen ? 'bg-white' : 'bg-white/90'}`}
    >
      <div className="mx-auto flex h-full w-full max-w-[1280px] items-center justify-between px-5 sm:px-8 ml:px-10">
        <nav className="flex h-full items-center gap-[60px]">
          {isMain && (
            <div className="flex sm:hidden">
              <Link href="/">
                <FallbackImage
                  src="/logo/numit_logo.png"
                  alt="numit logo"
                  width={88}
                  height={22}
                  style={{ width: 88, height: 22 }}
                  className="ml:hidden"
                />
              </Link>
            </div>
          )}
          <Link href="/" className="hidden sm:block ml:hidden">
            <FallbackImage src="/logo/numit_logo.png" alt="numit logo" width={88} height={22} />
          </Link>

          <Link href="/" className="mr-5 hidden ml:block">
            <FallbackImage src="/logo/numit_logo.png" alt="numit logo" width={144} height={34} />
          </Link>
          <div
            onMouseEnter={handleContentsMouseEnter}
            onMouseLeave={handleContentsMouseLeave}
            className="hidden h-full ml:block"
          >
            <button className="h-full px-5 text-xl font-bold hover:text-primary-500">소식</button>
            <AnimatePresence mode="wait">
              {isContentsDropdownOpen && <ContentsDropdown />}
            </AnimatePresence>
          </div>
          <Link
            href="/subscriptions"
            className="hidden h-full px-5 text-xl font-bold hover:text-primary-500 ml:block"
          >
            <p className="flex h-full items-center justify-center">투자</p>
          </Link>
          <div
            onMouseEnter={handleSupportMouseEnter}
            onMouseLeave={handleSupportMouseLeave}
            className="hidden h-full ml:block"
          >
            <button className="h-full px-5 text-xl font-bold hover:text-primary-500">
              고객지원
            </button>
            <AnimatePresence mode="wait">
              {isSupportDropdownOpen && <SupportDropdown />}
            </AnimatePresence>
          </div>
        </nav>
        <div className="relative hidden items-center gap-3 ml:flex">
          {user && (
            <>
              <Notification userId={user.id} />
              <Separator orientation="vertical" className="h-4 bg-gray-300" />
            </>
          )}
          {user ? (
            <div className="hidden items-center gap-3 ml:flex">
              <div className="flex items-center gap-[6px] rounded-md p-[6px] text-sm hover:bg-[#607D8B]/15">
                <Link
                  href="/user/my/profile"
                  prefetch={false}
                  className="flex items-center gap-[6px]"
                >
                  <UserCircleIcon className="h-6 w-6" />
                  <strong className="font-semibold">{username}</strong>님
                </Link>
              </div>
              <Separator orientation="vertical" className="h-4 bg-gray-300" />
              <button onClick={toggleLogoutVisibility} className="text-sm text-gray-500">
                로그아웃
              </button>
            </div>
          ) : (
            <div className="hidden items-center gap-3 text-gray-500 ml:flex">
              <Link href="/sign-in" prefetch={false}>
                <span className="text-sm">로그인</span>
              </Link>
              <Separator orientation="vertical" className="h-4 bg-gray-300" />
              <Link href="/sign-up" prefetch={false}>
                <span className="text-sm">회원가입</span>
              </Link>
            </div>
          )}
        </div>
        <div className="relative flex items-center gap-4 ml:hidden">
          {user && <Notification userId={user.id} />}
          <MobileNav toggleLogoutVisibility={toggleLogoutVisibility} />
        </div>
      </div>
      <ConfirmDialog
        isOpen={isLogoutVisible}
        handleAction={handleConfirm}
        handleOpen={toggleLogoutVisibility}
        title="로그아웃 하시겠습니까?"
      />
    </header>
  );
};
