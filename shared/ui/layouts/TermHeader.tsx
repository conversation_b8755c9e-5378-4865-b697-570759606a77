'use client';

import { XMarkIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const TermHeader = ({ title }: { title: string }) => {
  const { routerBack } = useWebViewRouter();

  return (
    <header className="fixed left-0 top-0 z-10 flex h-[50px] w-full items-center justify-center bg-white sm:hidden">
      <p className="font-semibold"> {title}</p>
      <button onClick={routerBack} className="absolute right-4 top-1/2 -translate-y-1/2">
        <XMarkIcon className="h-6 w-6" />
      </button>
    </header>
  );
};
