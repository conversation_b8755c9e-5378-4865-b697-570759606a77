'use client';

import { signOut, useSession } from 'next-auth/react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { ConfirmDialog } from '../ConfirmDialog';
import { Label } from '../shadcn/label';
import { Separator } from '../shadcn/separator';
import { SidebarLinkItem } from './SidebarLinkItem';

export const UserSidebar = () => {
  const { isVisible, toggleVisibility } = useVisibility();
  const { isVisible: isVisibleAccountDialog, toggleVisibility: toggleVisibilityAccountDialog } =
    useVisibility();

  const { username } = useFetchUser();
  const { routerPush } = useWebViewRouter();

  const goToAccountRegister = () => {
    routerPush('/deposit/account-register?callbackUrl=/user/assets/deposit');
  };

  const handleConfirm = async () => await signOut({ redirect: true, callbackUrl: '/' });

  return (
    <aside className="hidden min-w-[184px] space-y-[42px] px-6 ml:block">
      <h4 className="font-bold">
        <strong className="text-xl">{username}</strong> 님
      </h4>
      <Separator className="bg-gray-100" />
      <nav className="space-y-[42px]">
        <div className="space-y-6">
          <Label className="text-xl font-bold">자산</Label>
          <ul className="flex flex-col gap-6">
            <SidebarLinkItem
              href="/user/assets"
              text="자산/수익"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <SidebarLinkItem
              href="/user/assets/subscription-history"
              text="청약 신청 내역"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <SidebarLinkItem
              href="/user/assets/deposit"
              text="예치금 계좌"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </ul>
        </div>
        <div className="space-y-6">
          <Label className="text-xl font-bold">내 정보</Label>
          <ul className="flex flex-col gap-6">
            <SidebarLinkItem
              href="/user/my/profile"
              text="기본 정보 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <SidebarLinkItem
              href="/user/my/investment-settings"
              text="투자 정보 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <SidebarLinkItem
              href="/user/my/notification"
              text="알림 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </ul>
        </div>
        <div className="space-y-6">
          <Label className="text-xl font-bold">고객센터</Label>
          <ul className="flex flex-col gap-6">
            <SidebarLinkItem
              href="/user/support/inquiry"
              text="문의하기"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <SidebarLinkItem
              href="/user/support/inquiry-history"
              text="문의 내역"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </ul>
        </div>
      </nav>
      <Separator className="bg-gray-100" />
      <button
        className="border-none px-0 text-xs font-semibold text-gray-500 shadow-none"
        onClick={toggleVisibility}
      >
        로그아웃
      </button>
      <ConfirmDialog
        isOpen={isVisible}
        handleAction={handleConfirm}
        handleOpen={toggleVisibility}
        title="로그아웃 하시겠습니까?"
      />
      <ConfirmDialog
        isOpen={isVisibleAccountDialog}
        handleAction={goToAccountRegister}
        handleOpen={toggleVisibilityAccountDialog}
        title="예치금 가상계좌 개설 안내"
        description={`투자 서비스를 위한 \n 예치금 가상계좌 개설이 필요합니다. \n 개설하시겠습니까?`}
      />
    </aside>
  );
};
