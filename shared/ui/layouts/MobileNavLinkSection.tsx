import Link from 'next/link';
import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { Label } from '../shadcn/label';

interface MobileNavLinkSectionProps {
  label: string;
  links: {
    href: string;
    label: string;
  }[];
  handleClose: () => void;
}

export const MobileNavLinkSection = ({ label, links, handleClose }: MobileNavLinkSectionProps) => {
  const { routerPush } = useWebViewRouter();

  return (
    <div className="space-y-6 px-6 py-7">
      <Label className="text-xs font-semibold text-gray-500">{label}</Label>
      <div className="flex flex-col gap-5">
        {links.map(({ href, label }) => (
          <button
            key={href}
            onClick={() => routerPush(href)}
            className="inline-flex text-[15px] font-semibold text-gray-900"
          >
            {label}
          </button>
          // <Link
          //   href={href}
          //   key={href}
          //   className="text-[15px] font-semibold text-gray-900"
          //   onClick={handleClose}
          // >
          //   {label}
          // </Link>
        ))}
      </div>
    </div>
  );
};

interface MobileNavLinkProps {
  href: string;
  label: string;
}

export const MobileNavLink = ({ href, label }: MobileNavLinkProps) => {
  return (
    <Link href={href} className="text-[15px] font-semibold text-gray-900">
      {label}
    </Link>
  );
};
