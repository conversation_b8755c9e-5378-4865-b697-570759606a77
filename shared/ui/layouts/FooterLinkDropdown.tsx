'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { Plus, XIcon } from 'lucide-react';
import React, { useEffect, useRef } from 'react';

import { useVisibility } from '@/shared/model/useVisibility';

export const links = [
  { label: 'TESSA-Corp', value: 'https://www.tessa-corp.com' },
  { label: 'TESSA', value: 'https://www.tessa.art' },
];

export const FooterLinkDropdown = ({ className }: { className?: string }) => {
  const { isVisible, toggleVisibility } = useVisibility();
  const selectRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
      if (isVisible) {
        toggleVisibility();
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible]);

  return (
    <div className={className} ref={selectRef}>
      <div className="flex w-full flex-1 items-center justify-between">
        <button
          type="button"
          onClick={toggleVisibility}
          className="flex h-[44px] w-full items-center justify-start rounded-lg border border-gray-300 bg-black px-4 text-base text-gray-300 ml:h-12 ml:w-[180px]"
        >
          <div className="flex w-full items-center justify-between">
            패밀리사이트
            {isVisible ? (
              <XIcon className="h-5 w-5 text-white" />
            ) : (
              <Plus className="h-5 w-5 text-white" />
            )}
          </div>
        </button>
      </div>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-[50px] left-0 z-40 w-full min-w-[180px] rounded-md border border-gray-300 bg-black px-4 py-6 text-gray-300 shadow-md" // top을 bottom으로 변경
          >
            <div className="flex flex-col items-start">
              {links.map((link) => (
                <a
                  href={link.value}
                  key={link.value}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex w-full items-center justify-between gap-2 rounded p-2"
                >
                  {link.label}
                </a>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
