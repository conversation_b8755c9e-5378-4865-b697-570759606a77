'use client';

import Link from 'next/link';

import { useFooterStore } from '@/shared/store/useFooterStore';

import { FallbackImage } from '../FallbackImage';
import { Separator } from '../shadcn/separator';
import { FooterLinkDropdown } from './FooterLinkDropdown';

export const Footer = () => {
  const { isFooterVisible } = useFooterStore();

  const handleOpenPopup = (url: string) => {
    window.open(url, '_blank', 'width=700, height=600, top=0, left=0, scrollbars=yes');
  };

  return (
    <footer
      className={`relative flex min-h-[400px] items-center bg-black sm:min-h-[320px] ml:min-h-[424px] ${
        isFooterVisible ? 'block pb-16 sm:pb-0' : 'hidden'
      }`}
    >
      <div className="container">
        <div className="flex w-full flex-col justify-between gap-6 sm:flex-row sm:items-center sm:gap-0">
          <FallbackImage
            src="/logo/white_logo.png"
            alt="logo.png"
            width={96}
            height={24}
            className="ml:h-[30px] ml:w-[128px]"
          />
          <ul className="flex gap-10 text-sm text-gray-300 sm:gap-8 ml:gap-12 ml:text-lg">
            <li className="cursor-pointer" onClick={() => handleOpenPopup('/terms/terms-of-use')}>
              이용약관
            </li>
            <li
              className="cursor-pointer font-semibold text-white"
              onClick={() => handleOpenPopup('/terms/privacy-policy')}
            >
              개인정보처리방침
            </li>
            <li
              className="cursor-pointer"
              onClick={() => handleOpenPopup('/terms/behavior-collection')}
            >
              행태정보 수집안내
            </li>
          </ul>
        </div>
        <Separator className="my-8 hidden w-full bg-white/20 ml:my-[40px] ml:block" />
        <FooterLinkDropdown className="relative mb-[22px] mt-8 ml:hidden" />
        <div className="flex w-full items-start justify-between ml:gap-16">
          <p className="break-all text-xs text-gray-500 ml:text-base">
            주식회사 테사 대표: 김형준 | 사업자등록번호: 721-87-01451 | 통신판매업신고 :
            2024-서울강남-03590
            <br />
            서울특별시 강남구 봉은사로26길 12, 4층 | Tel : 1600-8625 | E-mail :{' '}
            <Link href="mailto:<EMAIL>"><EMAIL></Link>
            <br />
            <br />
            NUMIT (뉴밋) 브랜드 및 서비스 관련 상표권과 저작권 등을 포함한 모든 권리는 주식회사
            테사에 있습니다. Copyright © NUMIT All rights reserved.
          </p>
          <FooterLinkDropdown className="relative hidden ml:block" />
        </div>
      </div>
    </footer>
  );
};
