'use client';

import { usePathname, useRouter } from 'next/navigation';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useUserMobileTab } from '@/shared/model/layouts/useUserMobileTab';
import { useVisibility } from '@/shared/model/useVisibility';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/shadcn/tabs';

import { ConfirmDialog } from '../ConfirmDialog';

export const UserMobileTab = () => {
  const {
    isHide,
    isMobile,
    isAssets,
    mobileTabValue,
    isVisibleAccountDialog,
    toggleVisibilityAccountDialog,
    goToAccountRegister,
  } = useUserMobileTab();

  return (
    <>
      <Tabs defaultValue={mobileTabValue()} className={`ml:hidden ${isHide && 'hidden'}`}>
        {!isMobile ? (
          <TabsList className="grid h-[64px] w-full grid-cols-3 border-b border-gray-300 px-6 text-[17px] font-bold sm:px-8">
            <TabsTrigger
              value="assets"
              className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
            >
              자산
            </TabsTrigger>
            <TabsTrigger
              value="my"
              className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
            >
              내 정보
            </TabsTrigger>

            <TabsTrigger
              value="support"
              className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
            >
              고객 센터
            </TabsTrigger>
          </TabsList>
        ) : (
          !isAssets && (
            <TabsList className="grid h-[64px] w-full grid-cols-3 border-b border-gray-300 px-6 text-[17px] font-bold sm:px-8">
              <TabsTrigger
                value="assets"
                className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
              >
                자산
              </TabsTrigger>
              <TabsTrigger
                value="my"
                className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
              >
                내 정보
              </TabsTrigger>

              <TabsTrigger
                value="support"
                className="h-[60px] rounded-none text-[17px] font-bold data-[state=active]:text-primary-500"
              >
                고객 센터
              </TabsTrigger>
            </TabsList>
          )
        )}
        <TabsContent value="assets" className="my-[10px] border-b border-gray-300 sm:my-0">
          <div className="grid grid-cols-3 px-6 sm:px-8">
            <UserMobileTabItem
              href="/user/assets"
              text="자산/수익"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <UserMobileTabItem
              href="/user/assets/subscription-history"
              text="청약 신청 내역"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <UserMobileTabItem
              href="/user/assets/deposit"
              text="예치금 계좌"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </div>
        </TabsContent>
        <TabsContent value="my" className="my-[10px] border-b border-gray-300 sm:my-0">
          <div className="grid grid-cols-3 px-6 sm:px-8">
            <UserMobileTabItem
              href="/user/my/profile"
              text="기본 정보 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <UserMobileTabItem
              href="/user/my/investment-settings"
              text="투자 정보 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <UserMobileTabItem
              href="/user/my/notification"
              text="알림 설정"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </div>
        </TabsContent>
        <TabsContent value="support" className="my-[10px] border-b border-gray-300 sm:my-0">
          <div className="grid grid-cols-2 px-6 sm:px-8">
            <UserMobileTabItem
              href="/user/support/inquiry"
              text="문의하기"
              toggleDialog={toggleVisibilityAccountDialog}
            />
            <UserMobileTabItem
              href="/user/support/inquiry-history"
              text="문의내역"
              toggleDialog={toggleVisibilityAccountDialog}
            />
          </div>
        </TabsContent>
      </Tabs>
      <ConfirmDialog
        isOpen={isVisibleAccountDialog}
        handleAction={goToAccountRegister}
        handleOpen={toggleVisibilityAccountDialog}
        title="예치금 가상계좌 개설 안내"
        description={`투자 서비스를 위한 \n 예치금 가상계좌 개설이 필요합니다. \n 개설하시겠습니까?`}
      />
    </>
  );
};

const UserMobileTabItem = ({
  href,
  text,
  toggleDialog,
}: {
  href: string;
  text: string;
  toggleDialog: () => void;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { isExistAccount } = useFetchUser();

  const isActive = pathname === href;

  return (
    <button
      onClick={() => {
        if (href === '/user/assets/deposit' && !isExistAccount) {
          toggleDialog();
          return;
        }
        router.replace(href);
      }}
      className={`h-[56px] ${isActive && 'border-b-[3px] border-primary-500'} `}
    >
      <p
        className={`py-[18px] text-center text-sm font-semibold ${isActive ? 'text-primary-500' : 'text-gray-300'}`}
      >
        {text}
      </p>
    </button>
  );
};
