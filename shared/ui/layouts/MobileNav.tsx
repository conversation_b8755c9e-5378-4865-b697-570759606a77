'use client';

import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/solid';
import { Separator } from '@radix-ui/react-separator';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { contentsNavLinks, supportNavLinks } from '@/shared/config/navLinks';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/shared/ui/shadcn/sheet';

import { MobileNavLinkSection } from './MobileNavLinkSection';

interface MobileNavProps {
  toggleLogoutVisibility: () => void;
}

export const MobileNav = ({ toggleLogoutVisibility }: MobileNavProps) => {
  const { isVisible, toggleVisibility, closeToggle } = useVisibility();
  const { user, username } = useFetchUser();
  const pathname = usePathname();

  const { routerPush } = useWebViewRouter();

  const router = useRouter();

  useEffect(() => {
    closeToggle();
  }, [pathname]);

  return (
    <Sheet open={isVisible} onOpenChange={toggleVisibility}>
      <SheetTrigger className="hidden sm:block ml:hidden">
        <Bars3Icon className="h-6 w-6" />
      </SheetTrigger>
      <SheetContent className="bg-white">
        <SheetHeader className="mt-10 px-6 py-5">
          <div className="flex justify-between">
            {user ? (
              <button onClick={() => router.replace('/user/my/profile')}>
                <SheetTitle className="text-[17px] font-normal">
                  <strong>{username}</strong>님
                </SheetTitle>
              </button>
            ) : (
              <SheetTitle className="text-[17px] font-normal">
                {/* <Link href="/sign-in" className="font-bold text-primary-500 underline">
                  로그인
                </Link>{' '} */}
                <button
                  onClick={() => routerPush('/sign-in')}
                  className="font-bold text-primary-500 underline"
                >
                  로그인
                </button>{' '}
                하러가기
              </SheetTitle>
            )}

            <div className="flex items-center gap-3">
              {user && (
                <button
                  onClick={toggleLogoutVisibility}
                  className="h-[22px] rounded-[20px] border border-gray-200 px-2 text-[10px] font-semibold text-gray-600"
                >
                  로그아웃
                </button>
              )}
              <SheetClose asChild>
                <button type="submit">
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </SheetClose>
            </div>
          </div>
          <SheetDescription />
        </SheetHeader>
        <Separator className="h-1 bg-gray-50" />
        <MobileNavLinkSection
          label="소식"
          links={contentsNavLinks}
          handleClose={toggleVisibility}
        />
        <Separator className="mx-6 h-[0.5px] bg-gray-300" />
        <div className="space-y-6 px-6 py-7">
          <button
            onClick={() => routerPush('/subscriptions')}
            className="inline-flex text-[15px] font-semibold text-gray-900"
          >
            투자
          </button>
        </div>
        <Separator className="mx-6 h-[0.5px] bg-gray-300" />
        <MobileNavLinkSection
          label="고객지원"
          links={supportNavLinks}
          handleClose={toggleVisibility}
        />
      </SheetContent>
    </Sheet>
  );
};
