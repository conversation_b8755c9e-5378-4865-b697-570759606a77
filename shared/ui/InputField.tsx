import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from './shadcn/form';
import { Input } from './shadcn/input';

interface InputFieldProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  type?: string;
  className?: string;
  readOnly?: boolean;
  disabled?: boolean;
  requiredText?: boolean;
  maxLength?: number;
  props?: React.InputHTMLAttributes<HTMLInputElement>;
}

export const InputField = ({
  form,
  name,
  label,
  placeholder,
  type = 'text',
  className,
  readOnly = false,
  disabled = false,
  requiredText,
  maxLength,
  ...props
}: InputFieldProps) => {
  const { control, register, formState } = form;

  return (
    <FormField
      control={control}
      name={name}
      render={() => (
        <FormItem className="w-full space-y-3">
          {label && (
            <FormLabel className="font-semibold sm:text-base">
              {label}
              {requiredText && <strong className="font-semibold text-primary-500"> (필수)</strong>}
            </FormLabel>
          )}
          <FormControl>
            <Input
              {...register(name)}
              placeholder={placeholder}
              type={type}
              readOnly={readOnly}
              maxLength={maxLength}
              disabled={disabled}
              className={`${className} ${formState.errors[name] && 'border-2 border-red-500'}`}
              {...props}
            />
          </FormControl>
          <FormMessage className="text-xs font-semibold sm:text-sm" />
        </FormItem>
      )}
    />
  );
};
