'use client';

import { ChangeEvent } from 'react';

import { useToast } from '@/shared/model/useToast';

type Props = {
  acceptType?: string;
  uploadedFiles?: (files: File[]) => void;
  multiple?: boolean;
  ref?: React.Ref<HTMLInputElement>;
  maxSizeMB?: number;
  maxFile?: number;
};

export const FileUploader = ({
  uploadedFiles,
  multiple,
  ref,
  maxSizeMB,
  maxFile = 10,
  acceptType = '.pdf,.jpg,.png,.jpeg',
}: Props) => {
  const { errorToast } = useToast();

  const handleChangeFile = (e: ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;

    if (!files) {
      return;
    }

    if (files.length > maxFile) {
      errorToast({
        title: '최대 10장까지 등록 가능합니다.',
      });
      e.preventDefault();
      return;
    }

    if (maxSizeMB) {
      const maxSizeBytes = maxSizeMB * 1024 * 1024; // MB를 bytes로 변환

      const oversizedFiles = Array.from(files).filter((file) => file.size > maxSizeBytes);
      if (oversizedFiles.length > 0) {
        errorToast({
          title: `파일 크기는 ${maxSizeMB}MB 이하여야 합니다.`,
        });
        e.target.value = ''; // 파일 선택 초기화
        return;
      }
    }

    if (uploadedFiles) {
      uploadedFiles(Array.from(files));
    }
  };
  return (
    <div className="hidden">
      <input
        type="file"
        ref={ref}
        multiple={multiple}
        accept={acceptType}
        onChange={handleChangeFile}
      />
    </div>
  );
};
