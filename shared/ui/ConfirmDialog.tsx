import { PrimaryButton } from './PrimaryButton';
import { SecondaryButton } from './SecondaryButton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './shadcn/dialog';

interface ConfirmDialogProps {
  isOpen: boolean;
  handleOpen: () => void;
  title?: string;
  description?: string;
  caption?: string;
  handleAction?: () => void;
  text?: string;
  isCancelButton?: boolean;
}

export const ConfirmDialog = ({
  isOpen,
  handleOpen,
  title,
  handleAction,
  description,
  caption,
  text,
  isCancelButton = true,
}: ConfirmDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={handleOpen}>
      <DialogContent className="flex max-w-[327px] flex-col gap-10 rounded-[16px] bg-white px-6 leading-[150%] sm:max-w-[386px]">
        <DialogHeader className="mt-6 space-y-4">
          {title && (
            <DialogTitle className="whitespace-pre-line text-center text-base font-semibold">
              {title}
            </DialogTitle>
          )}
          {description && (
            <DialogDescription className="whitespace-pre-line text-center text-sm text-gray-900">
              {description}
            </DialogDescription>
          )}
          {caption && (
            <DialogDescription className="whitespace-pre-line text-center text-xs text-gray-700">
              {caption}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="mb-[10px] flex justify-center gap-2">
          {isCancelButton && (
            <SecondaryButton className="w-full" onClick={handleOpen} text="취소" />
          )}
          {handleAction && (
            <PrimaryButton
              type="submit"
              className={`h-[44px] w-full font-semibold sm:h-12`}
              onClick={handleAction}
              text={text}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
