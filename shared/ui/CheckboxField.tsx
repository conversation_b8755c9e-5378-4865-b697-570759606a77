import React from 'react';

import { cn } from '../lib/utils';
import { Checkbox } from './shadcn/checkbox';
import { Label } from './shadcn/label';

interface CheckboxFieldProps {
  value: string;
  label: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  className?: string;
  required?: boolean;
  linkType?: 'marketing' | 'privacy' | 'terms';
  size?: 'sm' | 'md';
  props?: React.ComponentProps<typeof Checkbox>;
}

export const CheckboxField = ({
  value,
  label,
  checked,
  onCheckedChange,
  className,
  required,
  linkType,
  size = 'md',
  ...props
}: CheckboxFieldProps) => {
  const handleOpenPopup = () => {
    if (!linkType) return;

    let url;
    if (linkType === 'marketing') {
      url = '/terms/marketing-usage';
    } else if (linkType === 'privacy') {
      url = '/terms/privacy-policy';
    } else if (linkType === 'terms') {
      url = '/terms/terms-of-use';
    }

    window.open(url, '_blank', 'width=700, height=600, top=0, left=0, scrollbars=yes');
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Checkbox
        checked={checked}
        onCheckedChange={onCheckedChange}
        className={`${size === 'sm' ? 'h-4 w-4 sm:h-5 sm:w-5' : 'h-5 w-5'} border-gray-300 bg-white text-white shadow-none data-[state=checked]:bg-primary-500`}
        value={value}
        {...props}
      />
      <Label
        onClick={handleOpenPopup}
        className={`${size === 'sm' ? 'text-xs sm:text-sm' : 'text-sm sm:text-base'} ${linkType && 'cursor-pointer'}`}
      >
        {label} {required && <span className="text-primary-500">(필수)</span>}
      </Label>
    </div>
  );
};
