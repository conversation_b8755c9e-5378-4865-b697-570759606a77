'use client';

import { CalendarIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';
import { motion } from 'framer-motion';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';

interface CustomDayPickerProps {
  selectedDate?: string;
  handleDateChange: (date: string) => void;
  label: string;
  isOpen: boolean;
  minDate?: string;
  maxDate?: string;
  handleDatePickerClick: (name: 'startDate' | 'endDate') => void;
  type: 'startDate' | 'endDate';
}

export const CustomDayPicker = ({
  selectedDate,
  handleDateChange,
  label,
  isOpen,
  minDate,
  maxDate,
  type,
  handleDatePickerClick,
}: CustomDayPickerProps) => {
  return (
    <div className="flex-1 sm:relative">
      <div className="flex w-full flex-1 items-center justify-between">
        <button
          type="button"
          onClick={() => handleDatePickerClick(type)}
          className={`flex w-full flex-1 items-center justify-between ${selectedDate ? 'text-gray-900' : 'text-gray-500'}`}
        >
          {selectedDate ? (
            format(selectedDate, 'yyyy-MM-dd')
          ) : (
            <>
              {label}
              <CalendarIcon className="h-5 w-5 text-gray-900" />
            </>
          )}
        </button>
        {selectedDate && (
          <button type="button" onClick={() => handleDateChange('')}>
            <XCircleIcon className="h-5 w-5 text-gray-400" />
          </button>
        )}
      </div>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className="absolute left-[-20px] top-12 z-40 w-[327px] rounded-xl bg-white p-4 shadow-md sm:left-0"
        >
          <DayPicker
            locale={ko}
            disabled={(date) => {
              if (minDate) {
                return date < new Date(new Date(minDate).setHours(0, 0, 0, 0));
              }
              if (maxDate) {
                return date > new Date(new Date(maxDate).setHours(23, 59, 59, 999));
              }
              return false;
            }}
            onDayClick={(value: Date) => {
              const formattedDate = format(value, 'yyyy-MM-dd');
              handleDateChange(formattedDate);
              handleDatePickerClick(type);
            }}
          />
        </motion.div>
      )}
    </div>
  );
};
