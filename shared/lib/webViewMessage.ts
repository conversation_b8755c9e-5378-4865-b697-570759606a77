export const RN_API = {
  ROUTER_CHANGE: 'ROUTER_CHANGE',
};

export const WebViewMessage = async (type: string, data: any) =>
  new Promise((resolve, reject) => {
    if (!window.ReactNativeWebView) {
      return;
    }
    const reqId = Date.now();
    // const TIMEOUT = 10000; // 10s
    // const timer = setTimeout(() => {
    //   /** android */
    //   document.removeEventListener('message', listener);
    //   /** ios */
    //   window.removeEventListener('message', listener);

    //   reject('TIMEOUT');
    // }, TIMEOUT);

    const listener = (event: any) => {
      const result = JSON.parse(event.data);

      const { data: listenerData, reqId: listenerReqId } = JSON.parse(event.data);

      if (listenerReqId === reqId) {
        // clearTimeout(timer);

        /** android */
        document.removeEventListener('message', listener);
        /** ios */
        window.removeEventListener('message', listener);
        resolve(listenerData);
      }

      resolve(result);
    };

    window.ReactNativeWebView.postMessage(
      JSON.stringify({
        type,
        data,
        reqId,
      }),
    );

    /** android */
    document.addEventListener('message', listener);
    /** ios */
    window.addEventListener('message', listener);
  });
