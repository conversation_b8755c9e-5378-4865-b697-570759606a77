import * as Sentry from '@sentry/nextjs';
import { logClientError } from '@artbloc/next-js-logger/client';
import axios, { isAxiosError } from 'axios';

import env from '@/shared/lib/env.schema';

export const cmsPublicInstance = axios.create({
  baseURL: `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms`,
  headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
});

cmsPublicInstance.interceptors.response.use(
  ({ data }) => data,
  (error) => {
    if (isAxiosError(error)) {
      logClientError('axios-error', error);
      if (error?.response?.status && error?.response?.status >= 500) {
        Sentry.captureException(error);
      }
    }
    return Promise.reject(error);
  },
);
