import axios from 'axios';
import { getSession } from 'next-auth/react';

import env from '@/shared/lib/env.schema';

export const userPrivateInstance = axios.create({
  baseURL: env.NEXT_PUBLIC_NUMIT_LB_URL,
  headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
});

userPrivateInstance.interceptors.request.use(
  async (config) => {
    const session = await getSession();

    if (!session) {
      window.location.href = '/sign-in';
      return Promise.reject(new Error('No session'));
    }

    config.headers.Authorization = `Bearer ${session?.user?.accessToken}`;
    return config;
  },
  (error) => {
    console.error('Request Error:', error);

    return Promise.reject(error);
  },
);

userPrivateInstance.interceptors.response.use(
  ({ data }) => data,
  async (error) => {
    // if (isAxiosError(error)) {
    //   logClientError('axios-error', error);
    //   if (error?.response?.status && error?.response?.status >= 500) {
    //     Sentry.captureException(error);
    //   }

    //   const url = error?.config?.url;
    //   const excludeUrls = ['/user/password/check', '/user/password/new'];

    //   if (error?.response?.status === 401 && url && !excludeUrls.includes(url)) {
    //     signOut({ redirectTo: '/sign-in' });
    //   } else if (error?.response?.status === 404) {
    //     signOut({ redirectTo: '/sign-in' });
    //   }
    // }

    return Promise.reject(error);
  },
);
