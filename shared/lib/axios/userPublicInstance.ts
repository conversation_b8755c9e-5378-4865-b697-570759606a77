import * as Sentry from '@sentry/nextjs';
import { logClientError } from '@artbloc/next-js-logger/client';
import axios, { isAxiosError } from 'axios';

import env from '@/shared/lib/env.schema';

export const userPublicInstance = axios.create({
  baseURL: env.NEXT_PUBLIC_NUMIT_LB_URL,
  headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
});

userPublicInstance.interceptors.response.use(
  ({ data }) => data,
  async (error) => {
    if (isAxiosError(error)) {
      logClientError('axios-error', error);
      if (error?.response?.status && error?.response?.status >= 500) {
        Sentry.captureException(error);
      }
    }
    return Promise.reject(error);
  },
);
