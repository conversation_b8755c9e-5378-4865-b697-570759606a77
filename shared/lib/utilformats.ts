import dayjs from 'dayjs';
import 'dayjs/locale/ko';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(relativeTime);
dayjs.locale('ko');
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Asia/Seoul');

export function utilFormats() {
  return {
    YYYYMMDD(date?: Date | string | null, format = 'YYYY-MM-DD') {
      if (!date) {
        return null;
      }

      return dayjs(date).format(format);
    },

    CASHCOMMA(value: number | string) {
      let num = value;
      if (!value) {
        return 0;
      }
      if (typeof value === 'string') {
        num = parseInt(value, 10);
      }

      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    URLFY(text?: string) {
      if (!text) {
        return;
      }
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      return text.replace(urlRegex, (url) => {
        return `<a href="${url}" target="_blank" class="text-indigo-500 underline underline-offset-2">${url}</a>`;
      });
    },
    DAYFROMNOW(date?: Date | string | null) {
      if (!date) {
        return null;
      }

      const targetDate = dayjs(date);
      const today = dayjs();
      const diffDays = targetDate.diff(today, 'day');

      return diffDays;
    },
  };
}
