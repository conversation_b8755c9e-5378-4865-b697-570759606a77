import { z } from 'zod';

const envSchema = z.object({
  // # Auth.js (NextAuth)
  NEXTAUTH_URL: z.string().url().nonempty(),
  NEXTAUTH_SECRET: z.string().nonempty(),

  //   # GA4 & Sentry
  GTAG_ID: z.string().nonempty(),
  SENTRY_AUTH_TOKEN: z.string().nonempty(),

  //   # Kakao & Naver Login
  NEXT_PUBLIC_KAKAO_API_KEY: z.string().nonempty(),
  NEXT_PUBLIC_NAVER_CLIENT_ID: z.string().nonempty(),
  NEXT_PUBLIC_APPLE_CLIENT_ID: z.string().nonempty(),
  APPLE_KEY_ID: z.string().nonempty(),
  APPLE_TEAM_ID: z.string().nonempty(),

  // NUMIT API, Client
  NEXT_PUBLIC_NUMIT_LB_URL: z.string().url().nonempty(),
  NEXT_PUBLIC_REDIRECT_URL: z.string().url().nonempty(),

  APPLE_PRIVATE_KEY: z.string().nonempty(),
  APPLE_BUNDLE_ID: z.string().nonempty(),

  // NUMIT API, Server
  NUMIT_GATEWAY_URL: z.string().url().nonempty(),
  NUMIT_LB_URL: z.string().url().nonempty(),

  // NODE_ENV
  NEXT_PUBLIC_NODE_ENV: z.string().nonempty(),

  // PORTONE
  NEXT_PUBLIC_STORE_ID: z.string().nonempty(),
  NEXT_PUBLIC_CHANNE_KEY: z.string().nonempty(),
  PORTONE_API_SECRET: z.string().nonempty(),
  // OCR
  OCR_SECRET_KEY: z.string().nonempty(),
  OCR_API_URL: z.string().url().nonempty(),
});

const result = envSchema.safeParse({
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  GTAG_ID: process.env.GTAG_ID,
  SENTRY_AUTH_TOKEN: process.env.SENTRY_AUTH_TOKEN,
  NEXT_PUBLIC_KAKAO_API_KEY: process.env.NEXT_PUBLIC_KAKAO_API_KEY,
  NEXT_PUBLIC_NAVER_CLIENT_ID: process.env.NEXT_PUBLIC_NAVER_CLIENT_ID,
  NEXT_PUBLIC_NUMIT_LB_URL: process.env.NEXT_PUBLIC_NUMIT_LB_URL,
  NEXT_PUBLIC_APPLE_CLIENT_ID: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID,
  APPLE_KEY_ID: process.env.APPLE_KEY_ID,
  APPLE_TEAM_ID: process.env.APPLE_TEAM_ID,
  NEXT_PUBLIC_REDIRECT_URL: process.env.NEXT_PUBLIC_REDIRECT_URL,
  NUMIT_GATEWAY_URL: process.env.NUMIT_GATEWAY_URL,
  NUMIT_LB_URL: process.env.NUMIT_LB_URL,
  NEXT_PUBLIC_NODE_ENV: process.env.NEXT_PUBLIC_NODE_ENV,
  NEXT_PUBLIC_STORE_ID: process.env.NEXT_PUBLIC_STORE_ID,
  NEXT_PUBLIC_CHANNE_KEY: process.env.NEXT_PUBLIC_CHANNE_KEY,
  PORTONE_API_SECRET: process.env.PORTONE_API_SECRET,
  OCR_SECRET_KEY: process.env.OCR_SECRET_KEY,
  OCR_API_URL: process.env.OCR_API_URL,
  APPLE_PRIVATE_KEY: process.env.APPLE_PRIVATE_KEY,
  APPLE_BUNDLE_ID: process.env.APPLE_BUNDLE_ID,
});

if (!result.success) {
  throw new Error(`환경 변수 검증 오류: ${JSON.stringify(result.error.format())}`);
}

const env = result.data;

export default env;
