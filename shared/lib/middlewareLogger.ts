import { NextRequest, NextResponse } from 'next/server';

import { logger } from './logger';

export interface RequestInfo {
  method: string;
  path: string;
  query: Record<string, string>;
  userAgent: string | null;
  ip: string;
  headers: {
    accept: string | null;
    'content-type': string | null;
    'accept-language': string | null;
  };
}

export const getRequestInfo = (request: NextRequest): RequestInfo => {
  const ip =
    request.headers.get('x-real-ip') ??
    request.headers.get('x-forwarded-for')?.split(',')[0] ??
    'unknown';

  return {
    method: request.method,
    path: request.nextUrl.pathname,
    query: Object.fromEntries(request.nextUrl.searchParams),
    userAgent: request.headers.get('user-agent'),
    ip,
    headers: {
      accept: request.headers.get('accept'),
      'content-type': request.headers.get('content-type'),
      'accept-language': request.headers.get('accept-language'),
    },
  };
};

export const logRequest = (request: NextRequest) => {
  const requestInfo = getRequestInfo(request);
  logger.info(`[Request] ${request.method} ${request.nextUrl.pathname}`, requestInfo);
};

export const logResponse = (response: NextResponse, request: NextRequest, duration: number) => {
  logger.info(`[Response] ${response.status}`, {
    duration: `${duration}ms`,
    path: request.nextUrl.pathname,
    ...(response.status >= 400 && {
      error: `Cannot ${request.method} ${request.nextUrl.pathname}`,
    }),
  });
};
