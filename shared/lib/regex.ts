export const phoneRegex = new RegExp(/^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/);

export const passwordRegex = new RegExp(
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])(?!.*\s)[A-Za-z\d!@#$%^&*]{8,16}$/,
);

export const koreanNameRegex = new RegExp(/^[가-힣\s]{2,8}$/);
export const englishNameRegex = new RegExp(/^[a-zA-Z\s]{2,16}$/);

export const urlRegex = new RegExp(/((https?:\/\/|www\.)[^\s]+)/g);
