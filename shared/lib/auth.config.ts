import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { NextAuthConfig, Session, User } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialProvider from 'next-auth/providers/credentials';

import { registerAgreement } from '@/entities/auth/api/registerAgreement';
import { registerNotificationAgreement } from '@/entities/auth/api/registerNotificationAgreement';

import env from '@/shared/lib/env.schema';

import { NotificationChannel, UserCat, UserRole } from '../types';

const refreshToken = async (refreshToken: string) => {
  const { data } = await axios.post(
    `${env.NUMIT_LB_URL}/auth/issue-token`,
    {},
    {
      headers: {
        Authorization: `Bearer ${refreshToken}`,
      },
    },
  );

  return {
    accessToken: data.data.accessToken,
    refreshToken: data.data.refreshToken,
  };
};

const authConfig = {
  trustHost: true,
  providers: [
    CredentialProvider({
      id: 'sns-signup',
      credentials: {
        socialId: { type: 'text', required: true },
        email: { type: 'email', required: true },
        mobileNumber: { type: 'text', required: true },
        name: { type: 'text', required: true },
        gender: { type: 'text', required: true },
        type: { type: 'text', required: true },
        isSmsAgree: { type: 'boolean', required: true },
        isEmailAgree: { type: 'boolean', required: true },
        birthDate: { type: 'text', required: true },
      },
      async authorize(credentials): Promise<User | null> {
        const {
          socialId,
          email,
          mobileNumber,
          name,
          gender,
          type,
          isSmsAgree,
          isEmailAgree,
          birthDate,
        } = credentials;
        try {
          const payload = {
            socialId,
            email,
            name,
            mobileNumber,
            gender,
            birthDate,
          };

          const { data } = await axios.post(
            `${env.NUMIT_LB_URL}/auth/social/${type}/signup`,
            payload,
          );

          if (!data) {
            return null;
          }

          await registerAgreement(data?.data.userId);

          // await tempDepositAccount({
          //   userId: data?.data.userId,
          //   depositAmount: *********,
          //   eventCode: 'DEPOSIT',
          //   remark: '소셜 회원가입',
          // });

          const smsAgree = isSmsAgree === 'true';
          const emailAgree = isEmailAgree === 'true';
          const channel = [
            ...(smsAgree ? [NotificationChannel.SMS] : []),
            ...(emailAgree ? [NotificationChannel.EMAIL] : []),
          ];

          if (channel.length > 0) {
            await registerNotificationAgreement({
              userId: data?.data.userId,
              channel,
            });
          }

          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(data.data.accessToken);

          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(
            data.data.refreshToken,
          );

          const user = {
            id: data?.data.userId,
            account: data?.data.email as string,
            userId: data?.data.userId,
            role: decodedAccessToken.role,
            userCat: decodedAccessToken.user_cat,
            accessToken: data?.data.accessToken,
            refreshToken: data?.data.refreshToken,
            accessTokenExpiredAt: decodedAccessToken.exp * 1000,
            refreshTokenExpiredAt: decodedRefreshToken.exp * 1000,
            provider: 'sns',
          };

          return user;
        } catch (e) {
          console.log('signin error', e);
          return null;
        }
      },
    }),
    CredentialProvider({
      id: 'sns-signin',
      credentials: {
        socialId: { type: 'text', required: true },
        mobileNumber: { type: 'text', required: false },
        type: { type: 'text', required: true },
        email: { type: 'email', required: false },
      },
      async authorize(credentials): Promise<User | null> {
        const { socialId, mobileNumber, type, email } = credentials;

        let payload = {};
        if (type === 'apple') {
          payload = {
            socialId,
            email,
          };
        } else {
          payload = {
            socialId,
            mobileNumber,
          };
        }

        try {
          const { data } = await axios.post(
            `${env.NUMIT_LB_URL}/auth/social/${type}/login`,
            payload,
          );

          if (!data) {
            return null;
          }

          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(data.data.accessToken);
          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(
            data.data.refreshToken,
          );

          const user = {
            id: data?.data.userId,
            account: data.data.account as string,
            userId: data?.data.userId,
            role: decodedAccessToken.role,
            userCat: decodedAccessToken.user_cat,
            accessToken: data?.data.accessToken,
            refreshToken: data?.data.refreshToken,
            accessTokenExpiredAt: decodedAccessToken.exp * 1000,
            refreshTokenExpiredAt: decodedRefreshToken.exp * 1000,
            provider: 'sns',
          };

          return user;
        } catch (e) {
          console.log('signin error', e);
          return null;
        }
      },
    }),
    CredentialProvider({
      id: 'email-signup',
      credentials: {
        email: { type: 'email', required: true },
        password: { type: 'password', required: true },
        name: { type: 'text', required: true },
        mobileNumber: { type: 'text', required: true },
        isSmsAgree: { type: 'boolean', required: true },
        isEmailAgree: { type: 'boolean', required: true },
      },
      async authorize(credentials): Promise<User | null> {
        const { isSmsAgree, isEmailAgree } = credentials;
        try {
          const { data } = await axios.post(`${env.NUMIT_LB_URL}/auth/user/signup`, credentials);

          if (!data) {
            return null;
          }

          await registerAgreement(data?.data.userId);

          // await tempDepositAccount({
          //   userId: data?.data.userId,
          //   depositAmount: *********,
          //   eventCode: 'DEPOSIT',
          //   remark: '회원가입',
          // });

          const smsAgree = isSmsAgree === 'true';
          const emailAgree = isEmailAgree === 'true';
          const channel = [
            ...(smsAgree ? [NotificationChannel.SMS] : []),
            ...(emailAgree ? [NotificationChannel.EMAIL] : []),
          ];

          if (channel.length > 0) {
            await registerNotificationAgreement({
              userId: data?.data.userId,
              channel,
            });
          }

          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(data.data.accessToken);
          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(
            data.data.refreshToken,
          );

          const user = {
            id: data?.data.userId,
            account: data?.data.account as string,
            userId: data?.data.userId,
            accessToken: data?.data.accessToken,
            role: decodedAccessToken.role,
            userCat: decodedAccessToken.user_cat,
            refreshToken: data?.data.refreshToken,
            accessTokenExpiredAt: decodedAccessToken.exp * 1000,
            refreshTokenExpiredAt: decodedRefreshToken.exp * 1000,
            provider: 'email',
          };

          return user;
        } catch (e) {
          console.log('signup error', e);
          return null;
        }
      },
    }),
    CredentialProvider({
      id: 'email-signin',
      credentials: {
        account: { type: 'email', required: true },
        password: { type: 'password', required: true },
      },
      async authorize(credentials): Promise<User | null> {
        try {
          const { data } = await axios.post(`${env.NUMIT_LB_URL}/auth/user/login`, {
            account: credentials?.account,
            password: credentials?.password,
          });

          if (!data) {
            return null;
          }

          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(data.data.accessToken);
          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(
            data.data.refreshToken,
          );
          const user = {
            id: data?.data.userId,
            account: credentials?.account as string,
            userId: data?.data.userId,
            role: decodedAccessToken.role,
            userCat: decodedAccessToken.user_cat,
            accessToken: data?.data.accessToken,
            refreshToken: data?.data.refreshToken,
            accessTokenExpiredAt: decodedAccessToken.exp * 1000,
            refreshTokenExpiredAt: decodedRefreshToken.exp * 1000,
            provider: 'email',
          };

          return user;
        } catch (e) {
          console.log('signin error', e);
          return null;
        }
      },
    }),
    CredentialProvider({
      id: 'corporate-signin',
      credentials: {
        account: { type: 'email', required: true },
        password: { type: 'password', required: true },
      },
      async authorize(credentials): Promise<User | null> {
        try {
          const { data } = await axios.post(`${env.NUMIT_LB_URL}/auth/corporate/login`, {
            account: credentials?.account,
            password: credentials?.password,
          });

          if (!data) {
            return null;
          }

          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(data.data.accessToken);
          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(
            data.data.refreshToken,
          );
          const user = {
            id: data?.data.userId,
            account: credentials?.account as string,
            userId: data?.data.userId,
            role: decodedAccessToken.role,
            userCat: decodedAccessToken.user_cat,
            accessToken: data?.data.accessToken,
            refreshToken: data?.data.refreshToken,
            accessTokenExpiredAt: decodedAccessToken.exp * 1000,
            refreshTokenExpiredAt: decodedRefreshToken.exp * 1000,
            provider: 'email',
          };

          return user;
        } catch (e) {
          console.log('signin error', e);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    jwt: async ({ token, user, trigger }: { token: JWT; user: User; trigger?: string }) => {
      // 유저 role update 일반사용자 -> 투자자 변환
      if (trigger === 'update') {
        const response = await fetch(`${env.NUMIT_LB_URL}/user/promote-to-investor`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
        });

        if (!response.ok) {
          console.error('User is not valid');
          return null;
        }

        const { data } = await response.json();

        const decodedAccessToken: {
          role: UserRole;
          iat: number;
          exp: number;
          sub: number;
          user_cat: UserCat;
        } = jwtDecode(data.accessToken);
        const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(data.refreshToken);

        return {
          ...token,
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
          role: decodedAccessToken.role,
          userCat: decodedAccessToken.user_cat,
          accessTokenExpiredAt: (decodedAccessToken.exp as number) * 1000,
          refreshTokenExpiredAt: (decodedRefreshToken.exp as number) * 1000,
        } as JWT;
      }

      if (user) {
        return {
          ...token,
          accessToken: user.accessToken,
          refreshToken: user.refreshToken,
          userId: user.userId,
          account: user.account,
          provider: user.provider,
          userCat: user.userCat,
          accessTokenExpiredAt: user.accessTokenExpiredAt,
          refreshTokenExpiredAt: user.refreshTokenExpiredAt,
        } as JWT;
      }

      //로그인 미유지
      if (token) {
        // 엑세스 토큰 만료시 로그아웃
        if (Date.now() > token.accessTokenExpiredAt) {
          return null;
        }

        // 엑세스토큰 재발급
        try {
          const result = await refreshToken(token.refreshToken);
          const decodedAccessToken: {
            role: UserRole;
            iat: number;
            exp: number;
            sub: number;
            user_cat: UserCat;
          } = jwtDecode(result.accessToken);
          const decodedRefreshToken: { iat: number; exp: number } = jwtDecode(result.refreshToken);
          token.accessToken = result.accessToken;
          token.refreshToken = result.refreshToken;
          token.role = decodedAccessToken.role;
          token.userCat = decodedAccessToken.user_cat;
          token.accessTokenExpiredAt = (decodedAccessToken.exp as number) * 1000;
          token.refreshTokenExpiredAt = (decodedRefreshToken.exp as number) * 1000;
          return token;
        } catch (error) {
          console.error('Token refresh error:', error);
          return null;
        }
      }

      //Todo: 유저 상태 체크 (임시)
      // if (token) {
      //   const response = await fetch(`${env.NUMIT_LB_URL}/user/profile`, {
      //     headers: {
      //       Authorization: `Bearer ${token.accessToken}`,
      //     },
      //   });

      //   if (!response.ok) {
      //     console.error('User is not valid');
      //     return null;
      //   }
      // }

      return token;
    },
    session: ({ session, token }: { session: Session; token: JWT }): any => {
      if (token && session.user) {
        session.user.role = token.role as UserRole;
        session.user.userCat = token.userCat as UserCat;
        session.user.account = token.account as string;
        session.user.userId = token.userId as string;
        session.user.accessToken = token.accessToken as string;
        session.user.refreshToken = token.refreshToken as string;
        session.user.provider = token.provider as string;
        session.user.accessTokenExpiredAt = token.accessTokenExpiredAt as number;
        session.user.refreshTokenExpiredAt = token.refreshTokenExpiredAt as number;
        return session;
      }

      return null;
    },
  },

  pages: {
    signIn: '/sign-in',
    signOut: '/',
  },
} satisfies NextAuthConfig;

export default authConfig;
