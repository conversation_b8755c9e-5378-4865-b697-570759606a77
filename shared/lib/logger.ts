type LogLevel = 'error' | 'warn' | 'info' | 'debug';

interface LoggerOptions {
  environment?: string;
  timestamp?: boolean;
}

class Logger {
  private environment: string;
  private timestamp: boolean;

  constructor(options: LoggerOptions = {}) {
    this.environment = options.environment || process.env.NODE_ENV || 'development';
    this.timestamp = options.timestamp ?? true;
  }

  private sanitizeData(data: any): any {
    const sensitiveFields = [
      'password',
      'token',
      'accessToken',
      'refreshToken',
      'authorization',
      'cookie',
    ];

    if (typeof data !== 'object' || !data) return data;

    return Object.keys(data).reduce((acc: any, key) => {
      if (sensitiveFields.some((field) => key.toLowerCase().includes(field))) {
        acc[key] = '[REDACTED]';
      } else {
        acc[key] = data[key];
      }
      return acc;
    }, {});
  }

  private formatMessage(level: LogLevel, message: string, meta?: any): string {
    const timestamp = this.timestamp ? `[${new Date().toISOString()}]` : '';
    const env = `[${this.environment}]`;
    const levelStr = `[${level}]`;
    const sanitizedMeta = meta ? this.sanitizeData(meta) : undefined;
    const metaStr = sanitizedMeta ? ` ${JSON.stringify(sanitizedMeta)}` : '';

    return `${timestamp} ${env} ${levelStr}: ${message}${metaStr}`;
  }

  error(message: string, meta?: any) {
    console.error(this.formatMessage('error', message, meta));
  }

  warn(message: string, meta?: any) {
    console.warn(this.formatMessage('warn', message, meta));
  }

  info(message: string, meta?: any) {
    console.info(this.formatMessage('info', message, meta));
  }

  debug(message: string, meta?: any) {
    console.debug(this.formatMessage('debug', message, meta));
  }
}

export const logger = new Logger();
