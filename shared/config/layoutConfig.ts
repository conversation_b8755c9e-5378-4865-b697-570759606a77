import { ReactNode } from 'react';

import { HeaderMode, ScreenSize } from '../types';

export interface LayoutConfig {
  header: {
    mode: HeaderMode;
    title?: string;
    subTitle?: string;
    rightComponent?: ReactNode;
  };
  gnb: {
    isVisible: boolean;
  };
}

// 모바일(744px 이하)인지 확인
export const isMobileScreen = (screenSize: ScreenSize): boolean => {
  return screenSize === ScreenSize.MOBILE;
};

// 라우트별 모바일 레이아웃 설정 (744px 이하에서만 적용)
export const routeLayoutConfigs: Record<string, LayoutConfig> = {
  '/': { header: { mode: HeaderMode.MAIN }, gnb: { isVisible: true } },
  '/subscriptions': {
    header: { mode: HeaderMode.CATEGORY, title: '투자' },
    gnb: { isVisible: true },
  },
  '/subscriptions/[id]': { header: { mode: HeaderMode.DETAIL }, gnb: { isVisible: false } },
  '/subscriptions/onboarding': {
    header: { mode: HeaderMode.LIST, subTitle: '' },
    gnb: { isVisible: false },
  },
  '/subscriptions/request': {
    header: { mode: HeaderMode.LIST, subTitle: '투자 의뢰 신청' },
    gnb: { isVisible: false },
  },
  '/subscriptions/apply': {
    header: { mode: HeaderMode.LIST, subTitle: '투자 위험 고지 안내' },
    gnb: { isVisible: false },
  },
  '/contents': { header: { mode: HeaderMode.CATEGORY, title: ' 소식' }, gnb: { isVisible: true } },
  '/contents/notices': {
    header: { mode: HeaderMode.LIST, subTitle: '공지사항' },
    gnb: { isVisible: false },
  },
  '/contents/notices/[id]': {
    header: { mode: HeaderMode.DETAIL, subTitle: '공지사항' },
    gnb: { isVisible: false },
  },
  '/contents/events': {
    header: { mode: HeaderMode.LIST, subTitle: '이벤트' },
    gnb: { isVisible: false },
  },
  '/contents/events/[id]': { header: { mode: HeaderMode.DETAIL }, gnb: { isVisible: false } },
  '/contents/curations': {
    header: { mode: HeaderMode.LIST, subTitle: '콘텐츠' },
    gnb: { isVisible: false },
  },
  '/contents/curations/[id]': {
    header: { mode: HeaderMode.LIST, subTitle: '콘텐츠' },
    gnb: { isVisible: false },
  },
  '/contents/news': {
    header: { mode: HeaderMode.DETAIL, subTitle: '언론자료' },
    gnb: { isVisible: false },
  },
  '/user/assets/deposit': {
    header: { mode: HeaderMode.LIST, subTitle: '자산' },
    gnb: { isVisible: false },
  },
  '/user/assets/subscription-history': {
    header: { mode: HeaderMode.LIST, subTitle: '자산' },
    gnb: { isVisible: false },
  },
  '/user/assets': {
    header: { mode: HeaderMode.LIST, subTitle: '자산' },
    gnb: { isVisible: false },
  },
  '/deposit/account-register': {
    header: { mode: HeaderMode.LIST, subTitle: '예치금 가상계좌 개설 발급 동의' },
    gnb: { isVisible: false },
  },
  '/deposit/withdraw': {
    header: { mode: HeaderMode.LIST, subTitle: '예치금 출금하기' },
    gnb: { isVisible: false },
  },
  '/deposit/account-change': {
    header: { mode: HeaderMode.LIST, subTitle: '출금 계좌 변경' },
    gnb: { isVisible: false },
  },
  '/mobile/user/my/profile': {
    header: { mode: HeaderMode.LIST, subTitle: '기본 정보 설정' },
    gnb: { isVisible: false },
  },
  '/mobile/user/my/profile/address': {
    header: { mode: HeaderMode.LIST, subTitle: '주소' },
    gnb: { isVisible: false },
  },

  '/mobile/user/my/withdrawal': {
    header: { mode: HeaderMode.LIST, subTitle: '회원 탈퇴' },
    gnb: { isVisible: false },
  },
  '/mobile/user/my/investment-settings': {
    header: { mode: HeaderMode.LIST, subTitle: '투자 정보 설정' },
    gnb: { isVisible: false },
  },
  '/mobile/user/apply-investor': {
    header: { mode: HeaderMode.LIST, subTitle: '투자자 유형 변경 신청' },
    gnb: { isVisible: false },
  },
  '/mobile/user/my/notification': {
    header: { mode: HeaderMode.LIST, subTitle: '알림 설정' },
    gnb: { isVisible: false },
  },
  '/mobile/user/support/inquiry-history': {
    header: { mode: HeaderMode.LIST, subTitle: '문의하기' },
    gnb: { isVisible: false },
  },
  '/mobile/user/support/inquiry-history/[id]': {
    header: { mode: HeaderMode.LIST, subTitle: '문의하기' },
    gnb: { isVisible: false },
  },
  '/mobile/user/support/inquiry': {
    header: { mode: HeaderMode.LIST, subTitle: '1:1 문의하기' },
    gnb: { isVisible: false },
  },
  '/supports/faqs': {
    header: { mode: HeaderMode.LIST, subTitle: 'FAQ' },
    gnb: { isVisible: false },
  },
  '/investor/suitability': {
    header: { mode: HeaderMode.DETAIL },
    gnb: { isVisible: false },
  },
  '/ocr': {
    header: { mode: HeaderMode.LIST, subTitle: '본인 실명인증' },
    gnb: { isVisible: false },
  },
  '/sign-up/corporate': {
    header: { mode: HeaderMode.LIST, subTitle: '법인 회원가입 신청' },
    gnb: { isVisible: false },
  },
  '/mobile/notifications': {
    header: { mode: HeaderMode.LIST, subTitle: '알림' },
    gnb: { isVisible: false },
  },
  '/terms': {
    header: { mode: HeaderMode.LIST, subTitle: '이용약관' },
    gnb: { isVisible: false },
  },
};

export const defaultLayoutConfig: LayoutConfig = {
  header: { mode: HeaderMode.MAIN },
  gnb: { isVisible: true },
};

export const getLayoutConfigByPathAndScreen = (
  pathname: string,
  screenSize: ScreenSize,
): LayoutConfig | null => {
  if (!isMobileScreen(screenSize)) return null;

  if (routeLayoutConfigs[pathname]) return routeLayoutConfigs[pathname];

  for (const [route, config] of Object.entries(routeLayoutConfigs)) {
    if (route.includes('[') && route.includes(']')) {
      const routePattern = route.replace(/\[.*?\]/g, '[^/]+');
      if (new RegExp(`^${routePattern}$`).test(pathname)) return config;
    }
  }

  const sortedRoutes = Object.keys(routeLayoutConfigs).sort((a, b) => b.length - a.length);
  for (const route of sortedRoutes) {
    if (pathname.startsWith(route) && route !== '/') return routeLayoutConfigs[route];
  }

  return defaultLayoutConfig;
};
