pipeline {
    agent {
        kubernetes {
            yaml """
            apiVersion: v1
            kind: Pod
            spec:
              containers:
              - name: node
                image: node:20
                command:
                - cat
                tty: true
              - name: kaniko
                image: gcr.io/kaniko-project/executor:debug
                command:
                - /busybox/sh
                args:
                - -c
                - "while true; do sleep 30; done"
                volumeMounts:
                - name: aws-secret
                  mountPath: /kaniko/.aws
              - name: aws-cli
                image: amazon/aws-cli:latest
                command:
                - /bin/sh
                args:
                - -c
                - "trap : TERM INT; sleep infinity & wait"
                volumeMounts:
                - name: aws-secret
                  mountPath: /root/.aws
              volumes:
              - name: aws-secret
                secret:
                  secretName: aws-secret
            """
        }
    }
    parameters {
        string(name: 'BRANCH_NAME', defaultValue: 'release', description: 'Branch name')
        string(name: 'DOCKER_FILE', defaultValue: 'Dockerfile', description: 'Dockerfile name')
        string(name: 'NODE_ENV', defaultValue: 'dev', description: 'Node environment')
    }
    environment {
        AWS_REGION = 'ap-northeast-2'
        ECR_REPO = '034523212152.dkr.ecr.ap-northeast-2.amazonaws.com/numit-web'
        APP_REPO = 'https://<EMAIL>/ARTBLOC/NUMIT/_git/NUMIT_WEB'
        MANIFEST_REPO = 'https://<EMAIL>/ARTBLOC/NUMIT/_git/NUMIT_EKS'
        K8S_NAMESPACE = 'numit-app'
    }
    stages {
        stage('Get Version') {
            steps {
                container('aws-cli') {
                    script {
                        // ECR에서 현재 버전 가져오기
                        def currentVersion = sh(
                            script: """
                                aws ecr describe-images \
                                    --repository-name numit-web \
                                    --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[?contains(@, `latest`) == `false`]' \
                                    --output text \
                                    --region ${AWS_REGION} | tr '\\t' '\\n' | grep -v latest | sort -V | tail -n 1 || echo "0.1.0"
                            """,
                            returnStdout: true
                        ).trim()
                        echo "Current version from ECR: ${currentVersion}"
                        
                        // 버전 형식 확인 및 처리
                        def newVersion
                        if (currentVersion == null || currentVersion == 'None') {
                            currentVersion = '0.0.0'
                        } else if (currentVersion.contains('-')) {
                            currentVersion = currentVersion.split('-')[0]
                        }

                        echo "Value of the current version after format validation : ${currentVersion} / ${currentVersion.tokenize('.')}"

                        newVersion = currentVersion.tokenize('.')
                        newVersion[2] = (newVersion[2].toInteger() + 1).toString()
                        newVersion = newVersion.join('.')

                        env.IMAGE_TAG = newVersion
                        echo "Current version: ${currentVersion}"
                        echo "New version: ${env.IMAGE_TAG}"
                    }
                }
            }
        }

        stage('Clone Application Repository') {
            steps {
                git branch: "${params.BRANCH_NAME}", 
                url: "${env.APP_REPO}"
            }
        }

        stage('Build and Push Docker Image') {
            steps {
                container('kaniko') {
                    script {
                        def dockerfile = params.DOCKER_FILE;
                        def branchSuffix = "-${params.BRANCH_NAME}"
                        def finalTag = "${env.IMAGE_TAG}${branchSuffix}"
                        
                        sh '''
                            mkdir -p /kaniko/.docker
                            
                            cat > /kaniko/.docker/config.json <<EOF
                            {
                              "credHelpers": {
                                "034523212152.dkr.ecr.ap-northeast-2.amazonaws.com": "ecr-login"
                              }
                            }
                            EOF
                        '''
                        
                        sh """
                        /kaniko/executor \
                        --context=. \
                        --destination=$ECR_REPO:${finalTag} \
                        --dockerfile=${dockerfile} \
                        --verbosity=info \
                        --build-arg NODE_ENV=${params.NODE_ENV}
                        """
                        
                        // 이후 단계에서 사용하기 위해 태그 저장
                        env.FINAL_IMAGE_TAG = finalTag
                    }
                }
            }
        }

        // stage('Update Manifest') {
        //     steps {
        //         container('node') {
        //             script {
        //                 sh 'git clone ${MANIFEST_REPO} manifests'
        //                 sh """
        //                 cd manifests
        //                 sed -i 's|image:.*|image: ${ECR_REPO}:${BUILD_NUMBER}|' deployment.yaml
        //                 git config user.email "<EMAIL>"
        //                 git config user.name "Jenkins"
        //                 git commit -am "Update image to ${ECR_REPO}:${BUILD_NUMBER}"
        //                 git push
        //                 """
        //             }
        //         }
        //     }
        // }
        // stage('Trigger ArgoCD Sync') {
        //     steps {
        //         container('aws-cli') {
        //             script {
        //                 sh """
        //                 aws eks update-kubeconfig --region ${AWS_REGION} --name my-cluster
        //                 kubectl -n argocd rollout restart deployment/argocd-server
        //                 """
        //             }
        //         }
        //     }
        // }
    }
}
