export interface Terms {
  createdBy: string;
  status: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  is_required: boolean;
  category: TermsCategory;
  content: string;
  releated_feature: string;
  versions: any[];
  id: string;
  publishedAt: String;
  updatedBy: string;
}

export enum TermsCategory {
  MARKETING_USAGE = 'MARKETING_USAGE',
  TERMS_OF_USE = 'TERMS_OF_USE',
  PRIVACY_POLICY = 'PRIVACY_POLICY',
  BEHAVIOR_COLLECTION = 'BEHAVIOR_COLLECTION',
  INVESTMENT_RISK_DISCLOSURE = 'INVESTMENT_RISK_DISCLOSURE',
}
