'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import { ListResponse } from '@/shared/interface';
import env from '@/shared/lib/env.schema';

import { TermsRequest } from '../interface';
import { Terms } from '../types';

export const getTermsServer = async (params: TermsRequest): Promise<ListResponse<Terms>> => {
  const response = await fetch(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/terms?releated_feature=signup&category=${params.category}`,
    {
      cache: 'force-cache',
      next: {
        tags: ['terms', params.category],
        revalidate: 60 * 3,
      },
    },
  );

  if (!response.ok) {
    logServerError('get terms server error', {
      error: {
        params: {
          category: params.category,
        },
        response,
      },
    });
    throw new Error('Failed to fetch terms');
  }

  const data = await response.json();

  return data.data;
};
