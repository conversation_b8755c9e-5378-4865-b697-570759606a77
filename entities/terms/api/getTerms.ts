import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { TermsRequest } from '../interface';
import { Terms } from '../types';

export const getTerms = async (params: TermsRequest): Promise<ListResponse<Terms>> => {
  const response = await cmsPublicInstance.get('/contents/terms', {
    params: { ...params, releated_feature: 'signup' },
  });
  return response.data;
};
