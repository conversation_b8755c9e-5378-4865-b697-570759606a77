import React from 'react';

import { InquiryCategory, InquiryCategoryLabel } from '../types';

export const InquiryCategoryBadge = ({
  category,
  className,
}: {
  category: InquiryCategory;
  className?: string;
}) => {
  const label = InquiryCategoryLabel[category];

  const color = 'text-gray-900';
  const bgColor = 'bg-gray-200';

  return (
    <div className={`${className}`}>
      <p
        className={`inline h-6 rounded px-2 py-[3px] text-xs font-bold leading-[18px] ${color} ${bgColor}`}
      >
        {label}
      </p>
    </div>
  );
};
