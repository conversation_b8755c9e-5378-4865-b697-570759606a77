import React from 'react';

import { InquiryAnswerStatus, InquiryAnswerStatusLabel } from '../types';

export const InquiryAnswerStatusBadge = ({
  answerStatus,
  className,
}: {
  answerStatus: InquiryAnswerStatus;
  className?: string;
}) => {
  const label = InquiryAnswerStatusLabel[answerStatus];

  const color = answerStatus === InquiryAnswerStatus.REQUEST ? 'text-blue-500' : 'text-green-500';

  const bgColor = answerStatus === InquiryAnswerStatus.REQUEST ? 'bg-blue-50' : 'bg-green-50';

  return (
    <div className={className}>
      <p
        className={`inline h-6 whitespace-nowrap rounded px-2 py-[3px] text-xs font-bold leading-[18px] ${color} ${bgColor}`}
      >
        {label}
      </p>
    </div>
  );
};
