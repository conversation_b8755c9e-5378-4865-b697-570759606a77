import { NotificationChannel } from '@/shared/types';

import { InquiryCategory } from '../types';

export interface CreateInquiryPayload {
  privacyAgreement: boolean;
  status: string;
  notificationChannel: NotificationChannel[];
  title: string;
  content: string;
  category: InquiryCategory;
  answerStatus: string;
  attachFiles: File[];
  userId: string;
}

export interface CreateIssuerInquiryPayload {
  status: string;
  securitiesId: string;
  issuerName: string;
  category: string;
  userId: string;
  title: string;
  answerStatus: string;
}
