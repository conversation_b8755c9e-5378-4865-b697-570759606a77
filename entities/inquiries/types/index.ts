import { NotificationChannel } from '@/shared/types';

export enum InquiryCategory {
  NORMAL = 'NORMAL',
  OTHER = 'OTHER',
}

export const InquiryCategoryLabel = {
  [InquiryCategory.NORMAL]: '일반',
  [InquiryCategory.OTHER]: '기타',
};

export interface InquiryHistory {
  createdBy: string;
  status: string;
  publishedAt: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  privacyAgreement: boolean;
  notificationChannel: NotificationChannel[];
  title: string;
  content: string;
  category: InquiryCategory;
  answer?: string;
  userId: string;
  answerStatus: InquiryAnswerStatus;
  versions: any[];
  id: string;
}

export enum InquiryAnswerStatus {
  REQUEST = 'REQUEST',
  ANSWER = 'ANSWER',
}

export const InquiryAnswerStatusLabel = {
  [InquiryAnswerStatus.REQUEST]: '답변 대기중',
  [InquiryAnswerStatus.ANSWER]: '답변완료',
};
