import React from 'react';

import { SubscriptionCmsContent } from '@/entities/subscriptions/types';

import { ListRequest, ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { InquiryHistory } from '../types';

export const getSubscriptionInquiries = async ({
  securitiesId,
  userId,
  params,
}: {
  securitiesId: string;
  userId: string;
  params: ListRequest;
}): Promise<ListResponse<InquiryHistory>> => {
  const response = await cmsPublicInstance.get('/contents/issuer-qna', {
    params: {
      securitiesId,
      userId,
      ...params,
    },
  });
  return response.data;
};
