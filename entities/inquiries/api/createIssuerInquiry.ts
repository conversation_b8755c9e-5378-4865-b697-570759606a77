import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { CreateIssuerInquiryPayload } from '../interface';

export const createIssuerInquiry = async (data: CreateIssuerInquiryPayload) => {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value as string);
    }
  });

  const response = await cmsPublicInstance.post('/contents/issuer-qna', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response;
};
