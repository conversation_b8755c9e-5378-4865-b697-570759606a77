import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { CreateInquiryPayload } from '../interface';

export const createInquiry = async (data: CreateInquiryPayload) => {
  const formData = new FormData();

  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      if (key === 'attachFiles' && Array.isArray(value)) {
        value.forEach((file) => {
          if (file instanceof File) {
            formData.append('attachFile', file);
          }
        });
      } else {
        formData.append(key, value as string);
      }
    }
  });

  const response = await cmsPublicInstance.post('/contents/qna', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response;
};
