import { ListRequest, ListResponse, ScrollApiResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { InquiryHistory } from '../types';

export const getMyInquiryHistory = async (
  userId: string,
  params: ListRequest,
): Promise<ListResponse<InquiryHistory>> => {
  const response = await cmsPublicInstance.get(`/contents/qna?userId=${userId}`, {
    params,
  });
  return response.data;
};

export const getMyInquiryHistoryList = async (
  userId: string,
  request: ListRequest,
): Promise<ScrollApiResponse<InquiryHistory>> => {
  const response = await getMyInquiryHistory(userId, request);

  return {
    data: response.data,
    nextPage: request.page ? request.page + 1 : 2,
    isLast: response.data.length < (request.perPage ?? 10),
    totalCount: response.meta.total,
  };
};
