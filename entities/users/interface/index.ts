import { NotificationChannel, NotificationType } from '@/shared/types';

import { User, UserCorporateProfile, UserProfile } from '../types';

export interface UpdateUserPayload {
  name?: string;
  mobileNumber?: string;
  email?: string;
}

export interface UpdateUserNotificationPayload {
  type: NotificationType[];
  channel: NotificationChannel[];
}

export interface UpdatePasswordPayload {
  password: string;
  newPassword: string;
}

export interface CreateWithdrawReasonPayload {
  reason: string;
  description?: string;
  userId: string;
}

export interface UserNotification {
  userId: string;
  type: NotificationType[];
  channel: NotificationChannel[];
}

export interface UpdateCorporateUserPayload {
  representativeName?: string;
  address1?: string;
  address2?: string;
  zipCode?: string;
  managerName?: string;
  managerMobileNumber?: string;
  brc?: File[];
}

export interface GeneralUserResponse extends Omit<User, 'profile'> {
  userProfile?: UserProfile;
}

export interface CorporateUserResponse extends Omit<User, 'profile'> {
  userCorporateProfile?: UserCorporateProfile;
}
