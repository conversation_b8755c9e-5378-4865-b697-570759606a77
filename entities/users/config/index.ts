import { WithdrawalCategory, WithdrawalCategoryLabel } from '../types';

export const WithdrawalCategorySearchOptions = [
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.REJOIN],
    value: WithdrawalCategory.REJOIN,
  },
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.CONTENT_QUALITY],
    value: WithdrawalCategory.CONTENT_QUALITY,
  },
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.CUSTOMER_SUPPORT],
    value: WithdrawalCategory.CUSTOMER_SUPPORT,
  },
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.SYSTEM_FAILURE],
    value: WithdrawalCategory.SYSTEM_FAILURE,
  },
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.PRIVACY_CONCERN],
    value: WithdrawalCategory.PRIVACY_CONCERN,
  },
  {
    label: WithdrawalCategoryLabel[WithdrawalCategory.OTHER],
    value: WithdrawalCategory.OTHER,
  },
];
