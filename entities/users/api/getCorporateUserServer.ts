'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

export const getCorporateUserServer = async (accessToken: string) => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/user/corporate`, {
    cache: 'force-cache',
    next: { tags: ['user', 'corporate'], revalidate: 60 * 3 },
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    logServerError('get corporate user server error', {
      error: {
        accessToken,
      },
    });
  }

  const data = await response.json();

  return data.data;
};
