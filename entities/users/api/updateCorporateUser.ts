import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { UpdateCorporateUserPayload } from '../interface';

export const updateCorporateUser = async (payload: UpdateCorporateUserPayload) => {
  const formData = new FormData();

  // 객체의 모든 키-값 쌍을 FormData에 추가
  Object.entries(payload).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value);
    }
  });

  const response = await userPrivateInstance.patch('/user/corporate', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
