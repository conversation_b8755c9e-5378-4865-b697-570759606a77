'use server';

import { auth } from '@/app/auth';
import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

export const deleteUser = async () => {
  const session = await auth();

  const data = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/user`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${session?.user.accessToken}`,
    },
  });

  const response = await data.json();

  if (response.statusCode !== 200) {
    logServerError('delete user server error', {
      error: {
        user: session?.user,
      },
    });
  }

  return { success: response.statusCode === 200 };
};
