'use server';

import { auth } from '@/app/auth';
import { logServerError } from '@artbloc/next-js-logger/server';
import axios from 'axios';

import env from '@/shared/lib/env.schema';

import { CreateWithdrawReasonPayload } from '../interface';

export const createWithdrawReason = async ({
  reason,
  description,
  userId,
}: CreateWithdrawReasonPayload) => {
  const session = await auth();

  const response = await axios.post(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/withdraw`,
    {
      userId,
      withdrawData: {
        reason,
        ...(description && { description }),
      },
    },
    {
      headers: {
        Authorization: `Bearer ${session?.user.accessToken}`,
      },
    },
  );
  const success = response.data.statusCode === 201;

  if (!success) {
    logServerError('create withdraw reason server error', {
      error: {
        user: session?.user,
      },
    });
  }

  return { success };
};
