import { InvestorQualificationStatus } from '@/entities/investor/interface';
import { InvestorQualificationType, PropensityLevelEnum } from '@/entities/investor/types';

import { UserCat } from '@/shared/types';

export interface User {
  id: string;
  account: string;
  createdAt: string;
  kycStatus: KycStatus;
  restrictionStatus: string;
  role: number;
  investorQualification: UserInvestorQualification;
  type: UserCat;
  updatedAt: string;
  userProfile?: UserProfile;
  userCorporateProfile?: UserCorporateProfile;
  userAuthOCR?: UserAuthOCR;
  investorPropensity?: UserInvestorPropensity;
  investorSuitabilityTest?: UserInvestorSuitability;
}

export interface UserProfile {
  address1?: string;
  address2?: string;
  birthDate?: string;
  createdAt: string;
  deletedAt?: string;
  email: string;
  gender?: string;
  id: number;
  mobileNumber: string;
  name: string;
  updatedAt: string;
  userId: string;
  zipCode?: string;
}

export interface UserCorporateProfile {
  id: number;
  email: string;
  companyName: string;
  crn: string;
  brn: string;
  representativeName: string;
  address1: string;
  address2: string;
  zipCode: string;
  managerName: string;
  managerPhoneNumber: string;
  managerMobileNumber: string;
  managerEmail: string;
  isApproved: boolean;
  brc: string[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  userId: string;
}

export enum WithdrawalCategory {
  REJOIN = 'REJOIN',
  CONTENT_QUALITY = 'CONTENT_QUALITY',
  CUSTOMER_SUPPORT = 'CUSTOMER_SUPPORT',
  SYSTEM_FAILURE = 'SYSTEM_FAILURE',
  PRIVACY_CONCERN = 'PRIVACY_CONCERN',
  OTHER = 'OTHER',
}

export const WithdrawalCategoryLabel = {
  [WithdrawalCategory.REJOIN]: '재가입을 위한 사유',
  [WithdrawalCategory.CONTENT_QUALITY]: '콘텐츠 퀄리티 및 서비스 정보 부족',
  [WithdrawalCategory.CUSTOMER_SUPPORT]: '고객 지원 만족도 떨어짐',
  [WithdrawalCategory.SYSTEM_FAILURE]: '시스템 장애 또는 기술적 이슈',
  [WithdrawalCategory.PRIVACY_CONCERN]: '개인정보 관심 또는 데이터 노출 우려',
  [WithdrawalCategory.OTHER]: '기타 사유',
};

export interface NewsletterResponse {
  created_at: string;
  email: string;
  last_subscribed_at: string;
  message: string;
  status: string;
  subscriber_id: string;
  verified_at: string;
  updated_at: string;
}

export enum MyProfileSteps {
  CONFIRM = 'CONFIRM',
  PROFILE = 'PROFILE',
  CHANGE = 'CHANGE',
}

export enum KycStatus {
  NON_KYC = 'NON_KYC',
  KYC = 'KYC',
}

export interface UserInvestorSuitability {
  consent: boolean;
  consentAt: string;
  createdAt: string;
  daletedAt?: string;
  expiredAt: string;
  id: number;
  investorSuitabilityQuestionId: string;
  updatedAt?: string;
  userId: string;
}

export interface UserInvestorPropensity {
  consent: boolean;
  consentAt: string;
  convertedScore: number;
  createdAt: string;
  expiredAt: string;
  deletedAt?: string;
  grade: PropensityLevelEnum;
  id: number;
  report: string;
  score: number;
  updatedAt?: string;
  userId: string;
}

export interface UserAuthOCR {
  createdAt: string;
  deletedAt?: string;
  id: number;
  ocrInfo: string;
  ocrInfoHash: string;
  registeredAt: string;
  updatedAt: string;
  userId: string;
}

export interface UserInvestorQualification {
  applying?: {
    appliedAt: string;
    approvedAt?: string;
    createdAt?: string;
    deletedAt?: string;
    expiredAt?: string;
    id: number;
    qualificationStatus: InvestorQualificationStatus;
    qualificationType: InvestorQualificationType;
    rejectedAt?: string;
    updatedAt?: string;
    userId: string;
  };
  current?: {
    appliedAt?: string;
    approvedAt?: string;
    createdAt: string;
    deletedAt?: string;
    expiredAt: string;
    id: number;
    qualificationStatus: InvestorQualificationStatus;
    qualificationType: InvestorQualificationType;
    rejectedAt?: string;
    updatedAt?: string;
    userId: string;
  };
}
