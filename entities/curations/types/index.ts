export interface Curation {
  category: CurationCategory;
  content: string;
  createdAt: string;
  createdBy: string;
  id: string;
  publishedAt: string;
  status: string;
  thumbnail: {
    fileId: string;
    name: string;
    url: string;
  };
  title: string;
  updatedAt: string;
  updatedBy: string;
  version: number;
  versions: any;
  tags: string;
  description: string;
  relatedContents?: Curation[];
  metaDescription: string;
  mediaType: CurationMediaType;
}

export enum CurationCategory {
  'issuer' = 'issuer',
  'investor' = 'investor',
  'press' = 'press',
}

export const CurationCategoryLabel = {
  [CurationCategory.issuer]: '발행인',
  [CurationCategory.investor]: '투자자',
  [CurationCategory.press]: '기자',
};

export enum CurationMediaType {
  'article' = 'article',
  'cardnews' = 'cardnews',
}
