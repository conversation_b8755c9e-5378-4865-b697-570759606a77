import React from 'react';

import { Curation } from '@/entities/curations/types';
import { CurationTagBadge } from '@/entities/curations/ui/CurationTagBadge';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';

export const CurationMainCard = (curation: Curation) => {
  const { YYYYMMDD } = utilFormats();
  const { title, thumbnail, createdAt, tags } = curation;
  const { routerPush } = useWebViewRouter();

  return (
    <div
      className="cursor-pointer space-y-5"
      onClick={() => routerPush(`/contents/curations/${curation.id}`)}
    >
      <div className="relative aspect-[386/248]">
        <FallbackImage
          src={thumbnail.url}
          alt={title}
          fill
          sizes="(max-width: 744px) 680px, (max-width: 1440px) 590px, 386px"
          className="rounded-[10px]"
          style={{ objectFit: 'cover' }}
        />
      </div>
      <div className="space-y-[6px] sm:space-y-2">
        <p className="text-xs text-gray-700 sm:text-sm">{YYYYMMDD(createdAt)}</p>
        <p className="text-base font-bold sm:text-xl ml:text-2xl">{title}</p>
      </div>
      {tags?.length > 0 && (
        <div className="mt-[6px] flex gap-1 sm:mt-2">
          {tags?.split(',').map((tag) => <CurationTagBadge key={tag} tag={tag} />)}
        </div>
      )}
    </div>
  );
};
