import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';

import { Curation } from '../types';

interface CurationRecommendCardProps {
  curation: Curation;
}

export const CurationRecommendCard = ({ curation }: CurationRecommendCardProps) => {
  const { title, thumbnail, id } = curation;

  const { routerPush } = useWebViewRouter();

  return (
    <div
      className="cursor-pointer space-y-4 sm:space-y-6"
      onClick={() => routerPush(`/contents/curations/${id}`)}
    >
      <div className="relative aspect-[387/248]">
        <FallbackImage
          src={thumbnail.url}
          alt={title}
          fill
          sizes="(max-width: 744px) 680px, (max-width: 1440px) 590px, 386px"
          className="rounded-[10px]"
        />
      </div>
      <h4 className="text-[17px] font-bold sm:text-lg ml:text-xl">{title}</h4>
    </div>
  );
};
