import Link from 'next/link';
import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { NewBadge } from '@/shared/ui/NewBadge';
import { Separator } from '@/shared/ui/shadcn/separator';

import { Curation } from '../types';
import { CurationTagBadge } from './CurationTagBadge';

interface CurationCardProps {
  curation: Curation;
  isLastItem: boolean;
  isLastPage: boolean;
  ref: (node?: Element | null) => void;
}

export const CurationCard = ({ curation, isLastItem, isLastPage, ref }: CurationCardProps) => {
  const { title, createdAt, description } = curation;

  const { YYYYMMDD } = utilFormats();

  const { routerPush } = useWebViewRouter();

  return (
    <>
      <div
        className="flex cursor-pointer flex-col gap-5 sm:flex-row sm:py-10"
        onClick={() => routerPush(`/contents/curations/${curation.id}`)}
      >
        <div className="relative aspect-[327/210] sm:h-[212px] ml:h-[248px] ml:w-[396px]">
          <FallbackImage
            src={curation.thumbnail.url}
            alt={curation.title}
            fill
            sizes="(max-width: 996px) 100vw, 386px"
            className="rounded-xl"
            style={{ objectFit: 'cover' }}
          />
          <div className="absolute left-0 top-0 h-full w-full rounded-xl bg-black/10 stroke-black" />
        </div>
        <div>
          <p className="text-xs text-gray-700 sm:text-sm">{YYYYMMDD(createdAt)}</p>
          <div className="mt-[6px] line-clamp-1 flex items-center gap-[6px] text-[17px] font-bold sm:mt-2 sm:text-xl ml:mt-6 ml:text-2xl">
            <h3>{title}</h3>

            <NewBadge size="md" date={createdAt} />
          </div>
          <p className="mt-2 line-clamp-2 text-base text-gray-700 sm:mt-3 ml:mt-4">{description}</p>
          {curation.tags && (
            <div className="mt-4 flex gap-[6px] sm:mt-6 ml:mt-12">
              {curation.tags?.split(',').map((tag) => <CurationTagBadge key={tag} tag={tag} />)}
            </div>
          )}
        </div>
      </div>
      {!isLastPage && <div ref={ref} />}
      {!isLastItem && <Separator className="my-10 bg-gray-300 sm:my-0" />}
    </>
  );
};
