import { Curation } from '@/entities/curations/types';
import { CurationTagBadge } from '@/entities/curations/ui/CurationTagBadge';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';

export const MobileContentsCurationCard = (curation: Curation) => {
  const { routerPush } = useWebViewRouter();

  return (
    <div
      className="space-y-[10px]"
      onClick={() => routerPush(`/contents/curations/${curation.id}`)}
    >
      <FallbackImage
        src={curation.thumbnail.url}
        alt={curation.title}
        width={158}
        height={100}
        className="h-[100px] w-[158px] rounded object-cover"
      />
      <div className="space-y-2">
        <h4 className="line-clamp-2 text-sm leading-[150%]">{curation.title}</h4>
        <div className="flex gap-1">
          {curation.tags?.split(',').map((tag) => <CurationTagBadge key={tag} tag={tag} />)}
        </div>
      </div>
    </div>
  );
};
