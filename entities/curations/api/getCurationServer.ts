'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

import { Curation } from '../types';

export const getCurationServer = async (id: string): Promise<Curation> => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/curation/${id}`, {
    cache: 'force-cache',
    next: {
      tags: ['curations', id],
      revalidate: 60 * 1,
    },
  });

  if (!response.ok) {
    logServerError('get curation server error', {
      error: {
        params: {
          id,
        },
        response,
      },
    });
    throw new Error('Failed to fetch curation');
  }

  const data = await response.json();

  return data.data;
};
