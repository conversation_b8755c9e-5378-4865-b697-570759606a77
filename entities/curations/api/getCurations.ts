import { ListResponse, ScrollApiResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { CurationListRequest } from '../interface';
import { Curation } from '../types';

export const getCurations = async (
  params: CurationListRequest,
): Promise<ListResponse<Curation>> => {
  const response = await cmsPublicInstance.get('/contents/curation', { params });
  return response.data;
};

export const getInfiniteCurations = async (
  request: CurationListRequest,
): Promise<ScrollApiResponse<Curation>> => {
  const response = await getCurations(request);

  return {
    data: response.data,
    nextPage: request.page ? request.page + 1 : 2,
    isLast: response.data.length < (request.perPage ?? 10),
    totalCount: response.meta.total,
  };
};
