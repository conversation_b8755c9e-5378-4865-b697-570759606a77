import { SecuritiesType, SecuritiesTypeLabel } from '@/entities/subscriptions/types';

import { DisclosureCategory, DisclosureCategoryLabel } from '../types';

export const SecuritiesTypeSearchOptions = [
  {
    label: '전체',
    value: 'all',
  },
  {
    label: SecuritiesTypeLabel[SecuritiesType.INVESTMENT_CONTRACT_SECURITIES],
    value: SecuritiesType.INVESTMENT_CONTRACT_SECURITIES,
  },
  {
    label: SecuritiesTypeLabel[SecuritiesType.INCOME_SECURITIES],
    value: SecuritiesType.INCOME_SECURITIES,
  },
];

export const DisclosureCategorySearchOptions = [
  {
    label: '전체',
    value: 'all',
  },
  {
    label: DisclosureCategoryLabel[DisclosureCategory.REGULAR],
    value: DisclosureCategory.REGULAR,
  },
  {
    label: DisclosureCategoryLabel[DisclosureCategory.SECURITIES_ISSUANCE],
    value: DisclosureCategory.SECURITIES_ISSUANCE,
  },
  {
    label: DisclosureCategoryLabel[DisclosureCategory.PERIODIC],
    value: DisclosureCategory.PERIODIC,
  },
  {
    label: DisclosureCategoryLabel[DisclosureCategory.OTHER],
    value: DisclosureCategory.OTHER,
  },
];
