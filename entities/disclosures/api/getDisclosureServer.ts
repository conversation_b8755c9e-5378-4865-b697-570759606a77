'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

import { Disclosure } from '../types';

export const getDisclosureServer = async (id: string): Promise<Disclosure> => {
  const response = await fetch(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/disclosure/${id}`,
    {
      cache: 'force-cache',
      next: {
        tags: ['disclosures', id],
        revalidate: 60 * 1,
      },
    },
  );

  if (!response.ok) {
    logServerError('get disclosure server error', {
      error: {
        params: {
          id,
        },
        response,
      },
    });
    throw new Error('Failed to fetch curation');
  }

  const data = await response.json();

  return data.data;
};
