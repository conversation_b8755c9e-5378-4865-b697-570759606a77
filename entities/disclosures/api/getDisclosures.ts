import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { DisclosureListRequest } from '../interface';
import { Disclosure } from '../types';

export const getDisclosures = async (
  params: DisclosureListRequest,
): Promise<ListResponse<Disclosure>> => {
  const response = await cmsPublicInstance.get('/contents/issuer-disclosure', { params });
  return response.data;
};
