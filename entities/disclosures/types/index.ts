import { SecuritiesType } from '@/entities/subscriptions/types';

import { AttachFile } from '@/shared/types';

export interface Disclosure {
  id: string;
  content: string;
  category: DisclosureCategory;
  createdAt: string;
  createdBy: string;
  issuer: string;
  publishedAt: string;
  scope: DisclosureScope;
  securitiesName: string;
  securitiesType: SecuritiesType;
  status: string;
  updatedAt: string;
  updatedBy: string;
  title: string;
  version: number;
  versions: any[];
  attachFiles: AttachFile[];
}

export enum DisclosureScope {
  INVESTOR = 'INVESTOR',
  ISSUER = 'ISSUER',
}
export const DisclosureScopeLabel = {
  [DisclosureScope.INVESTOR]: '투자자',
  [DisclosureScope.ISSUER]: '발행인',
};

export enum DisclosureCategory {
  REGULAR = 'REGULAR',
  SECURITIES_ISSUANCE = 'SECURITIES_ISSUANCE',
  PERIODIC = 'PERIODIC',
  OTHER = 'OTHER',
}

export const DisclosureCategoryLabel = {
  [DisclosureCategory.REGULAR]: '수시공시',
  [DisclosureCategory.SECURITIES_ISSUANCE]: '발행공시',
  [DisclosureCategory.PERIODIC]: '정기공시',
  [DisclosureCategory.OTHER]: '기타공시',
};
