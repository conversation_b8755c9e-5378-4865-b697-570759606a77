import { usePathname, useRouter } from 'next/navigation';
import React from 'react';

import {
  Disclosure,
  DisclosureCategoryLabel,
  DisclosureScopeLabel,
} from '@/entities/disclosures/types';
import { SecuritiesTypeLabel } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';

interface DisclosureMobileCardProps {
  disclosure: Disclosure;
  isLast: boolean;
}

export const DisclosureMobileCard = ({ disclosure, isLast }: DisclosureMobileCardProps) => {
  const { YYYYMMDD } = utilFormats();
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div
      onClick={() =>
        router.replace(`${pathname}?disclosureId=${disclosure.id}`, {
          scroll: false,
        })
      }
      className={`cursor-pointer space-y-2 border-t border-gray-300 py-4 text-gray-700 ${isLast && 'border-b'}`}
    >
      <div className="flex gap-[6px] text-xs">
        <p>{YYYYMMDD(disclosure.createdAt, 'YYYY-MM-DD hh:mm')}</p>∙
        <p>{SecuritiesTypeLabel[disclosure.securitiesType]}</p>
      </div>
      <h4 className="font-bold text-gray-900">{disclosure.title}</h4>
      <div className="flex gap-[6px] text-xs">
        <p>{disclosure.issuer}</p>∙<p>{DisclosureCategoryLabel[disclosure.category]}</p>∙
        <p>{DisclosureScopeLabel[disclosure.scope]}</p>
      </div>
    </div>
  );
};
