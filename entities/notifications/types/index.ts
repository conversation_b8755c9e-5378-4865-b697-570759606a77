export interface Notification {
  id: string;
  title: string;
  message: string;
  status: NotificationStatus;
  sourceType: string;
  contextData?: string;
  createdAt: string;
  updatedAt?: string;
  templatedId?: string;
  categoryId: string;
  channelId: string;
  category: {
    id: string;
    name: string;
    code: string;
    description: string;
    isRequired: boolean;
    isActive: boolean;
    createdAt: string;
    updatedAt?: string;
  };
  channel: {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
    createdAt: string;
    updatedAt?: string;
  };
  recipients: {
    id: string;
    status: RecipientStatus;
    email?: string;
    phone?: string;
    pushToken?: string;
    sentAt: string;
    readAt?: string;
    failureReason?: string;
    retryCount: number;
    createdAt: string;
    updatedAt?: string;
    attributes?: string;
    notificationId: string;
    userId: string;
  }[];
}

export enum RecipientStatus {
  PENDING = 'PENDING',
  SENT = 'SENT', // 실질적으로 사용하는 상태 , 정상적으로 보내진 상태
  READ = 'READ', // 실질적으로 사용하는 상태, 읽은 상태
  FAILED = 'FAILED',
}

export enum NotificationStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED', // 이건 API 쪽에서 고정으로 필터링해서 보내드릴게요
  FAILED = 'FAILED',
}
