import { ListResponse, ScrollApiResponse } from '@/shared/interface';
import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';

import { NotificationRequest } from '../interface';
import { Notification } from '../types';

export const getNotifications = async (
  userId: string,
  params: NotificationRequest,
): Promise<ListResponse<Notification>> => {
  const response = await userPublicInstance.get(`/notification/notification/user/${userId}`, {
    params,
  });
  return response.data;
};

export const getNotificationsList = async (
  userId: string,
  params: NotificationRequest,
): Promise<ScrollApiResponse<Notification>> => {
  const response = await getNotifications(userId, params);

  return {
    data: response.data,
    nextPage: params.page ? params.page + 1 : 2,
    isLast: response.data.length < (params.perPage ?? 10),
    totalCount: response.meta.total,
  };
};
