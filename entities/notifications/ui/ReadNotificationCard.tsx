import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { FallbackImage } from '@/shared/ui/FallbackImage';

import { Notification } from '../types';

interface ReadNotificationCardProps {
  notification: Notification;
}

export const ReadNotificationCard = ({ notification }: ReadNotificationCardProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="mx-5 space-y-2 text-gray-400">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-[6px]">
          <FallbackImage src="/icons/notice_off.png" alt="notice_off" width={24} height={24} />
          <p className="text-sm font-semibold">{notification.title}</p>
        </div>
        <p className="text-xs">{YYYYMMDD(notification.createdAt)}</p>
      </div>
      <div className="ml-auto rounded-lg border border-blue-gray-50 bg-blue-gray-00 px-[14px] py-2 text-sm sm:w-[330px]">
        {notification.message}
      </div>
    </div>
  );
};
