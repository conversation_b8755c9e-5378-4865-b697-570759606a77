import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { FallbackImage } from '@/shared/ui/FallbackImage';

import { Notification } from '../types';

interface UnreadNotificationCardProps {
  notification: Notification;
}

export const UnreadNotificationCard = ({ notification }: UnreadNotificationCardProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="mx-5 space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-[6px]">
          <FallbackImage src="/icons/notice_on.png" alt="notice_on" width={24} height={24} />
          <p className="text-sm font-semibold">{notification.title}</p>
        </div>
        <p className="text-xs text-gray-500">{YYYYMMDD(notification.createdAt)}</p>
      </div>
      <div className="ml-auto w-[330px] rounded-lg border border-blue-gray-50 bg-blue-gray-00 px-[14px] py-2 text-sm">
        {notification.message}
      </div>
    </div>
  );
};
