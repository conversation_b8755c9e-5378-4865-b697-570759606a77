import { ListRequest } from '@/shared/interface';

import { MyDepositHistory, UpdateDepositEventCode } from '../types';

export interface UpdateDepositPayload {
  userId: string;
  depositAmount: number;
  eventCode: UpdateDepositEventCode;
  remark: string;
}

export interface VerifyWithdrawAccountPayload {
  bankCode: string;
  accountNumber: string;
}

export interface CreateVirtualAccountPayload {
  bankCode: string;
  accountNumber: string;
}

export interface GetMyDepositHistoryPayload extends ListRequest {
  searchBeginAt?: Date;
  searchEndAt?: Date;
}

export interface WithdrawPayload {
  userId: string;
  depositAmount: number;
}

export interface DepositAccountPayload {
  userId: string;
  depositAmount: number;
  eventCode: string;
  remark: string;
}

export interface MyDepositHistoryResponse {
  list: MyDepositHistory[];
  totalPage: number;
  totalCount: number;
}
