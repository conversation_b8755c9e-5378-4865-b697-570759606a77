import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { WithdrawPayload } from '../interface';

export const withdrawCorporateDeposit = async (payload: WithdrawPayload) => {
  const response = await userPrivateInstance.post('/asset/deposit/corporate/withdraw', {
    userId: payload.userId,
    depositAmount: payload.depositAmount,
    eventCode: 'WITHDRAW',
    remark: '출금',
  });

  return response.data;
};
