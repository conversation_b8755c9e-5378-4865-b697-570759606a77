import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { CreateVirtualAccountPayload } from '../interface';

export const updateWithdrawAccount = async (payload: CreateVirtualAccountPayload) => {
  const { accountNumber, bankCode } = payload;

  const response = await userPrivateInstance.put('/asset/account/user', {
    accountNumber,
    bankCode,
    etc: '계좌 변경 요청',
  });

  return response.data;
};
