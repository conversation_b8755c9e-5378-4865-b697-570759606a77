import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { DepositAccountPayload } from '../interface';

export const tempDepositAccount = async (payload: DepositAccountPayload) => {
  const response = await userPrivateInstance.post('/asset/deposit', {
    userId: payload.userId,
    depositAmount: payload.depositAmount,
    eventCode: payload.eventCode,
    remark: payload.remark,
  });

  return response.data;
};
