import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { VerifyWithdrawAccountPayload } from '../interface';

export const updateCorporateAccount = async (
  accountId: string,
  payload: VerifyWithdrawAccountPayload,
) => {
  const response = await userPrivateInstance.put(`/asset/account/${accountId}/corporate`, {
    ...payload,
    etc: '계좌변경',
  });

  return response.data;
};
