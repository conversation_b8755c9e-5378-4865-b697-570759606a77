'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

export const checkCorporateAccountExistenceServer = async (accessToken: string) => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/asset/account/corporate/exists`, {
    next: { tags: ['corporate', 'user', 'accountExistence'], revalidate: 0 },
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    logServerError('get corporate server error', {
      error: {
        accessToken,
      },
    });
  }

  const data = await response.json();

  return data.data;
};
