import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { WithdrawPayload } from '../interface';

export const withdrawUserDeposit = async (payload: WithdrawPayload) => {
  const response = await userPrivateInstance.post('/asset/deposit/user/withdraw', {
    userId: payload.userId,
    depositAmount: payload.depositAmount,
    eventCode: 'WITHDRAW',
    remark: '출금',
  });

  return response.data;
};
