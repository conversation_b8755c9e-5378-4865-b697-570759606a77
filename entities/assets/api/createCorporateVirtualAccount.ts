import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { CreateVirtualAccountPayload } from '../interface';

export const createCorporateVirtualAccount = async (payload: CreateVirtualAccountPayload) => {
  const { accountNumber, bankCode } = payload;

  const response = await userPrivateInstance.post('/asset/account/corporate', {
    accountNumber,
    bankCode,
    etc: '계좌 개설 요청',
  });

  return response.data;
};
