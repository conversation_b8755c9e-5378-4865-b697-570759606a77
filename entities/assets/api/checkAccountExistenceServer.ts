'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

export const checkAccountExistenceServer = async (accessToken: string) => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/asset/account/user/exists`, {
    next: { tags: ['user', 'accountExistence'], revalidate: 0 },
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    logServerError('get user server error', {
      error: {
        accessToken,
      },
    });
  }

  const data = await response.json();

  return data.data;
};
