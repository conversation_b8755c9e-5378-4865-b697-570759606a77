import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { CreateVirtualAccountPayload } from '../interface';

export const createVirtualAccount = async (payload: CreateVirtualAccountPayload) => {
  const { accountNumber, bankCode } = payload;

  const response = await userPrivateInstance.post('/asset/account/user', {
    accountNumber,
    bankCode,
    etc: '계좌 개설 요청',
  });

  return response.data;
};
