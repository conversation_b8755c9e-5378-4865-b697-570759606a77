import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { VerifyWithdrawAccountPayload } from '../interface';

export const updateUserAccount = async (
  accountId: string,
  payload: VerifyWithdrawAccountPayload,
) => {
  const response = await userPrivateInstance.put(`/asset/account/${accountId}/user`, {
    ...payload,
    etc: '계좌변경',
  });

  return response.data;
};
