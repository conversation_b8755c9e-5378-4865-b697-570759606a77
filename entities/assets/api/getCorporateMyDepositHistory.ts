import { ScrollApiResponse } from '@/shared/interface';
import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { GetMyDepositHistoryPayload, MyDepositHistoryResponse } from '../interface';
import { MyDepositHistory } from '../types';

export const getCorporateMyDepositHistory = async (
  payload: GetMyDepositHistoryPayload,
): Promise<MyDepositHistoryResponse> => {
  const response = await userPrivateInstance.get(`/asset/deposit/corporate/history`, {
    params: {
      page: payload.page,
      size: payload.perPage,
      searchBeginAt: payload.searchBeginAt,
      searchEndAt: payload.searchEndAt,
    },
  });
  return response.data;
};

export const getCorporateMyDepositHistoryList = async (
  payload: GetMyDepositHistoryPayload,
): Promise<ScrollApiResponse<MyDepositHistory>> => {
  const response = await getCorporateMyDepositHistory(payload);

  return {
    data: response.list,
    nextPage: payload.page ? payload.page + 1 : 1,
    isLast: response.list.length < (payload.perPage ?? 10),
    totalCount: response.totalCount,
  };
};
