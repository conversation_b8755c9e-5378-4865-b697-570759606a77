import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { CreateVirtualAccountPayload } from '../interface';

export const closeAccount = async (payload: CreateVirtualAccountPayload) => {
  const response = await userPrivateInstance.delete('/account', {
    data: {
      accountNumber: payload.accountNumber,
      bankCode: payload.bankCode,
      etc: '계좌 해지 요청',
    },
  });

  return response.data;
};
