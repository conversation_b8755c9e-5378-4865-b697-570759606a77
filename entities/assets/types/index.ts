export interface Deposit {
  createdAt: string;
  depositId: number;
  depositHistoryId: number;
  currentBalance: number;
  eventCode: DepositEventCode;
  previousBalance: number;
  updatedAt?: string;
  rateofChange?: number;
  userId: string;
}

export enum DepositEventCode {
  SUBSCRIPTION = 'SUBSCRIPTION',
  REPAYMENT = 'REPAYMENT',
  DIVIDEND = 'DIVIDEND',
  DEPOSIT = 'DEPOSIT',
  WITHDRAW = 'WITHDRAW',
  CHARGE = 'CHARGE',
}

export const depositEventCodeValues = {
  [DepositEventCode.SUBSCRIPTION]: '청약금 출금',
  [DepositEventCode.REPAYMENT]: '상환',
  [DepositEventCode.DIVIDEND]: '배당금 입금',
  [DepositEventCode.DEPOSIT]: '입금',
  [DepositEventCode.WITHDRAW]: '출금',
  [DepositEventCode.CHARGE]: '충전',
};

export enum CreateDepositEventCode {
  INIT = 'INIT', // 생성
  DEPOSIT = 'DEPOSIT', // 입금
  DIVIDEND = 'DIVIDEND', // 배당금 입금
  REDEMPTION = 'REDEMPTION', // 상환
  CHARGE = 'CHARGE', // 충전
}

export enum UpdateDepositEventCode {
  DEPOSIT = 'DEPOSIT', // 입금
  WITHDRAW = 'WITHDRAW', // 출금
  SUBSCRIPTION = 'SUBSCRIPTION', // 청약
  DIVIDEND = 'DIVIDEND', // 배당금 입금
  REDEMPTION = 'REDEMPTION', // 상환
  CHARGE = 'CHARGE', // 충전
}

export enum DepositSteps {
  TERMS = 'TERMS',
  FORM = 'FORM',
  CONFIRM = 'CONFIRM',
  COMPLETE = 'COMPLETE',
}

export enum DepositChangeSteps {
  FORM = 'FORM',
  COMPLETE = 'COMPLETE',
}

export interface Account {
  depositId: number;
  accountId: number;
  userId: string;
  depositAccountNumberCode: string;
  depositAccountInstCode: string;
  masterAccountNumberCode: string;
  masterAccountInstCode: string;
  currentBalance: number; // 예치금
  currentNetBalance: number; // 출금 가능 금액
  depositAmountWithin24h: number; // 24시간 내 출금 불가능 금액
  subscriptionMarginSummary: {
    totalLockedMargin: number;
    detail: {
      securitiesId: string;
      applyQuantity: number;
      offeringPrice: number;
      transactionFee: number;
      securities: {
        securitiesId: string;
        securitiesName: string;
      };
    }[];
  };
}

export interface MyDepositHistory {
  accountId: number;
  createdAt: string;
  currentBalance: number;
  deleted: boolean;
  depositHistoryId: number;
  depositId: number;
  eventCode: DepositEventCode;
  eventDesc: string;
  previousBalance: number;
  rateOfChange: number;
  remark: string;
  updatedAt: string;
  userId: string;
}

export enum AssetEventCode {
  REPAYMENT = 'REPAYMENT',
  ALLOCATION = 'ALLOCATION',
}
