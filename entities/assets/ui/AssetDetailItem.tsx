import React from 'react';

interface AssetDetailItemProps {
  title: string;
  value: string;
}

export const AssetDetailItem = ({ title, value }: AssetDetailItemProps) => {
  return (
    <div className="flex flex-1 flex-row justify-between text-sm sm:flex-col">
      <p className="font-semibold text-gray-500 sm:text-base">{title}</p>
      <p className="sm:text-24 font-semibold">{value}</p>
    </div>
  );
};
