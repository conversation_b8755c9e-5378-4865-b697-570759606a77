import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';

import { MyDepositHistory } from '../types';

interface MobileDepositHistoryCardProps {
  depositHistory: MyDepositHistory;
  isLast: boolean;
}

export const MobileDepositHistoryCard = ({
  depositHistory,
  isLast,
}: MobileDepositHistoryCardProps) => {
  const { YYYYMMDD, CASHCOMMA } = utilFormats();

  return (
    <div
      key={depositHistory.depositHistoryId}
      className={`flex flex-col gap-1 py-4 leading-[150%] ${
        !isLast ? 'border-b border-gray-300' : ''
      }`}
    >
      <p className="text-xs text-gray-500">
        {YYYYMMDD(depositHistory.createdAt, 'YYYY-MM-DD hh:mm:ss')}
      </p>
      <p className="font-semibold">{depositHistory.eventDesc}</p>
      <span className="text-right text-sm font-semibold">
        {CASHCOMMA(depositHistory.currentBalance - depositHistory.previousBalance)}원
      </span>
    </div>
  );
};
