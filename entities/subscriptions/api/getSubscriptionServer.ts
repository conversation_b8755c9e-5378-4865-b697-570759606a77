import env from '@/shared/lib/env.schema';

import { Subscription } from '../types';

export const getSubscriptionServer = async (id: string): Promise<Subscription> => {
  const subscription = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/subscription/info/${id}`, {
    cache: 'force-cache',
    next: { tags: ['subscriptions', id], revalidate: 60 * 1 },
  });

  const data = await subscription.json();

  return data.data;
};
