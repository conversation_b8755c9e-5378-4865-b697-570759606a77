import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { SubscriptionApplyLimit } from '../types';

export const getSubscriptionApplyLimit = async (
  subscriptionInfoId: string,
): Promise<SubscriptionApplyLimit> => {
  const response = await userPrivateInstance.get(
    `/subscription/apply/info/${subscriptionInfoId}/user/apply-limit-summary`,
  );
  return response.data;
};
