import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { MySubscriptionsRequest, MySubscriptionsResponse } from '../interface';

export const getMySubscriptions = async (
  params: MySubscriptionsRequest,
): Promise<MySubscriptionsResponse> => {
  const { subscriptionApplyStatusList, ...restParams } = params;

  const queryParams = new URLSearchParams();

  // 배열 파라미터 처리
  if (subscriptionApplyStatusList) {
    subscriptionApplyStatusList.forEach((status) => {
      queryParams.append('subscriptionApplyStatusList', status);
    });
  }

  // 나머지 파라미터 처리
  Object.entries(restParams).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, String(value));
    }
  });

  const response = await userPrivateInstance.get(
    `/subscription/apply/my?${queryParams.toString()}`,
  );
  return response.data;
};
