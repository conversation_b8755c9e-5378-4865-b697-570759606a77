'use server';

import env from '@/shared/lib/env.schema';

import { SubscriptionBizStatus } from '../types';

export const getSubscriptionsServer = async ({
  subscriptionInfoStatusList,
  size,
}: {
  size?: number;
  subscriptionInfoStatusList?: SubscriptionBizStatus;
}) => {
  const subscriptions = await fetch(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/subscription/info?page=0&size=${size}&subscriptionInfoStatusList=${subscriptionInfoStatusList}`,
    {
      cache: 'force-cache',
      next: {
        tags: ['subscriptions', `${{ page: 0, size }}`, `${subscriptionInfoStatusList}`],
        revalidate: 60 * 1,
      },
    },
  );
  const data = await subscriptions.json();

  return data.data;
};
