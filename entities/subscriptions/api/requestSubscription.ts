import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { RequestSubscriptionPayload } from '../interface';

export const requestSubscription = async (payload: RequestSubscriptionPayload) => {
  const formData = new FormData();

  Object.entries(payload).forEach(([key, value]) => {
    if (key === 'attachFiles' && Array.isArray(value)) {
      value.forEach((file) => {
        formData.append('attachFiles', file);
      });
    } else {
      formData.append(key, value as string);
    }
  });

  const response = await cmsPublicInstance.post('/contents/investment-request', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
