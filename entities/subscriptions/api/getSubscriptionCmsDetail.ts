import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { SubscriptionCmsDetail } from '../types';

export const getSubscriptionCmsDetail = async (
  securitiesId: string,
): Promise<ListResponse<SubscriptionCmsDetail>> => {
  const response = await cmsPublicInstance('/contents/securities-detail', {
    params: {
      securities: securitiesId,
    },
  });

  return response.data;
};
