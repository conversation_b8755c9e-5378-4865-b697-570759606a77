import { SubscriptionApplyStatusList } from '../types';

export const getApplyStatusColor = (status: SubscriptionApplyStatusList) => {
  switch (status) {
    case SubscriptionApplyStatusList.APPLY:
    case SubscriptionApplyStatusList.ALLOT_TARGET:
    case SubscriptionApplyStatusList.ALLOT:
    case SubscriptionApplyStatusList.WAIT:
      return 'bg-green-50 text-green-500';
    case SubscriptionApplyStatusList.WITHDRAW:
    case SubscriptionApplyStatusList.NOT_ALLOT:
      return 'bg-red-50 text-red-500';
    default:
      return 'bg-gray-100 text-gray-500';
  }
};
