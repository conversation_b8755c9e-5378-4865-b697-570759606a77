import React from 'react';

interface SubscriptionDetailConditionItemProps {
  label: string;
  value: string;
}

export const SubscriptionDetailConditionItem = ({
  label,
  value,
}: SubscriptionDetailConditionItemProps) => {
  return (
    <div className="flex items-center justify-between sm:flex-col sm:justify-start sm:space-y-[6px]">
      <p className="text-sm font-normal text-gray-700 sm:font-semibold sm:text-gray-500">{label}</p>
      <p className="text-sm sm:text-base sm:font-semibold">{value}</p>
    </div>
  );
};
