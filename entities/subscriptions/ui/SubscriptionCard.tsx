import { Separator } from '@radix-ui/react-separator';
import React, { useMemo } from 'react';

import { fetchSubscriptionCmsDetail } from '@/features/subscription/api/fetchSubscriptionCmsDetail';
import { fetchSubscriptionStatistics } from '@/features/subscription/api/fetchSubscriptionStatistics';
import { useSubscriptionMainImage } from '@/features/subscription/model/useSubscriptionImage';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { NewBadge } from '@/shared/ui/NewBadge';

import { calculateProgress } from '../lib/calculateProgress';
import { SecuritiesTypeLabel, Subscription, SubscriptionBizStatus } from '../types';
import { SubscriptionProgress } from './SubscriptionProgress';

interface SubscriptionCardProps {
  subscription: Subscription;
}

export const SubscriptionCard = ({ subscription }: SubscriptionCardProps) => {
  const { routerPush } = useWebViewRouter();

  const { subscriptionInfoExposure, bizStatus } = subscription;

  const { data: statistics } = fetchSubscriptionStatistics(
    subscription.subscriptionInfoId.toString(),
  );

  const { applyStatistics, infoStatistics } = statistics ?? {};

  const { mainImage } = useSubscriptionMainImage(subscription.securities.securitiesId);

  const isProgress = subscriptionInfoExposure.progressExposureYn;

  const progress = calculateProgress(
    infoStatistics?.offeringTotalAmount ?? 0,
    applyStatistics?.applyTotalMargin ?? 0,
  );

  const isFailed = subscription.bizStatus === SubscriptionBizStatus.SUB_DONE && progress < 80;

  return (
    <div
      className="relative flex cursor-pointer flex-col rounded-[10px] border border-gray-300 sm:gap-8 sm:border-none"
      onClick={() => routerPush(`/subscriptions/${subscription.subscriptionInfoId}`)}
    >
      <div className="relative aspect-[368/237]">
        <FallbackImage
          src={mainImage?.image.url || '/images/subscription_image.png'}
          alt="subscription_image"
          fill
          className="rounded-[10px] object-cover"
        />
      </div>
      <NewBadge size="lg" date={subscription.createdAt} className="absolute left-4 top-4" />
      <div className="space-y-2 px-5 pt-4 sm:px-0 sm:pt-0">
        <div className="flex gap-3 text-xs">
          <p className="font-semibold">
            {SecuritiesTypeLabel[subscription.securities.securitiesType]}
          </p>
          <Separator orientation="vertical" className="w-[1px] bg-gray-300" />
          <p>{subscription.issuer.name}</p>
        </div>
        <p className="text-lg font-bold">{subscription.securities.securitiesName}</p>
        {/* Todo: 태그 임시 제거
        <div className="mt-1 flex gap-[6px]">
          <span className="rounded bg-gray-100 px-[6px] py-[3px] text-xs font-semibold text-gray-500">
            태그
          </span>
          <span className="bg-gray-100 px-[6px] py-[3px] text-xs font-semibold text-gray-500">
            태그
          </span>
        </div> */}
      </div>

      <SubscriptionProgress
        bizStatus={bizStatus}
        isProgress={isProgress}
        isFailed={isFailed}
        subscription={subscription}
        statistics={{
          offeringTotalAmount: infoStatistics?.offeringTotalAmount ?? 0,
          applyTotalMargin: applyStatistics?.applyTotalMargin ?? 0,
        }}
        progress={progress}
        className="px-5 pb-4 pt-8 sm:px-0 sm:pb-0 sm:pt-0"
      />
    </div>
  );
};
