import { isOpen } from '@/features/subscription/lib';

import { utilFormats } from '@/shared/lib/utilformats';
import { cn } from '@/shared/lib/utils';
import { Progress } from '@/shared/ui/shadcn/progress';

import { Subscription, SubscriptionBizStatus } from '../types';
import { SubscriptionCardPrice } from './SubscriptionCardPrice';

// entities/subscriptions/ui/SubscriptionStatus.tsx
type SubscriptionStatusProps = {
  bizStatus: SubscriptionBizStatus;
  isProgress: boolean;
  isFailed?: boolean;
  subscription: Subscription;
  statistics: {
    offeringTotalAmount: number;
    applyTotalMargin: number;
  };
  progress: number;
  className?: string;
};

export const SubscriptionProgress = ({
  bizStatus,
  isProgress,
  isFailed,
  subscription,
  statistics,
  progress,
  className,
}: SubscriptionStatusProps) => {
  const { CASHCOMMA } = utilFormats();

  // 상태별 렌더링 컴포넌트
  const statusComponents = {
    [SubscriptionBizStatus.SUB_WAIT]: (
      <p className={cn('text-sm font-semibold text-primary-500 sm:text-lg', className)}>
        {isOpen(subscription).openText}
      </p>
    ),
    [SubscriptionBizStatus.SUB_WIP]: isProgress ? (
      <div className={cn('space-y-3', className)}>
        <p className="text-sm sm:text-lg">
          총 발행 금액 <strong>{CASHCOMMA(statistics.offeringTotalAmount)}원</strong>
        </p>
        <div className="space-y-[6px]">
          <Progress value={progress} className="bg-blue-gray-50 !text-primary-500" />
          <div className="flex justify-between text-xs">
            <p>
              누적 투자 금액{' '}
              <strong className="font-semibold">{CASHCOMMA(statistics.applyTotalMargin)}원</strong>
            </p>
            <p>
              달성률 <strong className="text-primary-500">{progress}%</strong>
            </p>
          </div>
        </div>
      </div>
    ) : (
      <SubscriptionCardPrice
        totalAmount={statistics.offeringTotalAmount}
        applyTotalMargin={statistics.applyTotalMargin}
        className={`${className} text-sm sm:text-base`}
      />
    ),
    [SubscriptionBizStatus.SUB_DONE]: isFailed ? (
      <p className={cn('text-sm font-semibold text-gray-500 sm:text-lg', className)}>모집 마감</p>
    ) : (
      <p className={cn('text-sm sm:text-lg', className)}>
        총 <strong className="font-semibold">{CASHCOMMA(statistics.applyTotalMargin)}원</strong>{' '}
        모집 성공
      </p>
    ),
  };

  return statusComponents[bizStatus as keyof typeof statusComponents] || null;
};
