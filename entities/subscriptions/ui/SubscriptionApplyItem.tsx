import React from 'react';

interface SubscriptionApplyItemProps {
  label: string;
  value: string;
  className?: string;
  valueClassName?: string;
  labelClassName?: string;
  direction?: 'row' | 'column';
}

export const SubscriptionApplyInfoItem = ({
  label,
  value,
  valueClassName = 'font-semibold',
  labelClassName = 'text-gray-600',
  direction = 'row',
}: SubscriptionApplyItemProps) => {
  return (
    <div
      className={`flex justify-between leading-[150%] sm:flex-row sm:items-center ${
        direction === 'row' ? 'flex-row' : 'flex-col'
      }`}
    >
      <span className={labelClassName}>{label}</span>
      <span className={valueClassName}>{value}</span>
    </div>
  );
};
