import React from 'react';

import { getApplyStatusColor } from '../lib/getApplyStatusColor';
import { SubscriptionApplyStatusLabel, SubscriptionApplyStatusList } from '../types';

interface ApplyStatusTagProps {
  status: SubscriptionApplyStatusList;
}

export const ApplyStatusTag = ({ status }: ApplyStatusTagProps) => {
  return (
    <span
      className={`inline-block rounded px-2 py-[3px] text-xs font-semibold leading-[150%] ${getApplyStatusColor(status)}`}
    >
      {SubscriptionApplyStatusLabel[status]}
    </span>
  );
};
