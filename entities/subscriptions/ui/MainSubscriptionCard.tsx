import React from 'react';

import { fetchSubscriptionStatistics } from '@/features/subscription/api/fetchSubscriptionStatistics';
import { useSubscriptionMainImage } from '@/features/subscription/model/useSubscriptionImage';

import {
  SecuritiesTypeLabel,
  Subscription,
  SubscriptionBizStatus,
} from '@/entities/subscriptions/types';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { NewBadge } from '@/shared/ui/NewBadge';
import { Separator } from '@/shared/ui/shadcn/separator';

import { calculateProgress } from '../lib/calculateProgress';
import { SubscriptionProgress } from './SubscriptionProgress';

interface MainSbuscriptionCardProps {
  subscription: Subscription;
}

export const MainSubscriptionCard = ({ subscription }: MainSbuscriptionCardProps) => {
  const { routerPush } = useWebViewRouter();

  const { subscriptionInfoExposure, bizStatus } = subscription;

  const { data: statistics } = fetchSubscriptionStatistics(
    subscription.subscriptionInfoId.toString(),
  );

  const { applyStatistics, infoStatistics } = statistics ?? {};

  const { mainImage } = useSubscriptionMainImage(subscription.securities.securitiesId);

  const isProgress = subscriptionInfoExposure.progressExposureYn;

  const progress = calculateProgress(
    infoStatistics?.offeringTotalAmount ?? 0,
    applyStatistics?.applyTotalMargin ?? 0,
  );

  const isFailed = subscription.bizStatus === SubscriptionBizStatus.SUB_DONE && progress < 80;

  return (
    <div
      key={subscription.subscriptionInfoId}
      className="relative flex cursor-pointer flex-col rounded-[10px] border border-gray-300 sm:rounded-[20px] tb:flex-row"
      onClick={() => routerPush(`/subscriptions/${subscription.subscriptionInfoId}`)}
    >
      <div className="relative h-[215px] w-full transition-all duration-300 sm:max-w-[468px] sm:hover:scale-[108%] tb:h-[316px]">
        <FallbackImage
          src={mainImage?.image.url || '/images/subscription_image.png'}
          alt="subscription"
          fill
          sizes="(max-width: 468px) 100vw, 468px"
          className="rounded-t-[10px] object-cover tb:rounded-l-[20px] tb:rounded-tr-none"
        />
      </div>
      <NewBadge size="lg" date={subscription.createdAt} className="absolute left-4 top-4" />
      <div className="sm:min-h-auto flex w-full flex-1 flex-col justify-between space-y-8 px-5 py-4 sm:space-y-10 sm:p-6 tb:min-h-[316px] tb:space-y-0 tb:px-10 tb:py-8">
        <div className="space-y-2">
          <div>
            <div className="flex items-center gap-3 text-xs tb:text-sm">
              <p className="font-semibold">
                {SecuritiesTypeLabel[subscription.securities.securitiesType]}
              </p>
              <Separator orientation="vertical" className="h-3 bg-gray-300 sm:h-4" />
              <p>{subscription.issuer.name}</p>
            </div>
          </div>
          <p className="sm:text-20 tb:text-24 text-base font-semibold">
            {subscription.securities.securitiesName}
          </p>
          {/* Todo: 태그 임시 제거 */}
          {/* <div className="mt-1 flex gap-1 sm:gap-[6px]">
            <span className="rounded bg-gray-100 px-[6px] py-[3px] text-xs font-semibold leading-[150%] text-gray-500">
              태그
            </span>
            <span className="rounded bg-gray-100 px-[6px] py-[3px] text-xs font-semibold leading-[150%] text-gray-500">
              태그
            </span>
          </div> */}
        </div>

        {/* 진행중 O 프로그레스O */}
        <SubscriptionProgress
          bizStatus={bizStatus}
          isProgress={isProgress}
          isFailed={isFailed}
          subscription={subscription}
          statistics={{
            offeringTotalAmount: infoStatistics?.offeringTotalAmount ?? 0,
            applyTotalMargin: applyStatistics?.applyTotalMargin ?? 0,
          }}
          progress={progress}
        />
      </div>
    </div>
  );
};
