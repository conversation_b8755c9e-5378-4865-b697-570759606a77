import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { cn } from '@/shared/lib/utils';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionCardPriceProps {
  totalAmount: number;
  applyTotalMargin: number;
  className?: string;
}

export const SubscriptionCardPrice = ({
  totalAmount,
  applyTotalMargin,
  className,
}: SubscriptionCardPriceProps) => {
  const { CASHCOMMA } = utilFormats();

  return (
    <div className={cn('flex', className)}>
      <div className="flex flex-1 flex-col gap-1">
        <p className="font-semibold text-gray-500">목표금액</p>
        <p className="font-semibold">{CASHCOMMA(totalAmount)}원</p>
      </div>
      <div className="flex flex-1 gap-6">
        <Separator orientation="vertical" className="my-2 bg-gray-200" />
        <div className="flex flex-col gap-1">
          <p className="font-semibold text-gray-500">총 모집 금액</p>
          <p className="font-semibold">{CASHCOMMA(applyTotalMargin)}원</p>
        </div>
      </div>
    </div>
  );
};
