import React from 'react';

import { Separator } from '@/shared/ui/shadcn/separator';

interface DetailInfoItemProps {
  title: string;
  content: string;
  id: string;
}

export const DetailInfoItem = ({ title, content, id }: DetailInfoItemProps) => {
  return (
    <div className="space-y-4">
      <h5 className="flex items-center gap-2 text-lg font-bold">
        <Separator orientation="vertical" className="h-[22px] w-1 bg-primary-500" /> {title}
      </h5>
      <div id={id} dangerouslySetInnerHTML={{ __html: content }} className="ck-content" />
    </div>
  );
};
