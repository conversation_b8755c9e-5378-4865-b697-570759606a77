export const ApplyProgressIndicator = ({ current, total }: { current: number; total: number }) => {
  return (
    <div className="flex items-center gap-1 rounded-[20px] border border-gray-200 px-3 py-1 text-xs font-semibold">
      <span className="rounded-full text-primary-500">{current}</span>
      <span className="rounded-full text-gray-300">/</span>
      <span className="rounded-full text-gray-400">{total}</span>
    </div>
  );
};
