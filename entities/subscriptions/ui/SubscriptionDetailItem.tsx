import React from 'react';

import { cn } from '@/shared/lib/utils';

interface SubscriptionDetailItemProps {
  label: string;
  value: string;
  between?: boolean;
}

export const SubscriptionDetailItem = ({
  label,
  value,
  between = false,
}: SubscriptionDetailItemProps) => {
  return (
    <div className={cn('flex flex-1 gap-5 sm:justify-start', between && 'justify-between')}>
      <p className="min-w-20 text-sm text-gray-700">{label}</p>
      <p className="text-sm">{value}</p>
    </div>
  );
};
