export const MySubscriptionCardSkeleton = () => {
  return (
    <div className="space-y-4 rounded-lg border border-gray-300 p-5 sm:p-8">
      <div className="flex justify-between">
        <div className="h-5 w-20 animate-pulse rounded bg-gray-200" />
        <div className="h-5 w-32 animate-pulse rounded bg-gray-200" />
      </div>
      <div className="flex justify-between">
        <div className="h-5 w-20 animate-pulse rounded bg-gray-200" />
        <div className="h-5 w-32 animate-pulse rounded bg-gray-200" />
      </div>
      <div className="flex justify-between">
        <div className="h-5 w-20 animate-pulse rounded bg-gray-200" />
        <div className="h-5 w-32 animate-pulse rounded bg-gray-200" />
      </div>
      <div className="flex justify-between">
        <div className="h-5 w-20 animate-pulse rounded bg-gray-200" />
        <div className="h-5 w-32 animate-pulse rounded bg-gray-200" />
      </div>
    </div>
  );
};
