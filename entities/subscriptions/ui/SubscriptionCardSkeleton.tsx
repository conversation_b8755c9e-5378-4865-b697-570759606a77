import React from 'react';

export const SubscriptionCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-8">
      {/* 이미지 스켈레톤 */}
      <div className="relative aspect-[368/237]">
        <div className="h-full w-full animate-pulse rounded-lg bg-gray-200" />
      </div>

      {/* 뱃지 스켈레톤 */}
      <div className="absolute left-4 top-4">
        <div className="h-6 w-16 animate-pulse rounded-full bg-gray-200" />
      </div>

      {/* 컨텐츠 영역 스켈레톤 */}
      <div className="space-y-2">
        {/* 상단 텍스트 영역 */}
        <div className="flex gap-3">
          <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
          <div className="h-4 w-[1px] bg-gray-200" />
          <div className="h-4 w-24 animate-pulse rounded bg-gray-200" />
        </div>

        {/* 제목 영역 */}
        <div className="h-6 w-3/4 animate-pulse rounded bg-gray-200" />

        {/* 태그 영역 */}
        <div className="mt-1 flex gap-[6px]">
          <div className="h-6 w-16 animate-pulse rounded bg-gray-200" />
          <div className="h-6 w-16 animate-pulse rounded bg-gray-200" />
        </div>
      </div>

      {/* 하단 정보 영역 */}
      <div className="space-y-3">
        <div className="h-5 w-2/3 animate-pulse rounded bg-gray-200" />
        <div className="space-y-[6px]">
          <div className="h-2 w-full animate-pulse rounded-full bg-gray-200" />
          <div className="flex justify-between">
            <div className="h-4 w-32 animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
          </div>
        </div>
      </div>
    </div>
  );
};
