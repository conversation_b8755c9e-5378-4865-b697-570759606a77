import {
  DocsType,
  MySubscription,
  Subscription,
  SubscriptionApplyStatusList,
  SubscriptionBizStatus,
  SubscriptionSortType,
} from '../types';

export interface ApplySubscriptionPayload {
  applyQuantity: number;
  investmentAgreeAt: string;
  digitalSign: string;
}

export interface SubscriptionsResponse {
  list: Subscription[];
  totalPage: number;
  totalCount: number;
}

export interface SubscriptionsRequest {
  page: number;
  size: number;
  subscriptionInfoStatusList?: SubscriptionBizStatus;
  sortType?: SubscriptionSortType;
}

export interface MySubscriptionsRequest {
  page: number;
  size: number;
  subscriptionApplyStatusList?: SubscriptionApplyStatusList[];
}

// APPLY,WITHDRAW,WAIT,ALLOT_TARGET,ALLOT,NOT_ALLOT,REFUND_WAIT,REFUND_DONE

export interface MySubscriptionsResponse {
  list: MySubscription[];
  totalPage: number;
  totalCount: number;
}

export interface RequestSubscriptionPayload {
  status: string;
  companyName: string;
  applicantName: string;
  email: string;
  investmentDescription: string;
  attachFiles: File[];
}

export interface FileDownloadPayload {
  mapId: string;
  service: string;
  code: DocsType;
}
