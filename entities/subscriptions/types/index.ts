import { InquiryAnswerStatus, InquiryCategory } from '@/entities/inquiries/types';
import { PropensityLevelEnum } from '@/entities/investor/types';

export enum SubscriptionOnboardingSteps {
  OCR = 'OCR',
  ACCOUNT = 'ACCOUNT',
  // PROPENSITY = 'PROPENSITY',
  SUITABILITY = 'SUITABILITY',
}

export const SubscriptionOnboardingStepsLabel = {
  [SubscriptionOnboardingSteps.OCR]: '본인 실명인증',
  [SubscriptionOnboardingSteps.ACCOUNT]: '예치금 가상계좌 계설',
  // [SubscriptionOnboardingSteps.PROPENSITY]: '투자성향 진단',
  [SubscriptionOnboardingSteps.SUITABILITY]: '투자 적합성 테스트',
};

export enum SubscriptionApplySteps {
  QUANTITY = 'QUANTITY',
  DEPOSIT = 'DEPOSIT',
  CONFIRM = 'CONFIRM',
  COMPLETE = 'COMPLETE',
}

export interface Subscription {
  issuer: SubscriptionIssuer;
  allotAt: string; //배정일
  beginAt: string; // 청약 시작일
  bizStatus: SubscriptionBizStatus;
  considerationAt: string; //철회일
  colloffAt: string; // 숙려일
  createdAt: string; // 생성일
  deleted: boolean; // 삭제여부
  endAt: string; // 청약 종료일
  equalAllotRatio: number; // 균등 배정 비율
  firstcomeAllotRatio: number; // 선착순 배정 비율
  marginPayAt: string; // 증거급 납일 일시
  offeringPrice: number;
  offeringTotalAmount: number;
  personLimitQuantity: number; //개인 제한
  priorityAllotRatio: number; // 발행인 우선
  proportionalAllotRatio: number; //비례
  securities: {
    bizStatus: string;
    bizStatusDesc: string;
    createdAt: string;
    createdBy: string;
    allotmentDate: string;
    deleted: boolean;
    issueDate: string;
    issuerId: number;
    maturityDate: string;
    pricePerUnit: number;
    securitiesId: string;
    securitiesName: string;
    securitiesType: SecuritiesType;
    totalAmount: number;
    totalQuantity: number;
    updatedAt: string;
    updatedBy: string;
    requiredDocsDetail: RequiredDocsDetail[];
    securitiesInfoJson: {
      incomeTaxType: string;
      investmentRiskLevel: string;
      registeredYn: boolean;
      requiredDocs: {
        title: string;
        type: string;
        fileName: string;
        _id: string;
      }[];
      settlementInfo: string;
      statementAcceptedAt: string;
      statementAcceptedYn: boolean;
      underlyingAssetType: string;
    };
  };
  subscriptionInfoId: number;
  subscriptionInfoStatusStr: string;
  totalQuantity: number;
  transactionFee: number;
  updatedAt: string;
  subscriptionInfoExposure: {
    subscriptionInfoExposureId: number;
    exposureYn: boolean;
    progressExposureYn: boolean;
    exposureBeginAt: string;
    exposureEndAt: string;
  };
}

export enum SubscriptionBizStatus {
  SUB_WAIT = 'SUB_WAIT', // 청약대기
  SUB_WIP = 'SUB_WIP', // 청약중
  SUB_DONE = 'SUB_DONE', // 청약 완료
  ASG_R_READY = 'ASG_R_READY', //배정 준비중
  ASG_R_DONE = 'ASG_R_DONE', // 배정 준비완료
  WDR_WIP = 'WDR_WIP', // 출금중
  WDR_DONE = 'WDR_DONE', // 출금완료
  ASG_WIP = 'ASG_WIP', // 배정중
  ASG_DONE = 'ASG_DONE', // 배정완료
}

export enum SecuritiesType {
  INVESTMENT_CONTRACT_SECURITIES = 'INVESTMENT_CONTRACT_SECURITIES',
  INCOME_SECURITIES = 'INCOME_SECURITIES',
  PROFIT_PARTICIPATING_BONDS = 'PROFIT_PARTICIPATING_BONDS',
}

export const SecuritiesTypeLabel = {
  [SecuritiesType.INVESTMENT_CONTRACT_SECURITIES]: '투자계약증권',
  [SecuritiesType.INCOME_SECURITIES]: '수익증권',
  [SecuritiesType.PROFIT_PARTICIPATING_BONDS]: '이익참가부사채',
};

export const AllocationTypeLabel = {
  fifo_percent: '선착순',
  proportional_percent: '비례',
  equal_percent: '균등',
  issuer_percent: '발행인 우선',
};

export interface InvestmentContractSecuritiesInfo {
  InvestmentRiskLevel: number; // 투자위험 등급
  isStatementAccepted: boolean; //증권신고서 수리여부
  statementAcceptedAt: string; //증권신고서 수리일자
  incomeTaxType: string;
  securitiesStatementFile: string; //증권신고서 파일
  investmentAgreementFile: string; //투자계약서 파일
  investmentProspectusFile: string; //투자설명서
  investmentValueInfoFile: string; //투자가치정보
  settlementInfo?: string; //정산정보
  category?: string; //산업분류 등
  isRegistered?: boolean; //기명식 여부
}

export interface ProfitParticipatingBondInfo {
  bondType: string; // 채권종류
  settlementInfo: string; //정산정보
  incomeTaxType: string; //소득세 유형
  cumulativeReturn: string; //누적손익률
  couponRate: string; //표면금리
  paymentFreq: string; //지급주기
  interestPaymentMethod: string; //이자지급방식
  issuedDisclosureFiles: string; //발행 게재자료
  investmentValueInfoFIle: string; //투자가치정보
  isRegistered?: boolean; //기명식 여부
  isCallableOption?: boolean; //중도 상환권 여부
  isPrincipalLossRisk?: boolean; //원금손실 가능 여부
  issuanceType?: 'DEPOSITORY' | 'NON_DEPOSITORY'; //발행구분
  category: string; //산업분류
}

export interface MySubscription {
  applyQuantity: number;
  createdAt: string;
  deleted: boolean;
  depositAccountInstCode: string;
  depositAccountNumberCode: string;
  digitalSign: string;
  investmentAgreeAt: string;
  investorPropensityLevel: PropensityLevelEnum;
  margin: number;
  subscriptionApplyId: number;
  subscriptionApplyStatus: SubscriptionApplyStatusList;
  subscriptionInfo: {
    allotAt: string;
    beginAt: string;
    bizStatus: SubscriptionBizStatus;
    considerationAt: string;
    colloffAt: string;
    createdAt: string;
    deleted: boolean;
    endAt: string;
    equalAllotRatio: number;
    firstcomeAllotRatio: number;
    marginPayAt: string;
    offeringPrice: number;
    offeringTotalAmount: number;
    personLimitQuantity: number;
    priorityAllotRatio: number;
    proportionalAllotRatio: number;
    securities: {
      allotmentDate: string;
      bizStatus: string;
      bizStatusDesc: string;
      createdAt: string;
      createdBy: string;
      deleted: boolean;
      issueDate: string;
      issuerId: number;
      maturityDate: string; // 만료일
      pricePerUnit: number;
      requiredDocsDetail: RequiredDocsDetail[];
      securitiesId: string;
      securitiesInfoJson: {
        incomeTaxType: string;
        investmentRiskLevel: string;
        registeredYn: boolean;
        requiredDocs: RequiredDocsDetail[];
        settlementInfo: string;
        statementAcceptedAt: string;
        statementAcceptedYn: boolean;
        underlyingAssetType: string;
      };
      securitiesName: string;
      securitiesType: string;
      totalAmount: number;
      totalQuantity: number;
      updatedAt: string;
      updatedBy: string;
    };
    securitiesId: string;
    subscriptionInfoExposure: {
      exposureBeginAt: string;
      exposureEndAt: string;
      exposureYn: boolean;
      progressExposureYn: boolean;
      subscriptionInfoExposureId: number;
    };
    subscriptionInfoId: number;
    subscriptionInfoStatusStr: string;
    totalQuantity: number;
    transactionFee: number;
    updatedAt: string;
  };
  updatedAt: string;
  userId: string;
}

export interface RequiredDocsDetail {
  fileName: string;
  mimeType: string;
  size: number;
  _id: string;
  metadata?: {
    by: string;
    code: DocsType;
    mapId: string;
    service: string;
    title: string;
  };
}

export interface SubscriptionCmsDetail {
  summaryVisible: string;
  summary: string; //투자상품 요약
  investmentPointVisible: string; // 투자 포인트
  investmentPoint: string;
  assetDescribeVisible: string; // 기초자산 소개
  assetDescribe: string;
  profitStructureVisible: string; //수익구조
  profitStructure: string;
  operatorCompetitivenessVisible: string; //운용사업자 경쟁력
  operatorCompetitiveness: string;
  investmentConsideration: string; //투자 유의사항
  investmentConsiderationVisible: string;
  imageList?: {
    image: {
      filieId: string;
      name: string;
      type: string;
      url: string;
    };
    isMain: boolean;
    order: string;
  }[];
}

export interface SubscriptionCmsContent {
  attachFiles: { fileId: string; name: string; type: string; url: string }[];
  content: string;
  contentType: string;
  createdAt: string;
  createdBy: string;
  deleted: boolean;
  id: string;
  publishedAt: string;
  status: string;
  title: string;
  updatedAt: string;
  updatedBy: string;
  version: number;
  answer?: string;
  answerStatus?: InquiryAnswerStatus;
  category?: InquiryCategory;
}

export enum SubscriptionApplyStatusList {
  APPLY = 'APPLY', //청약 신청 완료
  WITHDRAW = 'WITHDRAW', //청약 철회
  WAIT = 'WAIT', //배정중
  ALLOT_TARGET = 'ALLOT_TARGET', //배정대상
  ALLOT = 'ALLOT', //배정완료
  NOT_ALLOT = 'NOT_ALLOT', //미배정
  REFUND_WAIT = 'REFUND_WAIT', //환불대기
  REFUND_DONE = 'REFUND_DONE', //환불완료
}

export const SubscriptionApplyStatusLabel = {
  [SubscriptionApplyStatusList.APPLY]: '청약 신청',
  [SubscriptionApplyStatusList.WITHDRAW]: '청약 철회',
  [SubscriptionApplyStatusList.WAIT]: '배정중',
  [SubscriptionApplyStatusList.ALLOT_TARGET]: '배정대상',
  [SubscriptionApplyStatusList.ALLOT]: '배정완료',
  [SubscriptionApplyStatusList.NOT_ALLOT]: '배정 실패',
  [SubscriptionApplyStatusList.REFUND_WAIT]: '공모미달',
  [SubscriptionApplyStatusList.REFUND_DONE]: '공모미달',
};

export interface SubscriptionStatistics {
  allotStatistics: {
    allotCount: number;
    allotTotalMargin: number;
    allotTotalQuantity: number;
    transactionFee: number;
  };
  applyStatistics: {
    applyCount: number;
    applyTotalMargin: number;
    applyTotalQuantity: number;
  };
  infoStatistics: {
    offeringPrice: number;
    offeringTotalAmount: number;
    qualifiedOfferingAmount: number;
    subscriptionInfoId: number;
    totalQuantity: number;
  };
}

export enum SubscriptionSortType {
  LATEST = 'LATEST',
  DEADLINE = 'DEADLINE',
  APPLY_AMOUNT = 'APPLY_AMOUNT',
}

export interface SubscriptionIssuer {
  brc: string[];
  brn: string;
  createdAt: string;
  email: string;
  id: number;
  mobileNumber: string;
  name: string;
  updatedAt: string;
  userId: string;
}

export interface SubscriptionApplyLimit {
  subscriptionInfoId: number;
  applyLimitCountPerPeriod: number;
  applyLimitPeriod: number;
  applyLimitTotalCount: number;
  currentApplyCount: number;
  appliedListDuringPeriod: {
    createdAt: string;
    updatedAt: string;
    deleted: boolean;
    subscriptionApplyId: number;
    userId: string;
    applyQuantity: number;
    investorSuitabilityAgreedYn: string;
    subscriptionApplyStatus: SubscriptionApplyStatusList;
    margin: number;
    investmentAgreeAt: string;
    digitalSign: string;
    subscriptionInfo: {
      createdAt: string;
      updatedAt: string;
      deleted: boolean;
      subscriptionInfoId: number;
      securitiesId: string;
      offeringTotalAmount: number;
      offeringPrice: number;
      transactionFee: number;
    };
  }[];
}

export enum DocsType {
  'DOC_BUSINESS_REGISTRATION_CERTIFICATE' = 'DOC_BUSINESS_REGISTRATION_CERTIFICATE',
  'DOC_CORPORATE_REGISTRATION_CERTIFICATE' = 'DOC_CORPORATE_REGISTRATION_CERTIFICATE',
  'DOC_ARTICLES_OF_INCORPORATION' = 'DOC_ARTICLES_OF_INCORPORATION',
  'DOC_MINUTES_EXTRAORDINARY_GENERAL_MEETING' = 'DOC_MINUTES_EXTRAORDINARY_GENERAL_MEETING',
  'DOC_SECURITIES_SUBSCRIPTION_APP' = 'DOC_SECURITIES_SUBSCRIPTION_APP',
  'DOC_BOND_SUBSCRIPTION_APP' = 'DOC_BOND_SUBSCRIPTION_APP',
  'DOC_FINANCIAL_STATEMENTS' = 'DOC_FINANCIAL_STATEMENTS',
  'DOC_BUSINESS_PLAN' = 'DOC_BUSINESS_PLAN',
  'DOC_INVESTMENT_AGREEMENT' = 'DOC_INVESTMENT_AGREEMENT',
  'DOC_INVESTMENT_PROSPECTUS' = 'DOC_INVESTMENT_PROSPECTUS',
  'DOC_SECURITIES_STATEMENT' = 'DOC_SECURITIES_STATEMENT',
  'DOC_ETC' = 'DOC_ETC',
}

export const DocsTypeLabel = {
  [DocsType.DOC_BUSINESS_REGISTRATION_CERTIFICATE]: '사업자등록증',
  [DocsType.DOC_CORPORATE_REGISTRATION_CERTIFICATE]: '법인등기부등본',
  [DocsType.DOC_ARTICLES_OF_INCORPORATION]: '정관',
  [DocsType.DOC_MINUTES_EXTRAORDINARY_GENERAL_MEETING]: '임시주주총회의사록',
  [DocsType.DOC_SECURITIES_SUBSCRIPTION_APP]: '증권청약서',
  [DocsType.DOC_BOND_SUBSCRIPTION_APP]: '사채청약서',
  [DocsType.DOC_FINANCIAL_STATEMENTS]: '재무제표',
  [DocsType.DOC_BUSINESS_PLAN]: '사업계획서',
  [DocsType.DOC_INVESTMENT_AGREEMENT]: '투자계약서',
  [DocsType.DOC_INVESTMENT_PROSPECTUS]: '투자설명서',
  [DocsType.DOC_SECURITIES_STATEMENT]: '증권신고서',
  [DocsType.DOC_ETC]: '기타서류',
};
