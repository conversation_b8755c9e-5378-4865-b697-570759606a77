export interface SocialUser {
  id: string;
  mobile: string;
  name: string;
  birthday: string;
  birthyear: string;
  email: string;
  gender: string;
}

export enum SignupPath {
  EMAIL = 'email',
  KAKAO = 'kakao',
  NAVER = 'naver',
  CORPORATE = 'corporate',
}

export enum SignUpSteps {
  TYPE = 'TYPE',
  TERMS = 'TERMS',
  FORM = 'FORM',
  COMPLETE = 'COMPLETE',
}

export enum SocialSignUpSteps {
  TERMS = 'TERMS',
  PREVIEW = 'PREVIEW',
  EMAIL = 'EMAIL',
  MOBILE = 'MOBILE',
  NAME = 'NAME',
  COMPLETE = 'COMPLETE',
}

export enum MinorSignUpSteps {
  TERMS = 'TERMS',
  PREVIEW = 'PREVIEW',
  COMPLETE = 'COMPLETE',
}

export enum CorporateSignUpSteps {
  TERMS = 'TERMS',
  COMPLETE = 'COMPLETE',
}

export enum FindUserSteps {
  FORM = 'FORM',
  COMPLETE = 'COMPLETE',
}

export enum SignType {
  SIGNUP = 'signup',
  SIGNIN = 'signin',
}

export enum SignState {
  NAVER_SIGNIN = 'naver-signin',
  NAVER_SIGNUP = 'naver-signup',
  KAKAO_SIGNIN = 'kakao-signin',
  KAKAO_SIGNUP = 'kakao-signup',
  APPLE_SIGNIN = 'apple-signin',
  APPLE_SIGNUP = 'apple-signup',
}

export enum SocialProvider {
  KAKAO = 'kakao',
  NAVER = 'naver',
}

export interface SocialUserData {
  socialId: string;
  mobileNumber: string;
  type: SocialProvider;
}

export interface SocialAuthParams {
  state: string | null;
  code: string | null;
  error: string | null;
  device: string | null;
  accessToken: string | null;
}

export interface SocialAuthResult {
  isUser: boolean;
  userData?: SocialUserData;
}
