import { NotificationChannel } from '@/shared/types';

export interface SignUpPayload {
  email: string;
  password: string;
  name: string;
  mobileNumber: string;
  address?: string;
  addressDetail?: string;
}

export interface SocialUserCheckPayload {
  socialId: string | number;
  mobileNumber?: string;
  email?: string;
}

export interface FindUserIdPayload {
  name: string;
  mobileNumber: string;
  apiKey: string;
}

export interface PasswordResetPayload {
  account: string;
  mobileNumber: string;
  apiKey: string;
  password: string;
}
export interface RegisterNotificationAgreementPayload {
  userId: string;
  channel: NotificationChannel[];
}

export interface RegisterKycPayload {
  ci: string;
  result: string;
  name: string;
  birthDate: string;
}

export interface CorporateSignUpPayload {
  email: string;
  companyName: string;
  crn: string;
  brn: string;
  representativeName: string;
  managerName: string;
  managerMobileNumber: string;
  brc: File[];
}

export interface SendGeneralResetLinkPayload {
  name: string;
  account: string;
}
