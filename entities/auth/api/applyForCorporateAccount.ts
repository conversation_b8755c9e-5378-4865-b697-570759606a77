import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';

import { CorporateSignUpPayload } from '../interface';

export const applyForCorporateAccount = async (payload: CorporateSignUpPayload) => {
  const formData = new FormData();

  Object.entries(payload).forEach(([key, value]) => {
    if (key === 'brc' && Array.isArray(value)) {
      value.forEach((file) => {
        formData.append('brc', file);
      });
    } else {
      formData.append(key, value as string);
    }
  });

  const response = await userPublicInstance.post('/auth/corporate/signup', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response;
};
