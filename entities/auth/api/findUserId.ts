import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';

import { FindUserIdPayload } from '../interface';

export const findUserId = async (payload: FindUserIdPayload) => {
  const { apiKey } = payload;
  userPublicInstance.defaults.headers.common['api-token'] = apiKey;
  const response = await userPublicInstance.post('/auth/find-id', {
    name: payload.name,
    mobileNumber: payload.mobileNumber,
  });

  return response.data;
};
