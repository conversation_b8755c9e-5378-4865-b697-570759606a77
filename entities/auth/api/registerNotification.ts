import axios from 'axios';

import env from '@/shared/lib/env.schema';

export const registerNotification = async (
  userId: string,
  { isEmailAgree, isSmsAgree }: { isEmailAgree: boolean; isSmsAgree: boolean },
) => {
  const channel: string[] = [];

  if (isEmailAgree) {
    channel.push('EMAIL');
  }

  if (isSmsAgree) {
    channel.push('SMS');
  }

  const response = await axios.post(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/auth/notification`, {
    userId,
    type: ['SERVICE', 'MARKETING'],
    channel,
  });

  return response.data;
};
