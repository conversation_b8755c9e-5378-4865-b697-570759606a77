import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';

import { PasswordResetPayload } from '../interface';

export const passwordReset = async (payload: PasswordResetPayload) => {
  const { account, mobileNumber, apiKey, password } = payload;
  userPublicInstance.defaults.headers.common['api-token'] = apiKey;
  const response = await userPublicInstance.post('/auth/reset-password', {
    account,
    mobileNumber,
    password,
  });
  return response.data;
};
