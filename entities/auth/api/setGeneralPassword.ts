import axios from 'axios';

import env from '@/shared/lib/env.schema';

export const setGeneralPassword = async ({
  token,
  password,
}: {
  token: string;
  password: string;
}) => {
  const response = await axios.post(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/auth/user/reset-password`,
    { password },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return response.data;
};
