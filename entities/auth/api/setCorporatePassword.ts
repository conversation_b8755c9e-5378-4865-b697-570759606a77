import axios from 'axios';

import env from '@/shared/lib/env.schema';

export const setCorporatePassword = async ({
  token,
  password,
}: {
  token: string;
  password: string;
}) => {
  const response = await axios.post(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/auth/corporate/reset-password`,
    { password },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return response.data;
};
