import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';

export const registerAgreement = async (userId: string) => {
  const response = await userPublicInstance.post(`/auth/agreement`, {
    userId,
    agreements: [
      {
        category: 'PRIVACY_POLICY',
        contentId: '678e4b83cf2cf940c91dd72f',
        isAgreed: true,
      },
      {
        category: 'TERMS_OF_USE',
        contentId: '678e4b83cf2cf940c91dd72f',
        isAgreed: true,
      },
      {
        category: 'MARKETING_USAGE',
        contentId: '678e4b83cf2cf940c91dd72f',
        isAgreed: true,
      },
    ],
  });

  return response.data;
};
