import { userPublicInstance } from '@/shared/lib/axios/userPublicInstance';
import { NotificationType } from '@/shared/types';

import { RegisterNotificationAgreementPayload } from '../interface';

export const registerNotificationAgreement = async (
  payload: RegisterNotificationAgreementPayload,
) => {
  const response = await userPublicInstance.post(`/auth/notification`, {
    ...payload,
    type: [NotificationType.MARKETING, NotificationType.SERVICE],
  });

  return response.data;
};
