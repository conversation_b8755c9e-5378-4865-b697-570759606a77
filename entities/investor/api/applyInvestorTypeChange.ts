import { userPrivateInstance } from '@/shared/lib/axios/userPrivateInstance';

import { InvestorTypeChangePayload } from '../interface';

export const applyInvestorTypeChange = async (payload: InvestorTypeChangePayload) => {
  const formData = new FormData();

  Object.entries(payload).forEach(([key, value]) => {
    if (key === 'files' && Array.isArray(value)) {
      value.forEach((file) => {
        formData.append('files', file);
      });
    } else {
      formData.append(key, value as string);
    }
  });

  const response = await userPrivateInstance.post(`/user/investor-qualification`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
