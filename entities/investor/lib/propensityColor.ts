import { PropensityTypeEnum } from '../types';

export const propensityColor = (propensityType: PropensityTypeEnum) => {
  switch (propensityType) {
    case PropensityTypeEnum.AGGRESSIVE:
      return {
        bgColor: 'bg-pink-00',
        borderColor: 'border-pink-50',
        textColor: 'text-pink-500',
      };
    case PropensityTypeEnum.ACTIVE:
      return {
        bgColor: 'bg-purple-00',
        borderColor: 'border-deep-purple-50',
        textColor: 'text-deep-purple-500',
      };
    case PropensityTypeEnum.NEUTRAL:
      return {
        bgColor: 'bg-primary-00',
        borderColor: 'border-primary-100',
        textColor: 'text-primary-500',
      };
    case PropensityTypeEnum.STABLE:
      return {
        bgColor: 'bg-cyan-00',
        borderColor: 'border-cyan-100',
        textColor: 'text-cyan-500',
      };
    case PropensityTypeEnum.CONSERVATIVE:
      return {
        bgColor: 'bg-green-00',
        borderColor: 'border-green-10',
        textColor: 'text-green-500',
      };
  }
};
