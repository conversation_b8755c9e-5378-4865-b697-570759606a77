export interface SuitabilityTest {
  id: number;
  testName: string;
  description: string;
  createdAt: string;
  questions: Question[];
}

// 진단 문제
export interface Question {
  questionNumber: number;
  questionText: string;
  selectionMode?: string;
  options: { label: 'A' | 'B'; text: string; selection?: string[] }[];
  correctAnswer?: 'A' | 'B';
  score?: number[];
}

// 투자 성향 진단 약관
export interface PropensityTerms {
  isHopeInvestment: boolean | undefined;
  isInfoAgree: boolean | undefined;
  isVulnerable: boolean | undefined;
}

export type SingleAnswer = {
  questionNumber: number;
  selectionMode: 'single';
  selectedKey: string; // 예: 'A2'
};

export type MultiAnswer = {
  questionNumber: number;
  selectionMode: 'multi';
  selectedKeys: string[]; // 예: ['A2', 'A3']
};

export type MultiRatioAnswer = {
  questionNumber: number;
  selectionMode: 'multi_v2';
  selected: { key: string; value: string }[];
};

export type PropensityAnswer = {
  [key: number]: SingleAnswer | MultiAnswer | MultiRatioAnswer;
};

export const PropensityAssetTypeRatioValues: Record<string, string> = {
  guaranteedProduct: '보장성상품',
  stock: '투자성상품',
  bond: '대출성상품',
  etc: '기타',
};

// 투자 성향 기대기간
export enum ExpectedPeriodEnum {
  UNDER_1Y = 'UNDER_1Y',
  UNDER_2Y = 'UNDER_2Y',
  UNDER_3Y = 'UNDER_3Y',
  UNDER_5Y = 'UNDER_5Y',
  OVER_5Y = 'OVER_5Y',
}

export const ExpectedPeriodEnumValues: Record<ExpectedPeriodEnum, string> = {
  [ExpectedPeriodEnum.UNDER_1Y]: '1년 미만',
  [ExpectedPeriodEnum.UNDER_2Y]: '1년 ~ 2년 미만',
  [ExpectedPeriodEnum.UNDER_3Y]: '2년 ~ 3년 미만',
  [ExpectedPeriodEnum.UNDER_5Y]: '3년 ~ 5년 미만',
  [ExpectedPeriodEnum.OVER_5Y]: '5년 이상',
};

//투자 성향 위험도
export enum RiskToleranceEnum {
  PRINCIPAL_PROTECTION = 'PRINCIPAL_PROTECTION', // 원금보전
  PLUS_MINUS_10 = 'PLUS_MINUS_10', // +-10%
  PLUS_MINUS_20 = 'PLUS_MINUS_20', // +- 20%
  PLUS_MINUS_30 = 'PLUS_MINUS_30', // +- 30%
}

export const RiskToleranceEnumValues: Record<RiskToleranceEnum, string> = {
  [RiskToleranceEnum.PRINCIPAL_PROTECTION]: '원금보전',
  [RiskToleranceEnum.PLUS_MINUS_10]: '±10%',
  [RiskToleranceEnum.PLUS_MINUS_20]: '±20%',
  [RiskToleranceEnum.PLUS_MINUS_30]: '±30%',
};

// 투자 성향 유형
export enum PropensityTypeEnum {
  AGGRESSIVE = 'AGGRESSIVE', // 공격투자형
  ACTIVE = 'ACTIVE', // 적극투자형
  NEUTRAL = 'NEUTRAL', // 위험중립형
  STABLE = 'STABLE', // 안정추구형
  CONSERVATIVE = 'CONSERVATIVE', // 안정형
}

export const PropensityTypeEnumValues: Record<PropensityTypeEnum, string> = {
  [PropensityTypeEnum.AGGRESSIVE]: '공격투자형',
  [PropensityTypeEnum.ACTIVE]: '적극투자형',
  [PropensityTypeEnum.NEUTRAL]: '위험중립형',
  [PropensityTypeEnum.STABLE]: '안정추구형',
  [PropensityTypeEnum.CONSERVATIVE]: '안정형',
};

export const PropensityLevels: Record<PropensityTypeEnum, string> = {
  [PropensityTypeEnum.AGGRESSIVE]: 'IPL10',
  [PropensityTypeEnum.ACTIVE]: 'IPL20',
  [PropensityTypeEnum.NEUTRAL]: 'IPL30',
  [PropensityTypeEnum.STABLE]: 'IPL40',
  [PropensityTypeEnum.CONSERVATIVE]: 'IPL50',
};

export enum PropensityLevelEnum {
  IPL10 = 'IPL10',
  IPL20 = 'IPL20',
  IPL30 = 'IPL30',
  IPL40 = 'IPL40',
  IPL50 = 'IPL50',
}

export const PropensityLevelEnumValues: Record<PropensityLevelEnum, string> = {
  [PropensityLevelEnum.IPL10]: '공격투자형',
  [PropensityLevelEnum.IPL20]: '적극투자형',
  [PropensityLevelEnum.IPL30]: '위험중립형',
  [PropensityLevelEnum.IPL40]: '안정추구형',
  [PropensityLevelEnum.IPL50]: '안정형',
};

// 투자 성향 연령대
export enum PropensityAgeEnum {
  UNDER_19 = 'UNDER_19', // 만 19세 미만
  AGE_19_30 = 'AGE_19_30', // 만 19세 ~ 만 30세
  AGE_31_40 = 'AGE_31_40', // 만 31세 ~ 만 40세
  AGE_41_50 = 'AGE_41_50', // 만 41세 ~ 만 50세
  AGE_51_64 = 'AGE_51_64', // 만 51세 ~ 만 64세
  AGE_65_79 = 'AGE_65_79', // 만 51세 ~ 만 64세
  OVER_80 = 'OVER_80', // 만 65세 이상
}

export const PropensityAgeEnumValues: Record<PropensityAgeEnum, string> = {
  [PropensityAgeEnum.UNDER_19]: '만 19세 미만',
  [PropensityAgeEnum.AGE_19_30]: '만 19세 ~ 만 30세',
  [PropensityAgeEnum.AGE_31_40]: '만 31세 ~ 만 40세',
  [PropensityAgeEnum.AGE_41_50]: '만 41세 ~ 만 50세',
  [PropensityAgeEnum.AGE_51_64]: '만 51세 ~ 만 64세',
  [PropensityAgeEnum.AGE_65_79]: '만 65세 ~ 만 79세',
  [PropensityAgeEnum.OVER_80]: '만 80세 이상',
};

// 투자 성향 소득
export enum PropensityEarningsEnum {
  UNDER_10M = 'UNDER_10M', // 1천만원 미만
  UNDER_30M = 'UNDER_30M', // 3천만원 이하
  UNDER_50M = 'UNDER_50M', // 5천만원 이하
  UNDER_100M = 'UNDER_100M', // 1억원 이하
  OVER_100M = 'OVER_100M', // 1억원 초과
}

export const PropensityEarningsEnumValues: Record<PropensityEarningsEnum, string> = {
  [PropensityEarningsEnum.UNDER_10M]: '1천만원 미만',
  [PropensityEarningsEnum.UNDER_30M]: '3천만원 이하',
  [PropensityEarningsEnum.UNDER_50M]: '5천만원 이하',
  [PropensityEarningsEnum.UNDER_100M]: '1억원 이하',
  [PropensityEarningsEnum.OVER_100M]: '1억원 초과',
};

// 투자 성향 자산 배분 비율
export enum PropensityAllocationRatioEnum {
  UNDER_10 = 'UNDER_10', // 10%미만
  BETWEEN_10_20 = 'BETWEEN_10_20', // 10%~ 20% 미만
  BETWEEN_20_30 = 'BETWEEN_20_30', // 20% ~ 30% 미만
  BETWEEN_30_50 = 'BETWEEN_30_50', // 30% ~ 50% 미만
  OVER_50 = 'OVER_50', // 50% 이상
}

export const PropensityAllocationRatioValues: Record<PropensityAllocationRatioEnum, string> = {
  [PropensityAllocationRatioEnum.UNDER_10]: '10% 미만',
  [PropensityAllocationRatioEnum.BETWEEN_10_20]: '10% ~ 20% 미만',
  [PropensityAllocationRatioEnum.BETWEEN_20_30]: '20% ~ 30% 미만',
  [PropensityAllocationRatioEnum.BETWEEN_30_50]: '30% ~ 50% 미만',
  [PropensityAllocationRatioEnum.OVER_50]: '50% 이상',
};

// 투자 성향 투자경험 유형
export enum InvestmentExperienceTypeEnum {
  DEPOSIT = 'DEPOSIT',
  BOND = 'BOND',
  MIXED = 'MIXED',
  STOCK = 'STOCK',
  ELW = 'ELW',
}

export const InvestmentExperienceTypeEnumValues: Record<InvestmentExperienceTypeEnum, string> = {
  [InvestmentExperienceTypeEnum.DEPOSIT]: '예금',
  [InvestmentExperienceTypeEnum.BOND]: '채권',
  [InvestmentExperienceTypeEnum.MIXED]: '혼합형',
  [InvestmentExperienceTypeEnum.STOCK]: '주식',
  [InvestmentExperienceTypeEnum.ELW]: 'ELW',
};

// 투자 성향 투자 목적 유형
export enum InvestmentPurposeTypeEnum {
  DEBT_REPAYMENT = 'DEBT_REPAYMENT', // 자산 채무상환
  LIVING_EXPENSES = 'LIVING_EXPENSES', // 생활비 마련
  SURPLUS_FUNDS = 'SURPLUS_FUNDS', // 여유 자금
  ASSET_GROWTH = 'ASSET_GROWTH', // 자산 늘리기
}

export const InvestmentPurposeTypeValues: Record<InvestmentPurposeTypeEnum, string> = {
  [InvestmentPurposeTypeEnum.DEBT_REPAYMENT]: '자산 채무상환',
  [InvestmentPurposeTypeEnum.LIVING_EXPENSES]: '생활비 마련',
  [InvestmentPurposeTypeEnum.SURPLUS_FUNDS]: '여유 자금 마련',
  [InvestmentPurposeTypeEnum.ASSET_GROWTH]: '자산 늘리기',
};

// 투자 성향 금융상품 이해도
export enum InvestmentUnderstandingTypeEnum {
  NONE = 'NONE', // 경험 없음
  BASIC = 'BASIC', // 조금 이해함
  ADVANCED = 'ADVANCED', // 깊이 있게 이해함
  EXPERT = 'EXPERT', // 구조를 이해함
}

export const InvestmentUnderstandingTypeEnumValues: Record<
  InvestmentUnderstandingTypeEnum,
  string
> = {
  [InvestmentUnderstandingTypeEnum.NONE]: '경험 없음',
  [InvestmentUnderstandingTypeEnum.BASIC]: '조금 이해함',
  [InvestmentUnderstandingTypeEnum.ADVANCED]: '깊이 있게 이해함',
  [InvestmentUnderstandingTypeEnum.EXPERT]: '구조를 이해함',
};

export interface PropensityScore {
  score: number;
  convertedScore: number;
  type: PropensityTypeEnum | undefined;
}

export enum PropensitySteps {
  NOTICE = 'NOTICE',
  TYPE = 'TYPE',
  TERMS = 'TERMS',
  QUESTION = 'QUESTION',
  RESULT = 'RESULT',
}

export enum InvestorQualificationType {
  GENERAL = 'GENERAL', // 일반
  QUALIFIED = 'QUALIFIED', // 적격
  PROFESSIONAL = 'PROFESSIONAL', // 전문
}

export const InvestorQualificationTypeValues: Record<InvestorQualificationType, string> = {
  [InvestorQualificationType.GENERAL]: '일반 투자자',
  [InvestorQualificationType.QUALIFIED]: '적격 투자자',
  [InvestorQualificationType.PROFESSIONAL]: '전문 투자자',
};

export enum InvestorSuitabilityTestStep {
  NOTICE = 'NOTICE',
  QUESTION = 'QUESTION',
  RESULT = 'RESULT',
}

export interface InvestorLimit {
  qualificationType: InvestorQualificationType;
  totalInvestLimitAmount: number; // 전체 투자 한도 금액
  issuerInvestLimitAmount: number; // 발행인별 투자 한도 금액
  ksdInvestorLimitInfo: {
    qualificationType: InvestorQualificationType;
    limitType: string;
  };
  registeredInvestorLimitInfo: {
    qualificationType: InvestorQualificationType;
    totalInvestLimitAmount: number; // 전체 투자 한도 금액
    issuerInvestLimitAmount: number; // 발행인별 투자 한도 금액
  };
}
