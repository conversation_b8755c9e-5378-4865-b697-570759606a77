import { InvestorQualificationType } from '../types';

export interface InvestorSuitabilityTestRegisterPayload {
  investorSuitabilityQuestionId: string;
  consent: boolean;
}

export interface InvestorPropensityRegisterPayload {
  report: string;
  score: number;
  convertedScore: number;
  grade: string;
  consent: boolean;
}

export interface InvestorTypeChangePayload {
  qualificationType: InvestorQualificationType;
  files: File[];
}

export enum InvestorQualificationStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
