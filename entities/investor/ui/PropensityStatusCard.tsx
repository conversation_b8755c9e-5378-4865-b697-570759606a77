import React from 'react';

import { propensityColor } from '../lib/propensityColor';
import { PropensityTypeEnum, PropensityTypeEnumValues } from '../types';

interface PropensityStatusCardProps {
  propensityType: PropensityTypeEnum;
}

export const PropensityStatusCard = ({ propensityType }: PropensityStatusCardProps) => {
  return (
    <div
      className={`flex w-full flex-col items-center justify-center rounded-lg border ${propensityColor(propensityType).borderColor} ${propensityColor(propensityType).bgColor} py-10`}
    >
      <h4 className="text-sm font-semibold">나의 투자 성향</h4>
      <h3 className={`text-20 ${propensityColor(propensityType).textColor}`}>
        {PropensityTypeEnumValues[propensityType]}
      </h3>
    </div>
  );
};
