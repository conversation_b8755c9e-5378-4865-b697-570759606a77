import React from 'react';

import { Separator } from '@/shared/ui/shadcn/separator';

interface PropensityResultItemProps {
  label: string;
  value?: string;
  isLast?: boolean;
  values?: string[];
}

export const PropensityResultItem = ({
  label,
  value,
  isLast,
  values,
}: PropensityResultItemProps) => {
  return (
    <>
      <div className="flex items-center justify-between py-2 text-sm">
        <span className="min-w-32 text-gray-600">{label}</span>
        {value && <span className="whitespace-pre-wrap">{value}</span>}
        {values && (
          <div className="flex flex-col gap-1">
            {values.map((v) => (
              <span key={v} className="text-right text-sm">
                {v}
              </span>
            ))}
          </div>
        )}
      </div>
      {!isLast && <Separator className="my-2 h-[1px] bg-gray-200" />}
    </>
  );
};
