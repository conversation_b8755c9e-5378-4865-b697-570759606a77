import React from 'react';

export const InvestorTestButtonSkeleton = ({ icon = false }: { icon?: boolean }) => {
  return (
    <div className="flex w-full items-center justify-between gap-3 rounded-lg border border-gray-300 bg-gray-50 px-5 py-4 sm:p-8">
      <div className="flex items-center gap-3">
        {icon && (
          <div className="flex h-[30px] w-[30px] items-center justify-center rounded border border-gray-300 bg-white">
            <div className="h-4 w-4 rounded bg-gray-200"></div>
          </div>
        )}
        <div className="h-6 w-64 rounded bg-gray-200"></div>
      </div>
    </div>
  );
};
