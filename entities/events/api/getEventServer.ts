'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

import { Event } from '../types';

export const getEventServer = async (id: string): Promise<Event> => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/event/${id}`, {
    cache: 'no-store',
    next: {
      tags: ['events', id],
    },
  });

  if (!response.ok) {
    logServerError('get event server error', {
      error: {
        params: {
          id,
        },
        response,
      },
    });
    throw new Error('Failed to fetch curation');
  }

  const data = await response.json();

  return data.data;
};
