import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

import { Event, EventStatus } from '../types';
import { EventStatusBadge } from './EventStatusBadge';

// 이벤트 카드 컴포넌트 - 이벤트 목록에서 각 이벤트를 보여주는 카드 컴포넌트
export const EventCard = ({ event }: { event: Event }) => {
  const { thumbnail, title, startDate, endDate, eventStatus } = event;

  const isDone = eventStatus === EventStatus.DONE;

  const { routerPush } = useWebViewRouter();

  return (
    <div onClick={() => routerPush(`/contents/events/${event.id}`)} className="cursor-pointer">
      <div>
        <ThumbnailSection thumbnail={thumbnail} isDone={isDone} />
        <EventInfoSection
          eventStatus={eventStatus}
          startDate={startDate}
          endDate={endDate}
          title={title}
        />
      </div>
    </div>
  );
};

interface ThumbnailSectionProps {
  thumbnail: { url: string; name: string };
  isDone: boolean;
}

const ThumbnailSection = ({ thumbnail, isDone }: ThumbnailSectionProps) => (
  <div className="relative aspect-[327/210] ml:aspect-[368/237]">
    <FallbackImage
      src={thumbnail.url}
      alt={thumbnail.name}
      fill
      style={{ objectFit: 'cover' }}
      sizes="(max-width: 996px) 100vw, (max-width: 1440px) 576px, 368px"
      className="rounded-[10px] border border-[#000]/10"
    />
    <div className="absolute left-0 top-0 h-full w-full rounded-xl bg-black/10 stroke-black" />
    {isDone && (
      <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center rounded-[10px] bg-black/50">
        <p className="text-2xl font-semibold text-white">종료된 이벤트</p>
      </div>
    )}
  </div>
);

interface EventInfoSectionProps {
  eventStatus: EventStatus;
  startDate: string;
  endDate: string;
  title: string;
}

const EventInfoSection = ({ eventStatus, startDate, endDate, title }: EventInfoSectionProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <>
      <div className="mb-[6px] mt-5 flex items-center gap-3 sm:mb-3 sm:mt-8 sm:gap-6">
        <EventStatusBadge event_status={eventStatus} />
        <Separator orientation="vertical" className="h-4 bg-gray-300" />
        <p className="text-xs text-gray-700 sm:text-base">
          {YYYYMMDD(startDate)} ~ {YYYYMMDD(endDate)}
        </p>
      </div>
      <h4 className="line-clamp-2 text-[17px] font-bold sm:text-xl">{title}</h4>
    </>
  );
};
