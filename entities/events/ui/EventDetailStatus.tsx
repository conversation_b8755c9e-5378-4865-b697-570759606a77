import React from 'react';

import { EventStatus, EventStatusLabel } from '../types';

export const EventDetailStatus = ({ eventStatus }: { eventStatus: EventStatus }) => {
  const color = eventStatus === EventStatus.DONE ? 'text-gray-500' : 'text-primary-500';
  const text = eventStatus === EventStatus.DONE ? '된 이벤트' : ' 이벤트';

  return (
    <p className={`mb-6 text-center font-semibold ${color}`}>
      {EventStatusLabel[eventStatus]}
      {text}
    </p>
  );
};
