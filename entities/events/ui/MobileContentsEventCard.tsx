import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';

import { Event } from '../types';

export const MobileContentsEventCard = (event: Event) => {
  const { routerPush } = useWebViewRouter();

  return (
    <div
      className="cursor-pointer space-y-[10px]"
      onClick={() => routerPush(`/contents/events/${event.id}`)}
    >
      <FallbackImage
        src={event.thumbnail.url}
        alt={event.title}
        width={158}
        height={100}
        className="h-[100px] w-[158px] rounded object-cover"
      />
      <h4 className="line-clamp-2 text-sm leading-[150%]">{event.title}</h4>
    </div>
  );
};
