import { AttachFile } from '@/shared/types';

export interface Event {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  createdBy: string;
  publishedAt: string;
  status: string;
  thumbnail: AttachFile;
  updatedAt: string;
  updatedBy: string;
  version: number;
  versions: any[];
  startDate: string;
  endDate: string;
  eventStatus: EventStatus;
  metaDescription: string;
  enableComment: boolean;
  mobileContent?: string;
  enableCommentCount: boolean;
}

export enum EventStatus {
  READY = 'READY',
  PROGRESS = 'PROGRESS',
  DONE = 'DONE',
}

export const EventStatusLabel = {
  [EventStatus.READY]: '준비중',
  [EventStatus.PROGRESS]: '진행중',
  [EventStatus.DONE]: '종료',
};
