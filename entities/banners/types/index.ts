import { AttachFile } from '@/shared/types';

export interface Banner {
  banner_items: BannerItem[];
  createdAt: string;
  createdBy: string;
  id: string;
  publishedAt: string;
  status: string;
  type: string;
  updatedAt: string;
  updatedBy: string;
  version: number;
  versions: any[];
}

export interface BannerItem {
  title: string;
  order: string;
  description: string;
  image: AttachFile;
  link: string;
  mobileImage?: AttachFile;
}
