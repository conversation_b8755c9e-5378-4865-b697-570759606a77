import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { BannerRequest } from '../interface';
import { Banner } from '../types';

export const getBanners = async (params: BannerRequest): Promise<ListResponse<Banner>> => {
  const response = await cmsPublicInstance.get('/contents/banner', {
    params,
  });
  return response.data;
};
