'use server';

import env from '@/shared/lib/env.schema';

import { BannerType } from '../interface';

export const getBannersServer = async ({ type }: { type: BannerType }) => {
  const banners = await fetch(
    `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/banner?type=${type}`,
    {
      cache: 'force-cache',
      next: { tags: ['banners', type], revalidate: 60 * 1 },
    },
  );
  const data = await banners.json();

  return data.data;
};
