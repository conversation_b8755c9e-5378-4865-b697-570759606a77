import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { FaqListRequest } from '../interface';
import { Faq } from '../types';

export const getFaqs = async (params: FaqListRequest): Promise<ListResponse<Faq>> => {
  const response = await cmsPublicInstance.get('/contents/faq?sort=order&order=asc', { params });
  return response.data;
};
