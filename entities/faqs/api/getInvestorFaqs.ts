import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { InvestorFaqListRequest } from '../interface';
import { Faq } from '../types';

export const getInvestorFaqs = async (
  params: InvestorFaqListRequest,
): Promise<ListResponse<Faq>> => {
  const response = await cmsPublicInstance.get('/contents/issuer-faq', { params });
  return response.data;
};
