export interface Faq {
  _id: string;
  content: string;
  category: FaqCategory;
  createdAt: string;
  createdBy: string;
  order: 4;
  publishedAt: string;
  status: string;
  title: string;
  updatedAt: string;
  updatedBy: string;
  version: number;
  versions: any[];
  id: string;
}

export enum FaqCategory {
  'SERVICE' = 'SERVICE',
  'ACCOUNT' = 'ACCOUNT',
  'OTHER' = 'OTHER',
}

export const FaqCategoryLabel = {
  [FaqCategory.SERVICE]: '서비스',
  [FaqCategory.ACCOUNT]: '회원 및 계정',
  [FaqCategory.OTHER]: '기타',
};

export enum FaqAnswerStatus {
  'REQUEST' = 'REQUEST',
  'ANSWER' = 'ANSWER',
}
