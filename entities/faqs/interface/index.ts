import { ListRequest } from '@/shared/interface';

import { FaqAnswerStatus, FaqCategory } from '../types';

export interface FaqListRequest extends ListRequest {
  category?: FaqCategory;
  title?: string;
  content?: string;
}

export interface InvestorFaqListRequest extends ListRequest {
  securitiesId: string;
}

export interface CreateFaqPayload {
  status: 'published';
  privacyAgreement: boolean;
  notificationChannel: string;
  title: string;
  content: string;
  category: FaqCategory;
  answer?: string;
  userId: string;
  answerStatus: FaqAnswerStatus.REQUEST;
  attachFiles?: File[];
}
