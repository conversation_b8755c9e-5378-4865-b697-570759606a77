import { ListRequest, ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { Comment } from '../types';

export const getComments = async (
  eventId: string,
  params: ListRequest,
): Promise<ListResponse<Comment>> => {
  const response = await cmsPublicInstance.get(`/comment/content/${eventId}`, { params });
  return response.data;
};
