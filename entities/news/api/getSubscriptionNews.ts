import { ListRequest, ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { SubscriptionCmsContent } from '../../subscriptions/types';

export const getSubscriptionNews = async (
  securitiesId: string,
  params: ListRequest,
): Promise<ListResponse<SubscriptionCmsContent>> => {
  const response = await cmsPublicInstance('/contents/securities-news', {
    params: {
      securities: securitiesId,
      page: 1,
      perPage: params.perPage,
    },
  });

  return response.data;
};
