import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

import { News } from '../types';

interface NewsCardProps {
  news: News;
}

export const NewsCard = ({ news }: NewsCardProps) => {
  const { link, thumbnail, title, createdAt, mediaCompany } = news;

  const { openExternalLink } = useWebViewRouter();

  const { YYYYMMDD } = utilFormats();

  return (
    <div onClick={() => openExternalLink(link)}>
      <div className="relative aspect-[327/210] ml:aspect-[368/237]">
        <FallbackImage
          src={thumbnail.url}
          alt={thumbnail.name}
          fill
          style={{ objectFit: 'cover' }}
          sizes="(max-width: 996px) 100vw, (max-width: 1440px) 576px, 368px"
          className="rounded-[10px]"
        />
      </div>
      <div className="mb-[6px] mt-5 flex items-center gap-3 text-xs text-gray-700 sm:mb-3 sm:gap-6 sm:text-base ml:mt-8">
        <p>{mediaCompany}</p>
        <Separator orientation="vertical" className="h-4 bg-gray-300" />
        <p className="">{YYYYMMDD(createdAt)}</p>
      </div>
      <h4 className="line-clamp-2 text-[17px] font-bold sm:text-xl">{title}</h4>
    </div>
  );
};
