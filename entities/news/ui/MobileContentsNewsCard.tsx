import Link from 'next/link';

import { News } from '@/entities/news/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

export const MobileContentsNewsCard = (news: News) => {
  const { thumbnail, title, createdAt, mediaCompany, link } = news;

  const { openExternalLink } = useWebViewRouter();

  const { YYYYMMDD } = utilFormats();

  return (
    <div className="space-y-[10px]" onClick={() => openExternalLink(link)}>
      <FallbackImage
        src={thumbnail.url}
        alt={title}
        width={158}
        height={100}
        className="h-[100px] w-[158px] rounded object-cover"
      />
      <div className="space-y-1">
        <div className="flex items-center gap-3 text-xs text-gray-500">
          <p>{mediaCompany}</p>
          <Separator orientation="vertical" className="h-4 bg-gray-300" />
          <p className="">{YYYYMMDD(createdAt)}</p>
        </div>
        <h4 className="line-clamp-2 text-sm leading-[150%]">{title}</h4>
      </div>
    </div>
  );
};
