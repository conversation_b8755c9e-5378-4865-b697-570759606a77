'use server';

import { logServerError } from '@artbloc/next-js-logger/server';

import env from '@/shared/lib/env.schema';

import { Notice } from '../types';

export const getNoticeServer = async (id: string): Promise<Notice> => {
  const response = await fetch(`${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/cms/contents/notice/${id}`, {
    cache: 'force-cache',
    next: {
      tags: ['notices', id],
      revalidate: 60 * 1,
    },
    headers: {
      Accept: 'application/json', // JSON 응답을 요청
    },
  });

  if (!response.ok) {
    logServerError('get notice server error', {
      error: {
        params: {
          id,
        },
        response,
      },
    });
    throw new Error('Failed to fetch curation');
  }

  const data = await response.json();

  return data.data;
};
