import { NoticeListRequest } from '@/features/notices/interface';

import { ListResponse } from '@/shared/interface';
import { cmsPublicInstance } from '@/shared/lib/axios/cmsPublicInstance';

import { Notice } from '../types';

export const getNotices = async (params: NoticeListRequest): Promise<ListResponse<Notice>> => {
  const response = await cmsPublicInstance.get('/contents/notice', { params });
  return response.data;
};
