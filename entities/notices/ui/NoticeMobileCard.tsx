import React from 'react';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

import { Notice } from '../types';

interface NoticeMobileCardProps {
  notice: Notice;
  isLast: boolean;
}

export const NoticeMobileCard = ({ notice, isLast }: NoticeMobileCardProps) => {
  const { YYYYMMDD } = utilFormats();

  const { routerPush } = useWebViewRouter();

  return (
    <div
      className={`cursor-pointer space-y-1 border-t border-gray-300 py-4 text-gray-700 ${isLast && 'border-b'}`}
      onClick={() => routerPush(`/contents/notices/${notice.id}`)}
    >
      <h4 className="font-bold text-gray-900">{notice.title}</h4>
      <span className="text-xs">{YYYYMMDD(notice.createdAt)}</span>
    </div>
  );
};
