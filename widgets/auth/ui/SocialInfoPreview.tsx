import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { UseFormReturn } from 'react-hook-form';

import { SocialSignUpFormData } from '@/features/auth/lib/socialSignUpFormSchema';

import { AuthFormContainer } from '@/shared/ui/AuthFormContainer';
import { EmptyUserField } from '@/shared/ui/EmptyUserField';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

interface SocialInfoPreviewProps {
  form: UseFormReturn<SocialSignUpFormData>;
  handleNextStep: () => void;
  isLoading: boolean;
}

export const SocialInfoPreview = ({ form, handleNextStep, isLoading }: SocialInfoPreviewProps) => {
  const { watch } = form;
  const email = watch('email');
  const mobile = watch('mobileNumber');
  const name = watch('name');

  return (
    <AuthFormContainer>
      <div>
        <p className="text-xl font-semibold sm:text-2xl">
          서비스 이용을 위한 <br />
          소셜 회원가입 정보를 확인해 주세요.
        </p>
        <div className="my-10 w-full space-y-4 rounded-[10px] border border-gray-300 bg-gray-50 p-7 sm:my-16">
          <div className="flex w-full justify-between">
            <span>이메일</span>
            {email ? (
              <div className="flex items-center gap-[10px]">
                <span className="font-semibold">{email}</span>
                <CheckCircleIcon className="h-4 w-4" color="green" />
              </div>
            ) : (
              <EmptyUserField />
            )}
          </div>
          <div className="flex w-full justify-between">
            <span>휴대폰 번호</span>
            {mobile ? (
              <div className="flex items-center gap-[10px]">
                <span className="font-semibold">{mobile}</span>
                <CheckCircleIcon className="h-4 w-4" color="green" />
              </div>
            ) : (
              <EmptyUserField />
            )}
          </div>
          <div className="flex w-full justify-between">
            <span>이름</span>
            {name ? (
              <div className="flex items-center gap-[10px]">
                <span className="font-semibold">{name}</span>
                <CheckCircleIcon className="h-4 w-4" color="green" />
              </div>
            ) : (
              <EmptyUserField />
            )}
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 left-0 w-full bg-white px-6 py-2 sm:static sm:p-0">
        <PrimaryButton
          text="다음"
          disabled={isLoading}
          className="h-12 w-full text-base"
          onClick={handleNextStep}
        />
      </div>
    </AuthFormContainer>
  );
};
