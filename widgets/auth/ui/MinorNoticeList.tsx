import React from 'react';

export const MinorNoticeList = () => {
  return (
    <div id="minor-notice-description" className="space-y-4 text-sm text-gray-900">
      <h5 className="text-base font-semibold">꼭 알아두세요.</h5>
      <ol className="list-outside list-disc space-y-4 pl-4 text-base">
        <li>
          미성년 고객(만19세 미만인 자)은 법정대리인(보호자)의 오프라인 동의를 받은 경우에만 전체
          서비스 이용이 가능하며, 온라인 등의 절차만 진행한 경우 일부 서비스 이용에 제한이 있을 수
          있습니다.
        </li>
        <li>
          오프라인 등의 절차 진행을 위하여 아래의 필수 서류를 지침 후 내방 해주세요.
          <p className="mt-4 text-sm font-semibold">필수 서류</p>
          <ol className="mt-2 list-inside list-decimal text-sm">
            <li>법정대리인 동의서(내방 시 작성)</li>
            <li>
              법정대리인을 증빙할 수 있는 증빙 서류(주민등록등본, 가족관계증명서, 기본증명서 등)
            </li>
            <li>법정대리인 신분증</li>
          </ol>
        </li>
      </ol>
    </div>
  );
};
