'use client';

import { motion } from 'framer-motion';
import React from 'react';

import { AppleLoginButton } from '@/features/auth/ui/AppleLoginButton';
import { KakaoLoginButton } from '@/features/auth/ui/KakaoLoginButton';
import { MinorNoticeDialog } from '@/features/auth/ui/MinorNoticeDialog';
import { NaverLoginButton } from '@/features/auth/ui/NaverLoginButton';

import { SignType } from '@/entities/auth/types';

import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SignUpTypeProps {
  handleStep: () => void;
}

export const SignUpType = ({ handleStep }: SignUpTypeProps) => {
  const { isVisible, toggleVisibility } = useVisibility();

  const { routerPush } = useWebViewRouter();

  return (
    <motion.div
      className="flex w-full max-w-[640px] flex-col items-center rounded-[20px] border-gray-300 pb-20 sm:h-auto sm:border sm:pb-0"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="w-full px-6 sm:px-20 sm:pt-20">
        <div className="flex flex-col space-y-[6px] text-center">
          <h3 className="text-24 sm:text-32">회원가입</h3>
          <h5 className="text-lg">회원가입 유형을 선택해주세요.</h5>
        </div>

        <div className="mt-10 flex h-[180px] w-full rounded-lg border border-gray-300">
          <button className="flex-1" onClick={handleStep}>
            <div className="flex h-full flex-col items-center justify-center">
              <FallbackImage src="/images/personal.png" alt="user" width={70} height={70} />
              <h4 className="mt-4 text-lg font-semibold">개인회원</h4>
            </div>
          </button>
          <button
            onClick={() => routerPush('/sign-up/corporate')}
            className="flex-1 border-l border-gray-300"
          >
            <div className="flex h-full flex-col items-center justify-center">
              <FallbackImage src="/images/business.png" alt="user" width={70} height={70} />
              <h4 className="mt-4 text-lg font-semibold">법인회원</h4>
            </div>
          </button>
        </div>
        <div className="my-6 flex items-center gap-2 sm:my-10">
          <Separator className="flex-1 bg-gray-150" />
          <p className="text-xs text-[#9D9995]">또는</p>
          <Separator className="flex-1 bg-gray-150" />
        </div>
        <div className="mt-6 space-y-3 sm:mt-8">
          <AppleLoginButton type={SignType.SIGNUP} text="Apple로 등록" />
          <KakaoLoginButton type={SignType.SIGNUP} text="카카오로 등록" />
          <NaverLoginButton type={SignType.SIGNUP} text="네이버로 등록" />
        </div>
      </div>
      <div className="mt-20 w-full space-y-4 rounded-b-[20px] bg-gray-50 p-10">
        <div className="flex items-center gap-3">
          <h5 className="text-sm font-semibold">회원가입 유형 안내</h5>{' '}
          <button
            onClick={toggleVisibility}
            className="text-xs font-semibold text-gray-500 underline"
          >
            *미성년자 회원이신가요?
          </button>
        </div>
        <ol className="list-outside list-disc space-y-2 pl-4 text-sm leading-[170%] text-gray-600">
          <li>개인회원은 개인 자격으로 뉴밋 서비스를 이용할 수 있는 투자자 유형입니다.</li>
          <li>법인회원은 법인 자격으로 뉴밋 서비스를 이용할 수 있는 투자자 유형입니다.</li>
          <li>소셜 로그인 이용 시 개인회원으로 가입되며, 법인회원은 이용하실 수 없습니다.</li>
        </ol>
      </div>
      <MinorNoticeDialog isOpen={isVisible} handleOpen={toggleVisibility} />
    </motion.div>
  );
};
