'use client';

import React from 'react';

import { useFindEmail } from '@/features/auth/model/useFindEmail';
import { FindEmailComplete } from '@/features/auth/ui/FindEmailComplete';
import { FindEmailForm } from '@/features/auth/ui/FindEmailForm';

import { FindUserSteps } from '@/entities/auth/types';

import { SecondaryButton } from '@/shared/ui/SecondaryButton';

interface FindEmailProps {
  step: FindUserSteps;

  isStep: (step: FindUserSteps) => boolean;
  handleStep: (nextStep: FindUserSteps) => void;
}

export const FindEmail = ({ step, handleStep, isStep }: FindEmailProps) => {
  const { form, onSubmit, findedEmail } = useFindEmail();

  const submitFindEmail = async () => {
    const success = await onSubmit(form.getValues());
    if (success) {
      handleStep(FindUserSteps.COMPLETE);
    } else {
      form.reset();
    }
  };

  const handleBack = () => {
    if (window.confirm('작성 중인 내용이 모두 초기화됩니다.\n이전 단계로 돌아가시겠습니까?')) {
      form.reset();
      handleStep(FindUserSteps.FORM);
    }
  };

  return (
    <div className={`${!isStep(FindUserSteps.FORM) ? 'mt-0 sm:mt-16' : 'mt-10 sm:mt-16'}`}>
      {isStep(FindUserSteps.FORM) && <FindEmailForm form={form} onSubmit={submitFindEmail} />}
      {isStep(FindUserSteps.COMPLETE) && findedEmail && (
        <FindEmailComplete email={findedEmail} handleBack={handleBack} />
      )}
    </div>
  );
};
