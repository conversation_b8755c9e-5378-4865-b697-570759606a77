'use client';

import { fetchBanners } from '@/features/banners/api/fetchBanners';
import { CarouselLeftButton } from '@/features/main/ui/CarouselLeftButton';
import { CarouselRightButton } from '@/features/main/ui/CarouselRightButton';
import { HeroCarousel } from '@/features/main/ui/HeroCarousel';

import { Banner } from '@/entities/banners/types';

import { ListResponse } from '@/shared/interface';
import { useCarousel } from '@/shared/model/useCarousel';

interface HeroBannerProps {
  banners: ListResponse<Banner>;
}

export const HeroBanner = ({ banners }: HeroBannerProps) => {
  const { setSwiper, handlePrev, handleNext, handleSlideChange, swiper, currentIndex } =
    useCarousel();

  const { data: bannerList } = fetchBanners({ type: 'main_top' }, banners);

  const bannerItems = bannerList?.data[0]?.banner_items || [];

  return (
    <section className="relative px-5 sm:px-8 ml:px-10">
      <div className="relative mx-auto max-w-[1840px]">
        <CarouselLeftButton
          aria-label="previous banner"
          handlePrev={handlePrev}
          className="absolute left-6 top-1/2 z-20 hidden -translate-y-1/2 items-center justify-center sm:flex ml:left-10"
        />
        <CarouselRightButton
          aria-label="next banner"
          handleNext={handleNext}
          className="absolute right-6 top-1/2 z-20 hidden -translate-y-1/2 items-center justify-center sm:flex ml:right-10"
        />
        <HeroCarousel
          swiper={swiper}
          currentIndex={currentIndex}
          handleSlideChange={handleSlideChange}
          setSwiper={setSwiper}
          banners={bannerItems}
        />
      </div>
    </section>
  );
};
