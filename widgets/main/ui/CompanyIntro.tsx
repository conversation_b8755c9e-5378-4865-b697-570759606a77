import React from 'react';

import { FallbackImage } from '@/shared/ui/FallbackImage';

export const CompanyIntro = () => {
  return (
    <>
      <section className="container hidden pb-[130px] pt-[100px] sm:block sm:pb-[140px] sm:pt-[120px] ml:pb-40 ml:pt-40">
        <div className="flex flex-col ml:flex-row ml:space-x-[122px]">
          <div className="hidden ml:block">
            <FallbackImage
              src="/images/main_service2.png"
              alt="company_intro"
              width={488}
              height={610}
              sizes="(max-width: 744px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="w-[488px]] h-[610px] rounded-[30px] border-none"
              unoptimized
            />
          </div>
          <div className="flex flex-col items-center gap-8 sm:gap-12 ml:justify-center ml:gap-[72px]">
            <div className="flex w-full flex-col items-center gap-[2px] sm:gap-3 ml:gap-5">
              <h1 className="text-24 sm:text-32 ml:text-44">미래를 여는 투자 플랫폼</h1>
              <FallbackImage
                src="/logo/numit_lg_logo.png"
                alt="numit_logo"
                width={155}
                height={37}
                className="sm:h-[56px] sm:w-[230px] ml:h-[73px] ml:w-[300px]"
                unoptimized
              />
            </div>
            <div className="flex w-full flex-col gap-8 sm:gap-12">
              <div className="relative aspect-[327/116] sm:aspect-[680/240] ml:hidden">
                <FallbackImage
                  src="/images/main_service.png"
                  alt="company_intro"
                  fill
                  sizes="(max-width: 744px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  unoptimized
                />
              </div>
              <div className="space-y-2 text-center font-semibold sm:space-y-4 sm:text-xl ml:text-[26px] ml:leading-[40px]">
                <p>
                  조각투자로 다양한 자산이 가진 무한한 <br /> 기회를 경험해보세요.
                </p>
                <p>
                  뉴밋에서는 지속 가능한 자산에 안전하게 <br /> 투자할 수 있습니다.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="relative flex h-[440px] items-end sm:hidden">
        <div className="absolute left-9 top-10 z-30 space-y-[26px]">
          <div className="space-y-1">
            <p className="text-20">미래를 여는 투자 플랫폼</p>
            <FallbackImage
              src="/logo/numit_lg_logo.png"
              alt="numit_logo"
              width={116}
              height={29}
              className="h-[29px] w-[116px]"
              unoptimized
            />
          </div>
          <div className="w-2/3 space-y-2 break-keep text-sm font-semibold leading-[150%]">
            <p>조각투자로 다양한 자산이 가진 무한한 기회를 경험해보세요.</p>
            <p>뉴밋에서는 지속 가능한 자산에 안전하게 투자할 수 있습니다.</p>
          </div>
        </div>
        <div className="relative mt-auto h-[260px] w-full bg-[url('/images/main_service_mobile.png')] bg-cover bg-center">
          <div className="absolute inset-0 bg-gradient-to-br from-white/95 to-transparent" />
        </div>
      </section>
    </>
  );
};
