import { ArrowUpRightIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { Button } from '@/shared/ui/shadcn/button';

export const MobileSubscriptionPromo = () => {
  const { routerPush } = useWebViewRouter();

  return (
    <section className="flex flex-col items-center justify-center space-y-6 bg-lime-500 py-10 sm:hidden">
      <div className="space-y-[6px] text-center">
        <h2 className="text-20">태양광 발전소부터 음악저작권까지</h2>
        <h4 className="text-sm font-semibold">
          일상의 모든것이 투자로 이어집니다. <br /> 뉴밋에서 그 문을 열어보세요.
        </h4>
      </div>
      <div>
        <Button
          onClick={() => routerPush('/subscriptions')}
          className="h-10 w-[120px] bg-gray-900 text-xs font-semibold leading-[150%] text-white"
        >
          투자하러가기
          <ArrowUpRightIcon className="!h-3 !w-3" strokeWidth={2} />
        </Button>
      </div>
    </section>
  );
};
