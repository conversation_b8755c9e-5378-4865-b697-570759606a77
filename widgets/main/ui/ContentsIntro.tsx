'use client';

import { ChevronRightIcon } from '@heroicons/react/24/solid';
import { useRouter } from 'next/navigation';
import 'swiper/css';

import { fetchCurations } from '@/features/curations/api/fetchCurations';

import { Curation } from '@/entities/curations/types';
import { CurationMainCard } from '@/entities/curations/ui/CurationMainCard';

import { ListResponse } from '@/shared/interface';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';
import { FallbackImage } from '@/shared/ui/FallbackImage';

export const ContentsIntro = ({ curations }: { curations: ListResponse<Curation> }) => {
  const { data: curationList } = fetchCurations({ perPage: 6 }, curations);

  const router = useRouter();
  const { screenSize } = useScreenStore();

  const goToCuration = () => {
    if (screenSize === ScreenSize.MOBILE) {
      router.replace('/contents/curations');
    } else {
      router.push('/contents/curations');
    }
  };

  return (
    <>
      <section className="w-full bg-blue-gray-50/50 px-5 pt-10 sm:px-8 sm:pt-[60px] ml:px-10 ml:pt-[120px]">
        <div className="mx-auto max-w-[1200px]">
          <div className="relative flex flex-col pb-[60px] sm:pb-0">
            <div className="flex items-center justify-between">
              <h2 className="text-2- sm:text-28 ml:text-32">최신 콘텐츠</h2>
              <div className="flex items-center font-semibold text-gray-500">
                <button onClick={goToCuration} className="flex items-center text-sm sm:text-base">
                  전체보기
                  <ChevronRightIcon className="h-4 w-4" strokeWidth={2} />
                </button>
              </div>
            </div>
            <div className="mt-6 grid grid-cols-1 gap-y-10 sm:mt-[18px] sm:grid-cols-2 sm:gap-x-5 sm:gap-y-[100px] ml:mt-12 ml:grid-cols-3">
              {curationList?.data.map((curation) => (
                <CurationMainCard key={curation.id} {...curation} />
              ))}
            </div>
          </div>
        </div>
      </section>
      <div className="relative hidden h-[85px] sm:block sm:h-[70px] ml:h-[180px]">
        <FallbackImage
          src="/images/main_contents_roundbg.png"
          alt="main_contents_roundbg"
          fill
          sizes="100vw"
        />
      </div>
    </>
  );
};
