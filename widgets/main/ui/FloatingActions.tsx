'use client';

import { BellIcon } from '@heroicons/react/24/outline';

import { NewsletterFormDialog } from '@/features/main/ui/NewsletterFormDialog';
import { ScrollUpButton } from '@/features/main/ui/ScrollUpButton';

import { Button } from '@/shared/ui/shadcn/button';

import { useFloatingActions } from '../model/useFloatingActions';

export const FloatingActions = () => {
  const { isVisible, isDialogOpen, isHover, handleDialog, handleHover, openDialog } =
    useFloatingActions();

  return (
    <>
      <div
        className={`fixed bottom-5 right-5 z-20 flex-col items-end gap-3 sm:bottom-10 sm:right-10 sm:flex ${isVisible ? 'flex' : 'hidden'}`}
      >
        <ScrollUpButton />
        {isHover ? (
          <Button
            aria-label="open newsletter dialog"
            className="h-[46px] rounded-full bg-primary-500 text-xl text-white active:bg-primary-600 ml:h-[60px]"
            onClick={openDialog}
            onMouseLeave={handleHover}
          >
            <BellIcon style={{ width: '20px', height: '20px' }} />
            소식 받기
          </Button>
        ) : (
          <>
            <Button
              aria-label="open newsletter dialog"
              onMouseEnter={handleHover}
              className="hidden !h-[60px] w-[60px] rounded-full bg-gray-900 text-white ml:flex"
            >
              <BellIcon style={{ width: '20px', height: '20px' }} />
            </Button>
            <Button
              aria-label="open newsletter dialog"
              onClick={openDialog}
              className="h-[46px] w-[46px] rounded-full bg-gray-900 text-white ml:hidden"
            >
              <BellIcon style={{ width: '20px', height: '20px' }} />
            </Button>
          </>
        )}
      </div>
      <NewsletterFormDialog isOpen={isDialogOpen} handleOpen={handleDialog} />
    </>
  );
};
