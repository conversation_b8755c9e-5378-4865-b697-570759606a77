'use client';

import { FallbackImage } from '@/shared/ui/FallbackImage';

const list = [
  '/images/slide_01.png',
  '/images/slide_02.png',
  '/images/slide_03.png',
  '/images/slide_04.png',
  '/images/slide_05.png',
  '/images/slide_06.png',
  '/images/slide_01.png',
  '/images/slide_02.png',
  '/images/slide_03.png',
  '/images/slide_04.png',
  '/images/slide_05.png',
  '/images/slide_06.png',
];

export const SlideContents = () => {
  return (
    <section className="hidden min-h-[664px] bg-gray-50 px-5 pb-40 sm:block sm:px-8 ml:h-[920px] ml:px-10 ml:pb-0">
      <div className="blurred-bottom flex aspect-[5/3] gap-5 ml:hidden">
        <div className="mobile-marquee">
          <div className="mobile-marquee-left">
            {list?.map((el, index) => (
              <div key={index} className="">
                <FallbackImage
                  src={el}
                  alt="slide"
                  width={387}
                  height={268}
                  style={{ width: '100%', height: '100%' }}
                  unoptimized
                />
              </div>
            ))}
          </div>
        </div>
        <div className="mobile-marquee">
          <div className="mobile-marquee-right">
            {list?.map((el, index) => (
              <div key={index} className="">
                <FallbackImage
                  src={el}
                  alt="slide"
                  width={387}
                  height={268}
                  style={{ width: '100%', height: '100%' }}
                  unoptimized
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="container flex items-center justify-between gap-[120px] pt-12 sm:pt-16 ml:h-full ml:pt-0">
        <div className="flex flex-1 flex-col gap-6 text-center sm:gap-8 ml:gap-12 ml:text-left">
          <div className="space-y-2">
            <h2 className="ml:text-60 break-keep text-title-1 font-bold sm:text-display-4">
              태양광 발전소<span className="font-normal">부터</span>
              <br />
              음악저작권
              <span className="font-normal">까지</span>
            </h2>
          </div>
          <p className="text-base !font-semibold sm:text-title-3 ml:text-[26px] ml:leading-[40px]">
            일상의 모든 것이 투자로 이어집니다. <br /> 뉴밋에서 그 문을 열어보세요.
          </p>
        </div>
        <div className="marquee hidden ml:block">
          <div className="marquee-content">
            {list?.map((el, index) => (
              <div key={index} className="">
                <FallbackImage src={el} alt="slide" width={387} height={268} unoptimized />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
