'use client';

import { useEffect, useState } from 'react';

import { useVisibility } from '@/shared/model/useVisibility';

export const useFloatingActions = () => {
  const [isVisible, setIsVisible] = useState(false);

  const { isVisible: isDialogOpen, toggleVisibility: handleDialogOpen } = useVisibility();

  const {
    isVisible: isHover,
    toggleVisibility: handleHover,
    openToggle: handleHoverOpen,
  } = useVisibility();

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleScroll = () => {
      const isTop = window.scrollY === 0;
      setIsVisible(!isTop);

      if (window.scrollY >= 2800 && !localStorage.getItem('event_popup')) {
        handleHover();
        handleDialogOpen();

        localStorage.setItem('event_popup', 'true');
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleDialog = () => {
    if (isHover) handleHover();
    handleDialogOpen();
  };

  const openDialog = () => {
    handleHoverOpen();
    handleDialogOpen();
  };

  return { isVisible, isDialogOpen, isHover, handleDialog, handleHover, openDialog };
};
