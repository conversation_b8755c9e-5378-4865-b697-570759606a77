'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';

import { fetchInfiniteCurations } from '@/features/curations/api/fetchInfiniteCurations';

import { CurationCategory } from '@/entities/curations/types';

export const useCurationList = () => {
  const searchParams = useSearchParams();
  const category = searchParams.get('category');
  const keyword = searchParams.get('keyword');
  const {
    data: curationList,
    fetchNextPage,
    hasNextPage,
  } = fetchInfiniteCurations({
    perPage: 10,
    category: category as CurationCategory,
    globalSearch: keyword ?? undefined,
  });

  const [ref, inView] = useInView({
    delay: 100,
    threshold: 0.5,
  });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage]);

  return { curationList, ref, hasNextPage };
};
