'use client';

import { CurationCard } from '@/entities/curations/ui/CurationCard';

import { EmptyList } from '@/shared/ui/EmptyList';

import { useCurationList } from '../model/useCurationList';

export const CurationList = () => {
  const { curationList, ref, hasNextPage } = useCurationList();

  return (
    <div>
      {curationList?.pages[0]?.totalCount > 0 ? (
        curationList?.pages.map((page, pageIndex) =>
          page.data?.map((curation, index) => (
            <CurationCard
              key={curation.id}
              curation={curation}
              ref={ref}
              isLastPage={!(hasNextPage && index === page.data.length - 5)}
              isLastItem={
                pageIndex === curationList.pages.length - 1 && index === page.data.length - 1
              }
            />
          )),
        )
      ) : (
        <EmptyList
          title="검색 결과가 없습니다."
          buttonText="돌아가기"
          className="mb-60 sm:mt-[200px]"
        />
      )}
    </div>
  );
};
