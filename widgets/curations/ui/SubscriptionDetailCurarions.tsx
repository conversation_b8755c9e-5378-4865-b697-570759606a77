import React from 'react';

import { fetchCurations } from '@/features/curations/api/fetchCurations';

import { CurationMainCard } from '@/entities/curations/ui/CurationMainCard';

export const SubscriptionDetailCurarions = () => {
  const { data: curationsData } = fetchCurations({
    page: 1,
    perPage: 3,
  });

  return (
    <div className="bg-blue-gray-00">
      <div className="mx-auto max-w-[840px] space-y-4 px-6 pb-20 pt-8 sm:space-y-14 sm:px-8 sm:pb-[180px] sm:pt-[100px] ml:max-w-screen-contents ml:space-y-4 ml:px-0">
        <h3 className="text-18 sm:text-20">투자 콘텐츠</h3>
        <div className="grid gap-5 sm:grid-cols-3">
          {curationsData?.data.map((curation) => (
            <CurationMainCard key={curation.id} {...curation} />
          ))}
        </div>
      </div>
    </div>
  );
};
