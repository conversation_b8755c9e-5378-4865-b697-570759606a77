import React from 'react';

import { Curation } from '@/entities/curations/types';
import { CurationRecommendCard } from '@/entities/curations/ui/CurationRecommendCard';

interface DetailRecommendContentsProps {
  curationDetail: Curation;
}

export const DetailRecommendContents = ({ curationDetail }: DetailRecommendContentsProps) => {
  const { relatedContents } = curationDetail;

  return (
    <section className="mx-auto mt-[140px] max-w-screen-lg space-y-[56px]">
      <h4 className="text-28 ml:text-32 text-center">추천 콘텐츠</h4>
      <div className="grid grid-cols-1 gap-12 sm:grid-cols-2 sm:gap-5 ml:grid-cols-3 ml:gap-12">
        {relatedContents?.map((curation, index) => (
          <CurationRecommendCard key={`${curation.id}-${index}`} curation={curation} />
        ))}
      </div>
    </section>
  );
};
