import React from 'react';

import { Curation } from '@/entities/curations/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { LinkCopyButton } from '@/shared/ui/LinkCopyButton';

interface CurationDetailHeaderProps {
  curationDetail: Curation;
}

export const CurationDetailHeader = ({ curationDetail }: CurationDetailHeaderProps) => {
  const { YYYYMMDD } = utilFormats();

  const { title, createdAt, id } = curationDetail;

  return (
    <div className="flex flex-col gap-12">
      <h1 className="ml:text-44 text-24 sm:text-40 line-clamp-3 text-center font-bold">{title}</h1>
      <div className="flex flex-col items-center gap-7 sm:gap-8">
        <p className="text-sm sm:text-base">{YYYYMMDD(createdAt)}</p>
        <LinkCopyButton url={`/contents/curations/${id}`} />
      </div>
    </div>
  );
};
