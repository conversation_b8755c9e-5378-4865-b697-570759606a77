import React from 'react';

import { Separator } from '@/shared/ui/shadcn/separator';

export const CurationListSkeleton = () => {
  return (
    <div>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index}>
          {/* 모바일 스켈레톤 */}
          <div className="flex flex-col sm:hidden">
            <div className="aspect-[327/210] w-full animate-pulse rounded-xl bg-gray-100" />
            <p className="mt-5 h-4 w-20 animate-pulse bg-gray-100" />
            <p className="mt-[6px] h-5 w-1/2 animate-pulse bg-gray-100" />
            <p className="mt-2 h-4 w-3/4 animate-pulse bg-gray-100" />
            <div className="mt-4 flex gap-[6px]">
              <div className="h-6 w-16 animate-pulse rounded-full bg-gray-100" />
              <div className="h-6 w-16 animate-pulse rounded-full bg-gray-100" />
            </div>
          </div>

          {/* 태블릿 스켈레톤 */}
          <div className="hidden sm:flex sm:flex-row sm:gap-5 sm:py-10 ml:gap-9">
            <div className="relative animate-pulse rounded-xl bg-gray-100 sm:h-[212px] sm:w-[336px] ml:h-[248px] ml:w-[396px]" />
            <div className="flex-1">
              <div className="h-4 w-20 animate-pulse bg-gray-100 sm:text-sm" />
              <div className="mt-2 h-6 w-3/4 animate-pulse bg-gray-100 ml:mt-6" />
              <div className="mt-3 h-[48px] w-full animate-pulse bg-gray-100 ml:mt-4" />
              <div className="mt-6 flex gap-[6px] ml:mt-12">
                <div className="h-6 w-16 animate-pulse rounded-full bg-gray-100" />
                <div className="h-6 w-16 animate-pulse rounded-full bg-gray-100" />
              </div>
            </div>
          </div>

          {index < 2 && <Separator className="my-10 bg-gray-300 sm:my-0" />}
        </div>
      ))}
    </div>
  );
};
