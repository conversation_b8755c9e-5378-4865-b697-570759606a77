import React from 'react';

import { Curation, CurationMediaType } from '@/entities/curations/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface CurationDetailContentsProps {
  curationDetail: Curation;
}

export const CurationDetailContents = ({ curationDetail }: CurationDetailContentsProps) => {
  const { content, thumbnail, mediaType } = curationDetail;

  return (
    <div className="space-y-[72px]">
      {mediaType !== CurationMediaType.cardnews && (
        <div className="relative aspect-[1200/771]">
          <FallbackImage
            src={thumbnail.url}
            alt="contents"
            fill
            priority
            sizes="(max-width: 996px) 100vw, 1200px"
            className="rounded-[30px]"
          />
        </div>
      )}
      <div dangerouslySetInnerHTML={{ __html: content }} className="ck-content" />
    </div>
  );
};
