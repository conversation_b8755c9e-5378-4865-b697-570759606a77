'use client';

import { useSearchParams } from 'next/navigation';
import React from 'react';

import { fetchEvents } from '@/features/events/api/fetchEvents';

import { EventCard } from '@/entities/events/ui/EventCard';

import { CommonPagination } from '@/shared/ui/CommonPagination';
import { EmptyList } from '@/shared/ui/EmptyList';

export const EventList = () => {
  const searchParams = useSearchParams();
  const page = searchParams.get('page') || 1;
  const perPage = 6;

  const { data } = fetchEvents({ page: Number(page), perPage });

  return (
    <>
      <div className="grid grid-cols-1 gap-y-12 sm:grid-cols-2 sm:gap-x-5 sm:gap-y-16 ml:grid-cols-3 ml:gap-12 ml:gap-x-12 ml:gap-y-20">
        {data?.data.map((event) => <EventCard key={event.id} event={event} />)}
      </div>
      {data?.data.length > 0 ? (
        <div className="my-16 sm:my-[72px] ml:my-[120px]">
          <CommonPagination pageSize={perPage} page={Number(page)} totalCount={data.meta.total} />
        </div>
      ) : (
        <EmptyList
          title="등록된 이벤트가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
    </>
  );
};
