import React from 'react';

export const EventListSkeleton = () => {
  return (
    <div className="grid grid-cols-1 gap-12 sm:grid-cols-2 sm:gap-5 ml:grid-cols-3 ml:gap-12">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index}>
          <div className="aspect-[327/210] animate-pulse rounded-[10px] bg-gray-100 ml:aspect-[368/237]" />
          <div className="mb-[6px] mt-5 h-5 w-40 animate-pulse bg-gray-100" />
          <div className="h-[30px] w-[250px] animate-pulse bg-gray-100" />
        </div>
      ))}
    </div>
  );
};
