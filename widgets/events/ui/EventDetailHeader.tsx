import React from 'react';

import { Event } from '@/entities/events/types';
import { EventDetailStatus } from '@/entities/events/ui/EventDetailStatus';

import { utilFormats } from '@/shared/lib/utilformats';
import { LinkCopyButton } from '@/shared/ui/LinkCopyButton';

export const EventDetailHeader = ({ eventDetail }: { eventDetail: Event }) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div>
      <EventDetailStatus eventStatus={eventDetail.eventStatus} />
      <h2 className="sm:text-40 ml:text-44 text-24 mb-12 text-center font-bold">
        {eventDetail?.title}
      </h2>
      <p className="mb-8 text-center text-gray-700">
        {YYYYMMDD(eventDetail.startDate)} ~ {YYYYMMDD(eventDetail.endDate)}
      </p>
      <div className="flex justify-center">
        <LinkCopyButton />
      </div>
    </div>
  );
};
