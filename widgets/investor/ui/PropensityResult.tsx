import React from 'react';

import { propensityQuestions } from '@/features/investor/config';

import { PropensityAnswer, PropensityTerms } from '@/entities/investor/types';
import { PropensityResultItem } from '@/entities/investor/ui/PropensityResultItem';

import { utilFormats } from '@/shared/lib/utilformats';

interface PropensityResultProps {
  answers: PropensityAnswer;
  terms: PropensityTerms;
}

export const PropensityResult = ({ answers, terms }: PropensityResultProps) => {
  const { YYYYMMDD } = utilFormats();

  const twoYearsLater = new Date(new Date().setFullYear(new Date().getFullYear() + 2));

  const getAnswerValue = (questionNumber: number, isMultiV2 = false): string | string[] => {
    const answer = answers[questionNumber];
    if (!answer) return '';

    const question = propensityQuestions.find((q) => q.questionNumber === questionNumber);
    if (!question) return '';

    if (answer.selectionMode === 'single') {
      const selection = question.selection.find((s) => {
        const key = Object.keys(s)[0];
        return key === answer.selectedKey;
      });
      return selection ? Object.values(selection)[0] : '';
    } else if (answer.selectionMode === 'multi') {
      return answer.selectedKeys
        .map((key) => {
          const selection = question.selection.find((s) => {
            const selectionKey = Object.keys(s)[0];
            return selectionKey === key;
          });
          return selection ? Object.values(selection)[0] : '';
        })
        .join(', ');
    } else if (answer.selectionMode === 'multi_v2') {
      return answer.selected.map((item) => {
        const selection = question.selection.find((s) => {
          const selectionKey = Object.keys(s)[0];
          return selectionKey === item.key;
        });
        return selection ? `${Object.values(selection)[0]}(${item.value})` : '';
      });
    }
    return '';
  };

  return (
    <div className="max-w-screen-test mx-auto mb-9 space-y-8 rounded-[20px] bg-gray-50 p-5 sm:mb-20 sm:p-8">
      <h4 className="font-semibold">진단 정보</h4>
      <div>
        <PropensityResultItem
          label="투자 희망 여부"
          value={terms.isHopeInvestment ? '예' : '아니오'}
        />
        <PropensityResultItem
          label="투자 정보 제공 여부"
          value={terms.isInfoAgree ? '예' : '아니오'}
        />
        <PropensityResultItem
          label="취약 투자자 여부"
          value={terms.isVulnerable ? '예' : '아니오'}
        />
        <PropensityResultItem
          label="등록일/만기일"
          value={`${YYYYMMDD(new Date())} / ${YYYYMMDD(twoYearsLater)}`}
        />
        <PropensityResultItem label="연령대" value={getAnswerValue(1) as string} />
        <PropensityResultItem label="투연소득" value={getAnswerValue(2) as string} />
        <PropensityResultItem label="금융자산 비중" value={getAnswerValue(3) as string} />
        <PropensityResultItem label="투자 비중" values={getAnswerValue(4) as string[]} />
        <PropensityResultItem label="투자 경험" value={getAnswerValue(5) as string} />
        <PropensityResultItem
          label="취득 및 처분 목적"
          value={getAnswerValue(6) as string}
          isLast
        />
      </div>
    </div>
  );
};
