import React from 'react';

import { PropensitySteps } from '@/entities/investor/types';

import { Progress } from '@/shared/ui/shadcn/progress';

interface InvestorProgressProps {
  isStep: (step: PropensitySteps) => boolean;
  currentQuestion: number;
}

export const InvestorProgress = ({ isStep, currentQuestion }: InvestorProgressProps) => {
  const isFirstStep = isStep(PropensitySteps.TYPE);
  const isSecondStep = isStep(PropensitySteps.TERMS);

  if (isFirstStep) {
    return (
      <h5 className="text-sm sm:text-lg">
        <p className="mb-3 text-lg font-bold sm:mb-0">
          투자자 보호를 위해 투자 성향 진단 후 청약 서비스 이용이 가능합니다.
        </p>
        투자자 정보 확인서는 <strong>금융소비자 보호법에 관한 법률</strong>에 따라 투자 성향을
        파악하고 그에 적합한 투자권유를 위한 자료로 활용합니다. <br />
        따라서 최대한 고객님의 상황에 맞거나 가장 가까운 항목을 선택해 주세요.
      </h5>
    );
  }

  if (isSecondStep) {
    return (
      <h5 className="text-lg font-bold">
        고객님의 투자 성향 진단을 시작합니다. <br />
        아래의 질문에 답변해 주세요.
      </h5>
    );
  }

  return (
    <div className="space-y-2">
      <Progress value={(currentQuestion + 1) * 10} className="bg-blue-gray-50 !text-primary-500" />
      <div className="text-sm font-semibold text-primary-500">
        <span>
          STEP {''}
          {currentQuestion + 1 < 10 ? `0${currentQuestion + 1}` : currentQuestion + 1}
        </span>
        <span className="font-semibold text-gray-400"> / 10</span>
      </div>
    </div>
  );
};
