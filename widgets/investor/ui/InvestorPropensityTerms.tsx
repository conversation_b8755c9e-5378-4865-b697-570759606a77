import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { PropensityTermsItem } from '@/features/investor/ui/PropensityTermsItem';

import { PropensityTerms } from '@/entities/investor/types';

interface InvestorPropensityTermsProps {
  terms: PropensityTerms;
  onTermsChange: (key: keyof PropensityTerms, value: boolean) => void;
}

export const InvestorPropensityTerms = ({ terms, onTermsChange }: InvestorPropensityTermsProps) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="max-w-screen-test mx-auto my-6 space-y-2 px-6 sm:my-20 sm:space-y-4 sm:px-8 ml:px-0"
      >
        <PropensityTermsItem
          isAgree={terms.isHopeInvestment}
          onAgree={(isAgree) => onTermsChange('isHopeInvestment', isAgree)}
          title="뉴밋 투자계약증권 투자를 희망하십니까?"
          description="투자를 희망하지 않으실 경우, 뉴밋 투자계약증권 거래가 불가능합니다."
        />
        <PropensityTermsItem
          isAgree={terms.isInfoAgree}
          onAgree={(isAgree) => onTermsChange('isInfoAgree', isAgree)}
          title="고객님의 투자자 정보 제공에 동의하십니까?"
          description="투자자 정보 제공에 동의하지 않으실 경우, 뉴밋 투자계약증권 거래가 불가능합니다."
        />
        <PropensityTermsItem
          isAgree={terms.isVulnerable}
          onAgree={(isAgree) => onTermsChange('isVulnerable', isAgree)}
          title="고객님께서는 취약 투자자에 해당하시나요?"
          description="취약 투자자란 고령 투자자/미성년자/금융상품 투자경험이 없는 분들이 대상이며, 금융상품 투자 시 불이익에 대한 정보를 다른 정보보다 우선하여 설명 받으실 수 있습니다."
        />
      </motion.div>
    </AnimatePresence>
  );
};
