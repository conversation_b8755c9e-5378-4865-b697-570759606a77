import React from 'react';

import { ratioOptions } from '@/features/investor/config';
import { PropensitySelect } from '@/features/investor/ui/PropensitySelect';

interface PropensityAssetTypeProps {
  value: string;
  handleAssetTypeRatio: (key: string) => void;
  title: string;
  description: string;
  options: string[];
  id: string;
}

export const PropensityAssetType = ({
  value,
  handleAssetTypeRatio,
  title,
  description,
  id,
  options,
}: PropensityAssetTypeProps) => {
  return (
    <div className="flex items-center justify-between rounded-lg border border-gray-300 bg-gray-50 px-5 py-4 sm:p-8">
      <div className="flex flex-col gap-1 sm:gap-2">
        <h4 className="text-sm font-bold sm:text-lg">{title}</h4>
        <h6 className="text-xs">{description}</h6>
      </div>
      <PropensitySelect value={value} onChange={handleAssetTypeRatio} options={options} id={id} />
    </div>
  );
};
