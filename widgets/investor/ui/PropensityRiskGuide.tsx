import React from 'react';

export const PropensityRiskGuide = () => {
  return (
    <div className="space-y-6">
      <h4 className="text-lg font-semibold">투자자 성향별 적합한 투자성 상품</h4>

      <div className="overflow-hidden rounded-lg border border-gray-300">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 text-sm font-semibold">
              <th className="w-1/2 border-r border-gray-300 px-6 py-4 text-center">투자자 성향</th>
              <th className="w-1/2 px-6 py-4 text-center">금융상품 위험등급</th>
            </tr>
          </thead>
          <tbody>
            <tr className="text-sm">
              <td className="w-1/2 border-r border-t border-gray-300 px-6 py-4 text-center font-semibold">
                공격투자형
              </td>
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">매우높은위험</td>
            </tr>
            <tr className="text-sm">
              <td
                className="w-1/2 border-r border-t border-gray-300 px-6 py-4 text-center font-semibold"
                rowSpan={2}
              >
                적극투자형
              </td>
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">높은위험</td>
            </tr>
            <tr className="text-sm">
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">다소높은위험</td>
            </tr>
            <tr className="text-sm">
              <td className="w-1/2 border-r border-t border-gray-300 px-6 py-4 text-center font-semibold">
                위험중립형
              </td>
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">보통위험</td>
            </tr>
            <tr className="text-sm">
              <td className="w-1/2 border-r border-t border-gray-300 px-6 py-4 text-center font-semibold">
                안정추구형
              </td>
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">낮은위험</td>
            </tr>
            <tr className="text-sm">
              <td className="w-1/2 border-r border-t border-gray-300 px-6 py-4 text-center font-semibold">
                안정형
              </td>
              <td className="w-1/2 border-t border-gray-300 px-6 py-4 text-center">매우낮은위험</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};
