import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { propensityQuestions } from '@/features/investor/config';

import { PropensityAnswer } from '@/entities/investor/types';

import { PropensityMulitRatio } from './PropensityMulitRatio';
import { PropensityMultiSelect } from './PropensityMultiSelect';
import { PropensitySingleSelect } from './PropensitySingleSelect';

interface PropensityQuestionProps {
  currentQuestion: number;
  answers: PropensityAnswer;
  handleSingleAnswer: (questionNumber: number, key: string, value: string, score: number) => void;
  handleMultiAnswer: (questionNumber: number, key: string, isSelected: boolean) => void;
  handleMultiRatioAnswer: (questionNumber: number, key: string, value: string) => void;
}

export const PropensityQuestion = ({
  currentQuestion,
  answers,
  handleSingleAnswer,
  handleMultiAnswer,
  handleMultiRatioAnswer,
}: PropensityQuestionProps) => {
  const type = propensityQuestions[currentQuestion].selectionMode as
    | 'single'
    | 'multi_v2'
    | 'multi';

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="max-w-screen-test mx-auto my-6 space-y-6 px-6 sm:my-20 sm:space-y-10 sm:px-8 ml:px-0"
      >
        <div className="space-y-2">
          <h3 className="text-18 sm:text-28">
            {propensityQuestions[currentQuestion].questionText}
          </h3>
          {type === 'multi' && <h5 className="text-gray-600">중복 선택 가능</h5>}
          {type === 'multi_v2' && (
            <h5 className="text-xs text-gray-600 sm:hidden">
              각 유형별 총 합계를 100%로 선택해 주세요.
            </h5>
          )}
        </div>
        {type === 'single' && (
          <PropensitySingleSelect
            currentIndex={currentQuestion}
            answers={answers}
            handleSingleAnswer={handleSingleAnswer}
          />
        )}
        {type === 'multi' && (
          <PropensityMultiSelect
            currentIndex={currentQuestion}
            answers={answers}
            handleMultiAnswer={handleMultiAnswer}
          />
        )}
        {type === 'multi_v2' && (
          <PropensityMulitRatio
            currentIndex={currentQuestion}
            answers={answers}
            handleMultiRatioAnswer={handleMultiRatioAnswer}
          />
        )}
      </motion.div>
    </AnimatePresence>
  );
};
