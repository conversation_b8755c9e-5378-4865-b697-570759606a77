import { InformationCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { propensityQuestions } from '@/features/investor/config';

import { MultiRatioAnswer, PropensityAnswer } from '@/entities/investor/types';

import { PropensityAssetType } from './PropensityAssetType';

type MultiRatioSelection = {
  [key: string]: string | string[];
  selection: string[];
};

interface PropensityMulitRatioProps {
  currentIndex: number;
  answers: PropensityAnswer;
  handleMultiRatioAnswer: (questionNumber: number, key: string, value: string) => void;
}

export const PropensityMulitRatio = ({
  currentIndex,
  answers,
  handleMultiRatioAnswer,
}: PropensityMulitRatioProps) => {
  const multi = Object.values(answers).find(
    (answer) => answer.selectionMode === 'multi_v2',
  ) as MultiRatioAnswer;
  const total = multi?.selected.reduce((acc: number, curr: { key: string; value: string }) => {
    const value = curr.value.replace('%', '');
    return acc + Number(value);
  }, 0);

  return (
    <div className="space-y-5">
      <div className="flex flex-col items-center gap-5 rounded-lg border border-gray-300 py-3 sm:p-8">
        <div className="bg-primary-00 hidden items-center gap-1 rounded-lg px-4 py-[10px] text-primary-500 sm:flex">
          <InformationCircleIcon className="h-4 w-4 text-primary-500" />
          <p className="rounded-lg">
            각 유형별 총 합계를 <strong>100%</strong>로 선택해주세요.
          </p>
        </div>
        <p className="flex items-center gap-3 text-sm font-semibold text-gray-500 sm:text-lg">
          현재 총 합계{' '}
          <strong className="sm:text-24 text-sm font-semibold text-primary-500 sm:text-gray-900">
            {total ?? 0}%
          </strong>
        </p>
      </div>
      <div className="space-y-2 sm:space-y-4">
        {propensityQuestions[currentIndex].selection.map((selection, index) => {
          const multiRatioSelection = selection as unknown as MultiRatioSelection;
          return (
            <PropensityAssetType
              key={index}
              id={Object.keys(multiRatioSelection)[0]}
              title={Object.values(multiRatioSelection)[0] as string}
              description={Object.values(multiRatioSelection)[0] as string}
              value={
                (
                  answers[propensityQuestions[currentIndex].questionNumber] as MultiRatioAnswer
                )?.selected.find((item) => item.key === Object.keys(multiRatioSelection)[0])
                  ?.value ?? '0%'
              }
              options={multiRatioSelection.selection}
              handleAssetTypeRatio={(value) =>
                handleMultiRatioAnswer(
                  propensityQuestions[currentIndex].questionNumber,
                  Object.keys(multiRatioSelection)[0],
                  value,
                )
              }
            />
          );
        })}
      </div>
    </div>
  );
};
