import React from 'react';

import { propensityQuestions } from '@/features/investor/config';
import { PropensityCommonButton } from '@/features/investor/ui/PropensityCommonButton';

import { MultiAnswer, PropensityAnswer } from '@/entities/investor/types';

interface PropensityMultiSelectProps {
  currentIndex: number;
  answers: PropensityAnswer;
  handleMultiAnswer: (questionNumber: number, key: string, isSelected: boolean) => void;
}

export const PropensityMultiSelect = ({
  currentIndex,
  answers,
  handleMultiAnswer,
}: PropensityMultiSelectProps) => {
  return (
    <div className="space-y-4">
      {propensityQuestions[currentIndex].selection.map((selection, index) => (
        <PropensityCommonButton
          key={index}
          text={Object.values(selection)[0]}
          isActive={(
            answers[propensityQuestions[currentIndex].questionNumber] as MultiAnswer
          )?.selectedKeys?.includes(Object.keys(selection)[0])}
          onClick={() =>
            handleMultiAnswer(
              propensityQuestions[currentIndex].questionNumber,
              Object.keys(selection)[0],
              true,
            )
          }
        />
      ))}
    </div>
  );
};
