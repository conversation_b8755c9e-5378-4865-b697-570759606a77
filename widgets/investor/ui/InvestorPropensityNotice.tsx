import React from 'react';

interface InvestorPropensityNoticeProps {
  className?: string;
}

export const InvestorPropensityNotice = ({ className }: InvestorPropensityNoticeProps) => {
  return (
    <div className={`rounded-lg bg-gray-50 px-6 py-8 sm:mb-40 sm:p-8 ${className}`}>
      <div className="space-y-4">
        <h5 className="text-sm font-semibold">꼭 알아두세요.</h5>
        <ul className="list-disc space-y-1 pl-4 leading-[170%]">
          <li>
            본 확인서는 금융소비자 보호에 관한 법률 제 18조 및 시행령 제12조 제1항에 따라 고객이
            적정성 원칙 대상 상품을 거래하고자 하는 경우에는 반드시 작성하여야만 합니다.
            <br />
            <span className="text-gray-500">
              적정성 원칙(금융소비자보호법 제18조): 소비자가 자발적으로 구매하려는 금융상품이
              소비자의 재산 등에 비추어 부적절한 경우 그 사실을 소비자에게 고지하고 확인할 의무
            </span>
          </li>
          <li>별도의 통지가 없는 경우 투자자 성향 확인서는 등록일로부터 2년간 유지됩니다.</li>
          <li>
            투자권유 희망여부 및 투자자 정보 제공여부는 향후 분쟁 또는 소송이 발생하는 경우 고객님의
            권리구제에 불리하게 사용될 수 있으므로 신중하게 작성할 필요가 있습니다.
          </li>
        </ul>
      </div>
    </div>
  );
};
