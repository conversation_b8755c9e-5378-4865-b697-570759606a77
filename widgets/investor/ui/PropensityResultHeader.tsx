import React from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import {
  PropensityScore,
  PropensityTypeEnum,
  PropensityTypeEnumValues,
} from '@/entities/investor/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface PropensityResultHeaderProps {
  propensityScore: PropensityScore;
}

export const PropensityResultHeader = ({ propensityScore }: PropensityResultHeaderProps) => {
  const { user, isGeneral } = useFetchUser();

  return (
    <div className="mx-auto mb-10 mt-6 flex max-w-screen-test flex-col items-center space-y-14 px-6 sm:mb-20 sm:mt-[100px] sm:px-8 ml:px-0">
      <h3 className="text-28">투자 성향 진단</h3>
      <div className="space-y-6">
        <FallbackImage
          src="/images/propensity_result_example.png"
          alt="투자 성향 진단 결과 예시"
          width={126}
          height={68}
          className="mx-auto h-[68px] w-[126px] sm:h-[150px] sm:w-[250px]"
        />
        <div className="space-y-4">
          <h2 className="text-20 sm:text-32 text-center">
            {isGeneral ? user?.userProfile?.name : user?.userCorporateProfile?.representativeName}
            님의 투자 성향은 <br />{' '}
            <strong className="text-primary-500">
              {PropensityTypeEnumValues[propensityScore.type as PropensityTypeEnum]}
            </strong>
            입니다.
          </h2>
          <h5 className="text-sm sm:text-base">
            투자에 상응하는 투자위험이 있음을 충분히 인식하고 있습니다.
          </h5>
        </div>
      </div>
    </div>
  );
};
