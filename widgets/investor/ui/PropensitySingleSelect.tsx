import React from 'react';

import { propensityQuestions } from '@/features/investor/config';
import { PropensityCommonButton } from '@/features/investor/ui/PropensityCommonButton';

import { PropensityAnswer, SingleAnswer } from '@/entities/investor/types';

interface PropensitySingleSelectProps {
  currentIndex: number;
  answers: PropensityAnswer;
  handleSingleAnswer: (questionNumber: number, key: string, value: string, score: number) => void;
}

export const PropensitySingleSelect = ({
  currentIndex,
  answers,
  handleSingleAnswer,
}: PropensitySingleSelectProps) => {
  return (
    <div className="space-y-2 sm:space-y-4">
      {propensityQuestions[currentIndex].selection.map((selection, index) => (
        <PropensityCommonButton
          key={index}
          text={Object.values(selection)[0]}
          isActive={
            (answers[propensityQuestions[currentIndex].questionNumber] as SingleAnswer)
              ?.selectedKey === Object.keys(selection)[0]
          }
          onClick={() =>
            handleSingleAnswer(
              propensityQuestions[currentIndex].questionNumber,
              Object.keys(selection)[0],
              Object.values(selection)[0],
              Number(propensityQuestions[currentIndex].score[index]),
            )
          }
        />
      ))}
    </div>
  );
};
