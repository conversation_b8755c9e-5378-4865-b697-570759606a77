import React from 'react';

import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

interface TestNoticeProps {
  onStartTest: () => void;
  title: string;
  description: string;
  notices: {
    description: string;
  }[];
  isOnboarding?: boolean;
}

export const TestNotice = ({
  onStartTest,
  title,
  description,
  notices,
  isOnboarding = false,
}: TestNoticeProps) => {
  return (
    <div
      className={`mx-auto mt-6 flex max-w-screen-test flex-col space-y-12 pb-20 sm:mb-40 sm:mt-[100px] sm:h-auto sm:justify-start sm:px-8 sm:pb-0 ml:px-0`}
    >
      <div className={`flex flex-col items-center gap-5 ${isOnboarding ? 'px-0' : 'px-6'}`}>
        <FallbackImage
          src="/images/suitability.png"
          alt="suitability-test-notice"
          width={260}
          height={171}
        />
        <h2 className="text-20 sm:text-40">{title}</h2>
        <h4 className="whitespace-pre-line text-center text-sm sm:text-lg">{description}</h4>
      </div>
      <div className="sm:space-y-20">
        <div className="space-y-4 rounded-lg bg-gray-50 px-6 py-8 sm:p-8">
          <p className="text-sm font-semibold">꼭 알아두세요.</p>
          <ol className="list-outside list-disc space-y-[6px] pl-4 text-sm leading-[170%]">
            {notices.map((notice) => (
              <li key={notice.description}>{notice.description}</li>
            ))}
          </ol>
        </div>
        <div className="fixed bottom-0 left-0 mt-5 flex w-full justify-center bg-white px-6 py-2 sm:static sm:mt-0 sm:py-0">
          <PrimaryButton
            text="테스트 시작하기"
            className="h-12 w-full text-base sm:!h-[60px] sm:w-[180px]"
            onClick={onStartTest}
          />
        </div>
      </div>
    </div>
  );
};
