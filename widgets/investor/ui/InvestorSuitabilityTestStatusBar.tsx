import React from 'react';

import { Progress } from '@/shared/ui/shadcn/progress';

interface InvestorSuitabilityTestStatusBarProps {
  testIndex: number;
  totalQuestions: number;
}

export const InvestorSuitabilityTestStatusBar = ({
  testIndex,
  totalQuestions,
}: InvestorSuitabilityTestStatusBarProps) => {
  const progress = ((testIndex + 1) / totalQuestions) * 100;

  return (
    <div className="mx-auto mt-5 w-full max-w-screen-test space-y-[6px] sm:space-y-2">
      <Progress value={progress} className="bg-blue-gray-50 text-primary-500" />
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-[6px] text-xs font-semibold sm:text-sm">
          <span className="text-primary-500">STEP</span>
          <span className="text-primary-500">{testIndex + 1}</span>
          <span className="text-gray-400"> / {totalQuestions}</span>
        </div>
      </div>
    </div>
  );
};
