import { Disclosure } from '@/entities/disclosures/types';
import { DisclosureMobileCard } from '@/entities/disclosures/ui/DisclosureMobileCard';

import { CommonPagination } from '@/shared/ui/CommonPagination';
import { EmptyList } from '@/shared/ui/EmptyList';

interface DisclosureMobileListProps {
  data?: {
    data: Disclosure[];
    meta: {
      total: number;
    };
  };
  page: number;
}

export const DisclosureMobileList = ({ data, page }: DisclosureMobileListProps) => {
  return (
    <div className="ml:hidden">
      {data?.data && data?.data.length > 0 ? (
        <>
          <p className="mb-6">
            전체 <span className="font-bold text-primary-500"> {data?.meta.total || 0}</span> 건
          </p>
          {data?.data.map((disclosure, index) => (
            <DisclosureMobileCard
              key={disclosure.id}
              disclosure={disclosure}
              isLast={data.data.length - 1 === index}
            />
          ))}
        </>
      ) : (
        <EmptyList
          title="검색 결과가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
      {data?.data && data?.data.length > 0 && (
        <div className="mt-16">
          <CommonPagination
            page={Number(page)}
            pageSize={10}
            totalCount={data?.meta.total || 0}
            scroll={false}
          />
        </div>
      )}
    </div>
  );
};
