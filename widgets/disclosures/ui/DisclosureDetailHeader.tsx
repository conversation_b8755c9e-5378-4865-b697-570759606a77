import { Separator } from '@radix-ui/react-separator';
import React from 'react';

import { Disclosure } from '@/entities/disclosures/types';
import { SecuritiesTypeLabel } from '@/entities/subscriptions/types';

import { utilFormats } from '@/shared/lib/utilformats';

interface DisclosureDetailHeaderProps {
  disclosure: Disclosure;
}

export const DisclosureDetailHeader = ({ disclosure }: DisclosureDetailHeaderProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="space-y-2 md:space-y-6">
      <div className="flex items-center justify-between text-gray-700">
        <div className="flex items-center gap-3 text-sm md:gap-4 md:text-base">
          <p className="font-semibold text-primary-500">공시</p>
          <Separator orientation="vertical" className="h-4 w-[1px] bg-gray-300" />
          <p>{YYYYMMDD(disclosure.createdAt, 'YYYY-MM-DD hh:mm')}</p>
        </div>
        <div className="hidden items-center gap-4 md:flex">
          <p>{SecuritiesTypeLabel[disclosure.securitiesType]}</p>
          <Separator orientation="vertical" className="h-4 w-[1px] bg-gray-300" />
          <p>{disclosure.issuer}</p>
        </div>
      </div>
      <h2 className="text-xl font-semibold md:text-2xl">{disclosure.title}</h2>
      <div className="mt-4 flex items-center gap-3 text-sm md:hidden md:gap-4 md:text-base">
        <p>{SecuritiesTypeLabel[disclosure.securitiesType]}</p>
        <Separator orientation="vertical" className="h-4 w-[1px] bg-gray-300" />
        <p>{disclosure.issuer}</p>
      </div>
    </div>
  );
};
