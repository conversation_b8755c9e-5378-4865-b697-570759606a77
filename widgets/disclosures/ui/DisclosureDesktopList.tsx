import React from 'react';

import { DisclosureColumn } from '@/features/disclosures/ui/DisclosureColumn';

import { Disclosure } from '@/entities/disclosures/types';

import { DataTable } from '@/shared/ui/DataTable';
import { EmptyList } from '@/shared/ui/EmptyList';

interface DisclosureDesktopListProps {
  data?: {
    data: Disclosure[];
    meta: {
      total: number;
    };
  };
}

export const DisclosureDesktopList = ({ data }: DisclosureDesktopListProps) => {
  return (
    <div className="hidden ml:block">
      {data?.data && data?.data.length > 0 ? (
        <DataTable<Disclosure, string>
          columns={DisclosureColumn}
          data={data}
          className="sm:px-4"
          isDisclosure
        />
      ) : (
        <EmptyList
          title="검색 결과가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
    </div>
  );
};
