'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useSearchParams } from 'next/navigation';

import { CommonPagination } from '@/shared/ui/CommonPagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/ui/shadcn/table';

interface MyDepositHistoryTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data?: {
    list: TData[];
    totalPage: number;
    totalCount: number;
  };
  pagination?: boolean;
  className?: string;
}

export function MyDepositHistoryTable<TData, TValue>({
  columns,
  data,
  pagination = true,
  className = 'py-10 px-6 sm:px-10',
}: MyDepositHistoryTableProps<TData, TValue>) {
  const searchParams = useSearchParams();
  const page = searchParams.get('page') || 1;

  const table = useReactTable<TData>({
    data: data?.list || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className="space-y-6">
      {pagination && (
        <p className="">
          전체 <span className="fond-bold text-primary-500"> {data?.totalCount || 0}</span> 건
        </p>
      )}
      <div className="clear-start border-t border-t-gray-300 sm:!mb-12 ml:!mb-10">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="h-[45px] bg-gray-50 px-10">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="text-center"
                      style={{ width: `${header.getSize()}px` }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className={'border-b border-gray-300'}>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className="cursor-pointer"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className={`${className} sm:py-8 ml:py-10`}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  데이터가 없습니다.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {pagination && (
        <CommonPagination page={Number(page)} pageSize={10} totalCount={data?.totalCount || 0} />
      )}
    </div>
  );
}
