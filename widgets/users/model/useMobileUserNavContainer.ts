import { signOut } from 'next-auth/react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { WebViewMessage } from '@/shared/lib/webViewMessage';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const useMobileUserNavContainer = () => {
  const { user } = useFetchUser();
  const { routerPush, isApp } = useWebViewRouter();
  const { isVisible: isLogoutVisible, toggleVisibility: toggleLogoutVisibility } = useVisibility();
  const { isVisible: isLoginVisible, toggleVisibility: toggleLoginVisibility } = useVisibility();

  const handleConfirm = async () => {
    if (isApp) {
      await signOut();
      WebViewMessage('logout', {});
    } else {
      await signOut({ redirect: true, callbackUrl: '/' });
    }
  };

  return {
    isLogoutVisible,
    isLoginVisible,
    handleConfirm,
    toggleLogoutVisibility,
    toggleLoginVisibility,
    user,
    routerPush,
  };
};
