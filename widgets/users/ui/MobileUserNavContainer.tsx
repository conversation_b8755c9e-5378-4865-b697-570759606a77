import { ChevronRightIcon } from '@heroicons/react/24/solid';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';

import { useMobileUserNavContainer } from '../model/useMobileUserNavContainer';

interface MobileUserNavContainerProps {
  title: string;
  items: { title: string; href?: string; value?: string; auth?: boolean }[];
  isLast?: boolean;
  className?: string;
}

export const MobileUserNavContainer = ({
  title,
  items,
  isLast,
  className,
}: MobileUserNavContainerProps) => {
  const {
    isLogoutVisible,
    isLoginVisible,
    handleConfirm,
    routerPush,
    toggleLogoutVisibility,
    toggleLoginVisibility,
    user,
  } = useMobileUserNavContainer();

  return (
    <div className={`px-6 ${className}`}>
      <p className="py-3 font-semibold leading-[150%]">{title}</p>
      <ul>
        {items.map((item) => (
          <UserNavItem
            key={item.title}
            title={item.title}
            handleAction={() => {
              if (item.auth && !user) {
                toggleLoginVisibility();
                return;
              }

              if (item.href) {
                routerPush(item.href);
              }
            }}
            value={item.value}
          />
        ))}
        {isLast && user && <UserNavItem title="로그아웃" handleAction={toggleLogoutVisibility} />}
      </ul>
      <ConfirmDialog
        isOpen={isLogoutVisible}
        handleAction={handleConfirm}
        handleOpen={toggleLogoutVisibility}
        title="로그아웃 하시겠습니까?"
      />
      <ConfirmDialog
        isOpen={isLoginVisible}
        handleAction={() => {
          routerPush('/sign-in');
        }}
        handleOpen={toggleLoginVisibility}
        title="로그인이 필요한 서비스입니다."
        description="로그인 하시겠습니까?"
      />
    </div>
  );
};

const UserNavItem = ({
  title,
  handleAction,
  value,
}: {
  title: string;
  handleAction?: () => void;
  value?: string;
}) => {
  return (
    <li onClick={handleAction} className="flex items-center justify-between py-3">
      <p>{title}</p>
      {value ? (
        <p className="text-sm text-gray-500"> {value}</p>
      ) : (
        <button>
          <ChevronRightIcon className="h-5 w-5 text-gray-400" />
        </button>
      )}
    </li>
  );
};
