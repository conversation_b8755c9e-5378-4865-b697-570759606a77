import { ChevronRightIcon } from '@heroicons/react/24/solid';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

export const MobileUserHeader = () => {
  const { user, username } = useFetchUser();
  const { routerPush } = useWebViewRouter();

  return (
    <header className="flex h-[42px] items-center justify-between px-5">
      {user ? (
        <>
          <div
            className="flex cursor-pointer items-center"
            onClick={() => routerPush('/mobile/user/my/profile')}
          >
            <p className="text-xl">
              <strong>{username}</strong>님
            </p>
            <ChevronRightIcon className="h-5 w-5" />
          </div>

          <button
            onClick={() => routerPush('/mobile/user/my/investment-settings')}
            className="h-7 w-[92px] rounded-[20px] border border-gray-300 text-xs font-semibold text-gray-700"
          >
            투자 정보 설정
          </button>
        </>
      ) : (
        <>
          <p className="text-xl" onClick={() => routerPush('/sign-in')}>
            <strong className="font-semibold text-primary-500">로그인</strong> 해주세요.
          </p>
          <button
            onClick={() => routerPush('/sign-in')}
            className="h-7 w-14 rounded-[20px] border border-gray-300 text-xs font-semibold text-gray-700"
          >
            로그인
          </button>
        </>
      )}
    </header>
  );
};
