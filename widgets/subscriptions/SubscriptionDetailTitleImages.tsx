import React from 'react';

import { useSubscriptionMainImage } from '@/features/subscription/model/useSubscriptionImage';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SubscriptionDetailTitleImagesProps {
  securitiesId: string;
}

export const SubscriptionDetailTitleImages = ({
  securitiesId,
}: SubscriptionDetailTitleImagesProps) => {
  const { imageList } = useSubscriptionMainImage(securitiesId);

  if (!imageList || imageList.length === 0) return null;

  return (
    <div className="mx-auto my-6 max-w-[800px] px-6 sm:my-8 sm:!mb-14 sm:!mt-10 sm:px-8 ml:max-w-screen-contents ml:px-0">
      {imageList?.map((item) => (
        <div key={item.order} className="relative mb-6 aspect-[5/2] w-full">
          <FallbackImage
            key={item.image.filieId}
            src={item.image.url}
            alt={item.image.name}
            fill
            className="object-cover"
            sizes="100vw"
          />
        </div>
      ))}
    </div>
  );
};
