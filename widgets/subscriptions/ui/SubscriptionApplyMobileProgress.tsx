import { SubscriptionApplySteps } from '@/entities/subscriptions/types';

import { Progress } from '@/shared/ui/shadcn/progress';

interface SubscriptionApplyMobileProgressProps {
  currentStep: SubscriptionApplySteps;
}

export const SubscriptionApplyMobileProgress = ({
  currentStep,
}: SubscriptionApplyMobileProgressProps) => {
  const subscriptionSteps = Object.values(SubscriptionApplySteps);
  const currentIndex = subscriptionSteps.indexOf(currentStep);
  const progressValue = ((currentIndex + 1) / subscriptionSteps.length) * 100;

  return (
    <div className="fixed left-0 top-[50px] w-full sm:hidden">
      <Progress
        className="h-1 rounded-none bg-blue-gray-50"
        indicatorClassName="rounded-none"
        value={progressValue}
      />
    </div>
  );
};
