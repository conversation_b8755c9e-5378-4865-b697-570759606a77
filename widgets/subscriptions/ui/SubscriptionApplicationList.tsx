import { useState } from 'react';

import { AssetSubscriptionCard } from '@/widgets/assets/ui/AssetSubscriptionCard';

import { fetchMySubscriptions } from '@/features/subscription/api/fetchMySubscriptions';

import { SubscriptionApplyStatusList } from '@/entities/subscriptions/types';

import { CommonSelect } from '@/shared/ui/CommonSelect';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

interface SubscriptionApplicationListProps {
  status: SubscriptionApplyStatusList[];
  isInvestor: boolean;
  isSelect?: boolean;
  isAsset: boolean;
  categoryTabs: { label: string; value: SubscriptionApplyStatusList | 'ALL' }[];
}

export const SubscriptionApplicationList = ({
  status,
  isInvestor,
  isAsset,
  isSelect = false,
  categoryTabs,
}: SubscriptionApplicationListProps) => {
  const [size, setSize] = useState(5);
  const [searchTypes, setSearchTypes] = useState<SubscriptionApplyStatusList[]>(status);

  const { data: mySubscriptions, isLoading } = fetchMySubscriptions(
    {
      page: 0,
      size,
      subscriptionApplyStatusList: searchTypes,
    },
    !!isInvestor,
  );

  const handleSortType = (value: SubscriptionApplyStatusList | 'ALL') => {
    if (value === 'ALL') {
      setSearchTypes(status);
    } else if (value === SubscriptionApplyStatusList.REFUND_WAIT) {
      setSearchTypes([
        SubscriptionApplyStatusList.REFUND_WAIT,
        SubscriptionApplyStatusList.REFUND_DONE,
      ]);
    } else {
      setSearchTypes([value]);
    }
  };

  const hasSameElements =
    searchTypes.length === status.length && searchTypes.every((type) => status.includes(type));

  return (
    <>
      <div className="space-y-5">
        {isSelect && (
          <div className="flex flex-col gap-2 sm:flex-row">
            <CommonSelect
              placeholder="전체"
              value={hasSameElements ? 'ALL' : searchTypes[0]}
              onChange={(value) => handleSortType(value as SubscriptionApplyStatusList | 'ALL')}
              options={categoryTabs}
            />
          </div>
        )}

        {isLoading ? (
          <Loading />
        ) : !isInvestor || (mySubscriptions?.list && mySubscriptions?.list.length === 0) ? (
          <div className="flex h-[250px] flex-col items-center justify-center gap-3 rounded-[20px] border border-gray-300 sm:h-[320px]">
            <FallbackImage
              src="/icons/img_empty.png"
              alt="empty-list"
              width={56}
              height={56}
              className="h-14 w-14 sm:block sm:h-16 sm:w-16"
            />
            <p className="text-sm text-gray-400 sm:text-lg">현재 보유한 투자 상품이 없습니다.</p>
          </div>
        ) : (
          mySubscriptions?.list.map((subscription, index) => (
            <AssetSubscriptionCard key={index} subscription={subscription} isAsset={isAsset} />
          ))
        )}
      </div>
      {Boolean(mySubscriptions?.totalCount && mySubscriptions?.totalCount > size) && (
        <div className="mt-16 flex justify-center">
          <SecondaryButton
            text="더보기"
            className="!h-[60px] w-40 border-gray-300"
            onClick={() => setSize(size + 5)}
          />
        </div>
      )}
    </>
  );
};
