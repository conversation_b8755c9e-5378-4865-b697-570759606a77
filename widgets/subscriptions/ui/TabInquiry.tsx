import { ChevronDownIcon, PencilSquareIcon } from '@heroicons/react/24/solid';
import { AnimatePresence, motion } from 'framer-motion';

import { useFaqList } from '@/widgets/faqs/model/useFaqList';

import { fetchInvestorFaqs } from '@/features/faqs/api/fetchInvestorFaqs';
import { FaqCard } from '@/features/faqs/ui/FaqCard';
import { SubscriptionInquiryForm } from '@/features/subscription/ui/SubscriptionInquiryForm';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { Subscription } from '@/entities/subscriptions/types';

import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { Separator } from '@/shared/ui/shadcn/separator';
import { TabsContent } from '@/shared/ui/shadcn/tabs';

import { SubscriptionInquiryList } from '../../inquiries/ui/SubscriptionInquiryList';

interface TabInquiryProps {
  securitiesId: string;
  subscription?: Subscription;
}

export const TabInquiry = ({ securitiesId, subscription }: TabInquiryProps) => {
  const { user } = useFetchUser();
  const { isVisible: isInquiryVisible, toggleVisibility: toggleInquiryVisibility } =
    useVisibility();

  const { data: faqs } = fetchInvestorFaqs({ securitiesId, page: 1, perPage: 10 });

  const { selectedFaq, handleFaqClick } = useFaqList();

  const { routerPush } = useWebViewRouter();

  return (
    <TabsContent value="inquiry" className="my-10 pb-20 sm:my-20 sm:pb-0">
      <div className="mx-auto max-w-[800px] px-6 sm:px-8 ml:max-w-screen-contents ml:px-0">
        <div
          className={`space-y-4 rounded-lg border-gray-200 bg-white sm:border ${!isInquiryVisible && 'border'}`}
        >
          <div className={`space-y-4 sm:p-8 ${!isInquiryVisible && 'p-5'}`}>
            <h5 className="font-semibold">궁금한 사항이 있으신가요?</h5>
            <div className="space-y-2">
              <p className="text-sm">투자 전 궁금한 점이 있으시다면 문의 사항을 남겨주세요.</p>
              <ul className="list-outside list-disc space-y-1 pl-4 text-sm">
                <li>투자 상품에 대한 문의사항을 등록해 주세요.</li>
                <li>답변이 완료되면 가입하신 이메일로 알림을 보내드립니다.</li>
              </ul>
            </div>
          </div>

          {user ? (
            <AnimatePresence mode="wait">
              {isInquiryVisible && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="px-6 sm:px-8"
                >
                  <Separator className="bg-gray-200" orientation="horizontal" />
                  <SubscriptionInquiryForm
                    securitiesId={securitiesId}
                    handleClose={toggleInquiryVisibility}
                    subscription={subscription}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          ) : (
            <div className="px-5 sm:px-8">
              <Separator className="bg-gray-200" orientation="horizontal" />
              <button
                onClick={() => routerPush('/sign-in')}
                className="mb-5 mt-6 flex w-full items-center justify-between rounded-lg bg-primary-00 px-5 py-4 sm:px-8 sm:py-6"
              >
                <p className="font-semibold text-primary-500">로그인 후 이용이 가능합니다.</p>
                <PencilSquareIcon className="h-6 w-6 text-primary-500" />
              </button>
            </div>
          )}

          {user && (
            <button
              onClick={toggleInquiryVisibility}
              className="relative flex h-[45px] w-full min-w-[120px] items-center justify-center gap-1 rounded-b-lg bg-blue-gray-00 text-sm font-semibold text-gray-500"
            >
              <div className="flex items-center gap-1">
                문의하기
                <ChevronDownIcon className={`h-5 w-5 ${isInquiryVisible && 'rotate-180'}`} />
              </div>
            </button>
          )}
        </div>
        <div className="mt-14 space-y-4 sm:mt-20 sm:space-y-8">
          <h4 className="text-18 sm:text-24">자주하는 질문(FAQ)</h4>
          <div>
            {faqs?.data.map((faq, index) => (
              <FaqCard
                key={faq.id}
                faq={faq}
                isLast={index === faqs.data.length - 1}
                handleFaqClick={handleFaqClick}
                selectedFaq={selectedFaq}
                isInquiry
              />
            ))}
          </div>
        </div>
      </div>

      {user && (
        <>
          <Separator className="mt-7 h-4 bg-blue-gray-00 sm:mt-20" />
          <SubscriptionInquiryList securitiesId={securitiesId} />
        </>
      )}
    </TabsContent>
  );
};
