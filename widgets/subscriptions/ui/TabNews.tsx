import React, { useState } from 'react';

import { fetchSubscriptionNews } from '@/features/subscription/api/fetchSubscriptionNews';

import { utilFormats } from '@/shared/lib/utilformats';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Separator } from '@/shared/ui/shadcn/separator';
import { TabsContent } from '@/shared/ui/shadcn/tabs';

interface TabNewsProps {
  securitiesId: string;
}

export const TabNews = ({ securitiesId }: TabNewsProps) => {
  const [perPage, setPerPage] = useState(5);
  const { data: subscriptionNews, isLoading } = fetchSubscriptionNews(securitiesId, {
    perPage,
  });

  const { YYYYMMDD } = utilFormats();

  if (isLoading) return <Loading />;

  return (
    <TabsContent
      value="news"
      className="mx-auto my-8 max-w-[800px] px-6 pb-20 sm:my-20 sm:px-8 ml:max-w-screen-contents ml:px-0"
    >
      {subscriptionNews?.data.length === 0 && (
        <div className="flex h-[400px] flex-col items-center justify-center gap-4">
          <FallbackImage
            src="/icons/img_empty.png"
            alt="empty-list"
            width={64}
            height={64}
            className="sm:block"
          />
          <p className="text-gray-400">아직 등록된 투자 새소식이 없습니다.</p>
        </div>
      )}
      <div>
        {subscriptionNews?.data.map((news, index) => (
          <div key={news.id}>
            <div className="space-y-6 sm:space-y-12">
              <div className="space-y-2 sm:space-y-3">
                <p className="text-18 sm:text-24">{news.title}</p>
                <p className="text-sm text-gray-500">{YYYYMMDD(news.createdAt)}</p>
              </div>
              <div dangerouslySetInnerHTML={{ __html: news.content }} />
            </div>
            {index !== subscriptionNews?.data.length - 1 && (
              <Separator className="my-10 h-[1px] bg-gray-700 sm:my-20" />
            )}
          </div>
        ))}
      </div>
      {Boolean(subscriptionNews?.meta?.total && subscriptionNews?.meta?.total > perPage) && (
        <div className="mt-[100px] flex justify-center">
          <SecondaryButton
            onClick={() => {
              setPerPage(perPage + 5);
            }}
            text="더 보기"
            className="w-20 border-2 border-gray-300 font-semibold text-gray-900 sm:!h-[60px] sm:w-40"
          />
        </div>
      )}
    </TabsContent>
  );
};
