import { AnimatePresence, motion } from 'framer-motion';
import { useSession } from 'next-auth/react';
import { UseFormReturn } from 'react-hook-form';

import { fetchInvestorLimit } from '@/features/investor/api/fetchInvestorLimit';
import { SubscriptionApplyFormData } from '@/features/subscription/lib/SubscriptionApplySchema';
import { SubscriptionApplyQuantityForm } from '@/features/subscription/ui/SubscriptionApplyQuantityForm';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { InvestorQualificationType } from '@/entities/investor/types';
import { Subscription } from '@/entities/subscriptions/types';
import { ApplyProgressIndicator } from '@/entities/subscriptions/ui/ApplyProgressIndicator';

import { utilFormats } from '@/shared/lib/utilformats';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { Separator } from '@/shared/ui/shadcn/separator';

import { ApplyProductSummaryInfo } from './ApplyProductSummaryInfo';
import { SubscriptionApplyInfo } from './SubscriptionApplyInfo';
import { SubscriptionApplyLimit } from './SubscriptionApplyLimit';

interface SubscriptionApplyQuantityProps {
  form: UseFormReturn<SubscriptionApplyFormData>;
  subscription?: Subscription;
  mobileProgressStep: () => number;
}

export const SubscriptionApplyQuantity = ({
  form,
  subscription,
  mobileProgressStep,
}: SubscriptionApplyQuantityProps) => {
  const { CASHCOMMA } = utilFormats();

  usePageHeader({
    mode: HeaderMode.LIST,
    subTitle: '청약 수량 입력',
    rightComponent: <ApplyProgressIndicator current={mobileProgressStep()} total={4} />,
  });

  const { user } = useFetchUser();

  const { data: investorLimit } = fetchInvestorLimit(subscription?.issuer.id.toString());

  const isProfessionalUser =
    user?.investorQualification.current?.qualificationType ===
    InvestorQualificationType.PROFESSIONAL;

  const maxUserQuantity = Math.floor(
    (investorLimit?.issuerInvestLimitAmount || 0) / (subscription?.offeringPrice || 0),
  );

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="mt-8 space-y-6 sm:mt-0 sm:space-y-12"
      >
        <div className="space-y-6 px-6 sm:space-y-4 sm:px-8 ml:px-0">
          <SubscriptionApplyLimit investorLimit={investorLimit} />
          <ApplyProductSummaryInfo subscription={subscription} />
        </div>

        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        {/* 청약가능 수량 (1인 한도 수량) */}
        <SubscriptionApplyInfo
          items={[
            {
              label: '1회당 금액',
              value: `${CASHCOMMA(subscription?.offeringPrice || 0)}원`,
            },
            {
              label: '청약 가능 수량',
              value: isProfessionalUser ? '투자 한도 제한 없음' : `1좌-${maxUserQuantity}좌`,
            },
          ]}
          title="청약 정보"
        />
        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        <SubscriptionApplyQuantityForm
          form={form}
          subscription={subscription}
          isProfessionalUser={isProfessionalUser}
        />
      </motion.div>
    </AnimatePresence>
  );
};
