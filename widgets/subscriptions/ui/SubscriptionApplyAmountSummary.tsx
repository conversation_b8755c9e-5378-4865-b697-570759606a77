import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { SubscriptionApplyFormData } from '@/features/subscription/lib/SubscriptionApplySchema';

import { Account } from '@/entities/assets/types';
import { Subscription } from '@/entities/subscriptions/types';
import { SubscriptionApplyInfoItem } from '@/entities/subscriptions/ui/SubscriptionApplyItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionApplyAmountSummaryProps {
  subscription?: Subscription;
  form: UseFormReturn<SubscriptionApplyFormData>;
  account?: Account;
}

export const SubscriptionApplyAmountSummary = ({
  subscription,
  form,
  account,
}: SubscriptionApplyAmountSummaryProps) => {
  const { CASHCOMMA } = utilFormats();
  const offeringPrice = subscription?.offeringPrice || 0;
  const quantity = form.watch('quantity');
  const totalAmount = quantity * offeringPrice;
  const platformFee = 0;
  const requiredDepositAmount = Math.max(0, totalAmount - (account?.currentNetBalance || 0));
  const isDepositRequired = requiredDepositAmount > 0;

  return (
    <div className="relative z-[100] space-y-5 px-6 sm:space-y-3 sm:px-8 ml:px-0">
      <h4 className="sm:text-20 text-base font-semibold">청약 금액 내역</h4>

      <div className="rounded-lg border-gray-300 sm:border">
        <div className="space-y-3 sm:space-y-6 sm:p-8">
          <SubscriptionApplyInfoItem
            label="총 청약 증거금"
            value={`${CASHCOMMA(totalAmount || 0)}원`}
            labelClassName="text-sm sm:text-base text-gray-600"
            valueClassName="text-sm sm:text-base"
          />
          {/* 트랜잭션 fee */}
          <SubscriptionApplyInfoItem
            label="플랫폼 수수료"
            value={`${CASHCOMMA(platformFee || 0)}원`}
            labelClassName="text-sm sm:text-base text-gray-600"
            valueClassName="text-sm sm:text-base"
          />
          <SubscriptionApplyInfoItem
            label="보유 예치금"
            value={`${CASHCOMMA(account?.currentNetBalance || 0)}원`}
            labelClassName="text-sm sm:text-base text-gray-600"
            valueClassName="text-sm sm:text-base"
          />
        </div>

        <Separator orientation="horizontal" className="my-7 bg-gray-200 sm:hidden" />

        <div className="rounded-b-lg sm:bg-gray-50 sm:px-8 sm:py-6">
          <div className="flex w-full items-center justify-between">
            <span className="text-18">입금 필요 금액</span>
            {isDepositRequired ? (
              <span className="text-18 sm:text-24 text-primary-500 sm:text-red-500">
                {CASHCOMMA(requiredDepositAmount)}원
              </span>
            ) : (
              <span className="text-18 sm:text-24">0원</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
