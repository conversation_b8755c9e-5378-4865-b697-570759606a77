import React from 'react';

import { CheckboxField } from '@/shared/ui/CheckboxField';

export const SubscriptionPropensityAgreementCheckbox = () => {
  return (
    <div className="space-y-6">
      <h4 className="text-lg font-semibold">유의사항</h4>
      <div className="space-y-6 leading-[170%]">
        <p className="font-semibold">
          본 확인서는 투자자가 본인의 투자성향에 적합하지 않은 투자성 상품에 대한 거래 의사를
          확인하기위해 제공되었습니다. <br /> 또한 향후 분쟁 또는 소송이 발생하는 경우 고객님의
          권리구제에 불리하게 사용될 수 있으므로 신중하게 작성할 필요가 있습니다.
        </p>
        <ol className="list-outside list-disc space-y-4 pl-6">
          <li>
            투자자 성향 대비 위험도가 높은 투자성 상품 가입 시 금융회사는 투자권유를 할 수 없으므로
            본인 판단 하에 투자여부를 결정하시기 바랍니다.
          </li>
          <li>
            투자 설명을 요청하지 않는 경우 금융회사는 설명 의무*를 부담하지 않습니다. <br />
            <strong className="font-normal text-gray-500">
              *설명의무(금융소비자보호법 제19조): 금융상품의 중요사항을 소비자가 이해할 수 있도록
              설명
            </strong>
          </li>
          <li>
            투자 시 원금 손실이 발생할 수 있으며, 투자 손인에 대한 책임은 모두 고객님께 귀속됩니다.
          </li>
          <li>
            투자자 성향에 비해 고위험 상품에 투자하는 경우에는 예상보다 큰 폭의 손실이 발생할 수
            있습니다.
          </li>
        </ol>
      </div>
      <div className="mt-4 rounded-lg border border-gray-300 bg-gray-50 p-6">
        <CheckboxField
          value="1"
          label="상기 내용을 읽고 해당 내용을 충분히 인지하였으며, 이에 동의합니다."
          checked={false}
          required
          size="sm"
          onCheckedChange={() => {}}
        />
      </div>
    </div>
  );
};
