import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { OcrFormData } from '@/features/ocr/lib/ocrSchema';
import { OcrSteps } from '@/features/ocr/type';
import { OcrForm } from '@/features/ocr/ui/OcrForm';
import { OcrMobileForm } from '@/features/ocr/ui/OcrMobileForm';
import { OcrMobileGuide } from '@/features/ocr/ui/OcrMobileGuide';
import { SubscriptionStartOnRectangleButton } from '@/features/subscription/ui/SubscriptionStartOnRectangleButton';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { TestComplete } from '@/shared/ui/TestComplete';

interface OnboardingOcrProps {
  ocrStep: OcrSteps;
  isOcrLoading: boolean;
  ocrForm: UseFormReturn<OcrFormData>;
  handleFileUpload: () => void;
  handleFileChange: (files: File[]) => void;
  handleVerify: () => void;
  isVerifiedLoading: boolean;
  fileUploaderRef: React.RefObject<HTMLInputElement | null>;
  handleNext: () => void;
  isOcrStep: (step: OcrSteps) => boolean;
  isErrorDialogVisible: boolean;
  toggleErrorDialog: () => void;
  toggleExitDialog: () => void;
}

export const OnboardingOcr = ({
  ocrStep,
  isOcrLoading,
  ocrForm,
  handleFileUpload,
  handleFileChange,
  handleVerify,
  isVerifiedLoading,
  fileUploaderRef,
  handleNext,
  isOcrStep,
  isErrorDialogVisible,
  toggleErrorDialog,
  toggleExitDialog,
}: OnboardingOcrProps) => {
  const ocrVerifyStep = ocrStep === OcrSteps.VERIFY || ocrStep === OcrSteps.UPLOAD;

  const { isApp } = useWebViewRouter();

  const nextButtonByApp = () => {
    if (isOcrStep(OcrSteps.UPLOAD)) {
      handleFileUpload();
    } else if (isOcrStep(OcrSteps.VERIFY)) {
      handleVerify();
    } else if (isOcrStep(OcrSteps.COMPLETE)) {
      handleNext();
    }
  };

  return (
    <>
      {isApp ? (
        <section>
          {isOcrStep(OcrSteps.UPLOAD) && (
            <OcrMobileGuide
              isOcrLoading={isOcrLoading}
              form={ocrForm}
              handleFileUpload={handleFileUpload}
              handleFileChange={handleFileChange}
              handleVerify={handleVerify}
              isVerifiedLoading={isVerifiedLoading}
              fileUploaderRef={fileUploaderRef}
              isOnboarding
            />
          )}
          {isOcrStep(OcrSteps.VERIFY) && (
            <OcrMobileForm
              isOcrLoading={isOcrLoading}
              form={ocrForm}
              handleFileUpload={handleFileUpload}
              handleFileChange={handleFileChange}
              handleVerify={handleVerify}
              isVerifiedLoading={isVerifiedLoading}
              fileUploaderRef={fileUploaderRef}
              isOnboarding
            />
          )}
          {isOcrStep(OcrSteps.COMPLETE) && (
            <TestComplete
              title="본인 실명인증 완료"
              description="본인 실명인증이 완료되었습니다."
              goToCallbackUrl={handleNext}
              isOnboarding
            />
          )}
        </section>
      ) : (
        <>
          <section>
            {ocrVerifyStep && (
              <OcrForm
                isOcrLoading={isOcrLoading}
                form={ocrForm}
                handleFileUpload={handleFileUpload}
                handleFileChange={handleFileChange}
                handleVerify={handleVerify}
                isVerifiedLoading={isVerifiedLoading}
                fileUploaderRef={fileUploaderRef}
                isOnboarding
              />
            )}
            {isOcrStep(OcrSteps.COMPLETE) && (
              <TestComplete
                title="본인 실명인증 완료"
                description="본인 실명인증이 완료되었습니다."
                goToCallbackUrl={handleNext}
              />
            )}
          </section>
        </>
      )}

      {isApp && (
        <div className="fixed bottom-0 left-0 flex w-full items-center gap-2 bg-white px-6 py-2 sm:hidden">
          {isOcrStep(OcrSteps.VERIFY) && (
            <SecondaryButton
              onClick={() => {
                ocrForm.reset();
                handleFileUpload();
              }}
              text="다시 촬영하기"
              className="h-12 w-full border-gray-300 text-base"
            />
          )}
          <PrimaryButton
            onClick={nextButtonByApp}
            disabled={isVerifiedLoading}
            text="다음"
            className="h-12 w-full text-base"
          />
          <SubscriptionStartOnRectangleButton handleClick={toggleExitDialog} />
        </div>
      )}
      <ConfirmDialog
        isOpen={isErrorDialogVisible}
        handleOpen={toggleErrorDialog}
        description={`유효한 신분증이 아닙니다. \n 이미지를 다시 확인해 주세요.`}
        handleAction={() => {
          toggleErrorDialog();
          ocrForm.reset();
        }}
        isCancelButton={false}
      />
    </>
  );
};
