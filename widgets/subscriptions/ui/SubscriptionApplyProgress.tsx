import { SubscriptionApplySteps } from '@/entities/subscriptions/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SubscriptionApplyProgressProps {
  currentStep: SubscriptionApplySteps;
}

export const SubscriptionApplyProgress = ({ currentStep }: SubscriptionApplyProgressProps) => {
  const steps = [
    SubscriptionApplySteps.QUANTITY,
    SubscriptionApplySteps.DEPOSIT,
    SubscriptionApplySteps.CONFIRM,
    SubscriptionApplySteps.COMPLETE,
  ];
  const labels = ['청약 수량 입력', '예치금 입금', '청약 내역 확인', '청약 완료'];

  const getStepAlignment = (index: number) => {
    switch (index) {
      case 1:
        return 'left-[-12px]';
      case 2:
        return 'left-[-16px]';
      case 3:
        return 'left-[-4px]';
      default:
        return '';
    }
  };

  return (
    <div className="mb-[100px] mt-[60px] hidden px-6 sm:block sm:px-8 ml:px-0">
      <div className="flex">
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const isFirst = index === 0;
          const isCurrent = step === currentStep;
          const isDone = steps.indexOf(step) < steps.indexOf(currentStep);
          const isUpcoming = steps.indexOf(step) > steps.indexOf(currentStep);

          return (
            <div key={step} className={`relative flex flex-col ${!isLast && 'flex-1'}`}>
              <div
                className={`flex w-full items-center ${isFirst ? 'pl-5' : isLast ? 'pr-5' : ''}`}
              >
                {isCurrent && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-500 font-bold text-white ring-8 ring-primary-200/20">
                    {index + 1}
                  </div>
                )}
                {isDone && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-500 font-bold text-white">
                    <FallbackImage
                      src="/icons/check_white.png"
                      alt="check"
                      width={16}
                      height={16}
                    />
                  </div>
                )}
                {isUpcoming && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-gray-200 bg-gray-50 text-lg font-bold text-gray-300">
                    {index + 1}
                  </div>
                )}

                {!isLast && (
                  <div
                    className={`h-0.5 flex-1 border-t-2 ${isDone ? 'border-primary-500' : 'border-dashed border-gray-200'}`}
                  />
                )}
              </div>
              <div
                className={`absolute top-12 flex text-sm font-semibold text-gray-400 ${getStepAlignment(index)}`}
              >
                {labels[index]}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
