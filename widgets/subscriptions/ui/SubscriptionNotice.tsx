import Link from 'next/link';
import React from 'react';

interface SubscriptionNoticeProps {
  className?: string;
}

export const SubscriptionNotice = ({ className }: SubscriptionNoticeProps) => {
  const handleOpenPopup = (url: string) => {
    window.open(url, '_blank', 'width=700, height=600, top=0, left=0, scrollbars=yes');
  };

  return (
    <div className={className}>
      <div
        className={`mt-6 flex flex-col justify-center gap-3 rounded-[10px] border border-black/[0.04] bg-primary-00 p-5 text-xs sm:flex-row sm:items-center sm:px-8 sm:py-6`}
      >
        <h4 className="flex min-w-[45px] items-center font-semibold text-primary-500">투자 유의</h4>
        <div className="items-center gap-2 sm:flex">
          <h6 className="leading-[150%] text-gray-700">
            크라우드펀딩은 투자 원금의 전부 또는 일부에 대한 손실 위험이 있는 고위험 투자입니다.
            투자 내용을 꼼꼼하게 확인한 후 안전한 투자 하시길 바랍니다.
            <p
              onClick={() => handleOpenPopup('/terms/investment-risk-disclosure')}
              className="inline cursor-pointer pl-2 text-xs font-semibold underline"
            >
              자세히 보기
            </p>
          </h6>
        </div>
      </div>
    </div>
  );
};
