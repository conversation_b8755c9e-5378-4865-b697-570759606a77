'use client';

import React from 'react';
import 'swiper/css';
import { Autoplay, Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

import { fetchBanners } from '@/features/banners/api/fetchBanners';

import { Banner } from '@/entities/banners/types';

import { ListResponse } from '@/shared/interface';
import { useCarousel } from '@/shared/model/useCarousel';
import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SubscriptionHeroBannerProps {
  banners: ListResponse<Banner>;
}

export const SubscriptionHeroBanner = ({ banners }: SubscriptionHeroBannerProps) => {
  const { setSwiper, handleSlideChange, swiper, currentIndex } = useCarousel();

  const { data: bannerList } = fetchBanners({ type: 'broker_main' }, banners);

  return (
    <div className="mx-auto mt-5 max-w-screen-lg sm:mt-0 sm:px-8 ml:px-10">
      <Swiper
        spaceBetween={10}
        slidesPerView={1}
        modules={[Pagination, Navigation, Autoplay]}
        onSlideChange={(swiperInstance) => handleSlideChange(swiperInstance)}
        navigation
        onSwiper={(e) => {
          setSwiper(e);
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        grabCursor={true}
        touchMoveStopPropagation={true}
        className="rounded-[20px]"
      >
        {bannerList?.data[0]?.banner_items.map((banner, index) => (
          <SwiperSlide key={index}>
            <div className="relative aspect-[680/247] ml:aspect-[1200/237]">
              <FallbackImage
                src={banner.image.url}
                alt="subscription_banner"
                fill
                quality={100}
                priority
                sizes="100vw, (min-width: 1440px) 1200px"
                className="sm:rounded-[20px]"
              />
            </div>
          </SwiperSlide>
        ))}
        <div className="absolute bottom-4 right-5 z-20 flex h-[30px] w-[70px] items-center justify-center rounded-[60px] bg-black/20">
          <div className="flex items-center gap-2 text-sm font-semibold text-white/50">
            <span className="text-white">{currentIndex + 1}</span> <span>∙</span>
            <span className="text-white/50">{swiper?.slides.length}</span>
          </div>
        </div>
      </Swiper>
    </div>
  );
};
