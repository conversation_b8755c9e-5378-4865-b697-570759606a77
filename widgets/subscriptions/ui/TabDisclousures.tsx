import { useParams, usePathname } from 'next/navigation';
import React from 'react';

import { useDisclosures } from '@/views/disclosure/model/useDisclosures';

import { DisclosureDesktopList } from '@/widgets/disclosures/ui/DisclosureDesktopList';
import { DisclosureDetailHeader } from '@/widgets/disclosures/ui/DisclosureDetailHeader';
import { DisclosureMobileList } from '@/widgets/disclosures/ui/DisclosureMobileList';

import { DisclosureSearchForm } from '@/features/disclosures/ui/DisclosureSearchForm';
import { useFetchCheckSubscriptionAllotment } from '@/features/subscription/api/fetchCheckSubscriptionAllotment';

import { UserRole } from '@/shared/types';
import { AttachFileList } from '@/shared/ui/AttachFileList';
import { BackButton } from '@/shared/ui/BackButton';
import { Loading } from '@/shared/ui/Loading';
import { Separator } from '@/shared/ui/shadcn/separator';
import { TabsContent } from '@/shared/ui/shadcn/tabs';

export const TabDisclousures = ({ securitiesId }: { securitiesId: string }) => {
  const { data, page, disclosureId, disclosure, isDisclosureLoading, session } = useDisclosures({
    securitiesId,
  });
  const { id: subscriptionInfoId } = useParams();

  const isRegularUser = !session || session.user.role === UserRole.USR;

  const { data: isApplyAloDone } = useFetchCheckSubscriptionAllotment({
    subscriptionInfoId: subscriptionInfoId?.toString(),
    isEnabled: !isRegularUser,
  });

  const pathname = usePathname();

  if (!isApplyAloDone) {
    return (
      <TabsContent
        value="disclosures"
        className="mx-auto my-8 max-w-[800px] px-6 pb-20 sm:my-20 sm:px-8 sm:pb-0 ml:max-w-screen-lg ml:px-0"
      >
        <div className="mx-auto my-20 flex h-[187px] max-w-[800px] flex-col justify-center break-keep bg-gray-50 px-6 text-center text-sm text-gray-700 sm:px-8 sm:pb-0 sm:text-lg ml:max-w-screen-contents ml:px-0">
          배정이 완료된 투자자 회원만 이용하실 수 있는 발행인 '공시' 메뉴입니다.
        </div>
      </TabsContent>
    );
  }

  if (isDisclosureLoading) {
    return <Loading />;
  }

  return (
    <TabsContent
      value="disclosures"
      className="mx-auto my-8 max-w-[800px] px-6 pb-20 sm:my-20 sm:px-8 sm:pb-0 ml:max-w-screen-lg ml:px-0"
    >
      {disclosureId && disclosure ? (
        <section className="mx-auto mt-10 max-w-screen-contents md:mt-[100px]">
          <DisclosureDetailHeader disclosure={disclosure} />
          <Separator className="my-10 h-[1px] bg-gray-300 md:my-[88px]" />
          <div
            dangerouslySetInnerHTML={{ __html: disclosure?.content || '' }}
            className="ck-content"
          />
          {disclosure?.attachFiles && (
            <>
              <Separator className="mb-10 mt-10 h-[1px] bg-gray-300 md:mt-[88px]" />
              <AttachFileList attachFiles={disclosure?.attachFiles} />
            </>
          )}
          <div className="mt-[72px] flex justify-center md:mt-[88px]">
            <BackButton href={pathname} scroll={false} />
          </div>
        </section>
      ) : (
        <>
          <DisclosureSearchForm />
          <DisclosureDesktopList data={data} />
          <DisclosureMobileList data={data} page={Number(page)} />
        </>
      )}
    </TabsContent>
  );
};
