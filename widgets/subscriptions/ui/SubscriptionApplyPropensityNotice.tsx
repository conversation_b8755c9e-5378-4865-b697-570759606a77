import React from 'react';

import { PropensityRiskGuide } from '@/widgets/investor/ui/PropensityRiskGuide';

import { PropensityTypeEnum } from '@/entities/investor/types';
import { PropensityStatusCard } from '@/entities/investor/ui/PropensityStatusCard';

import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

import { SubscriptionPropensityAgreementCheckbox } from './SubscriptionPropensityAgreementCheckbox';

export const SubscriptionApplyPropensityNotice = () => {
  return (
    <div className="mx-auto mt-[100px] max-w-screen-test space-y-20 px-6 sm:px-8 ml:px-0">
      <h2 className="text-40">
        투자 성향에 적합하지 않은 <br /> 투자성 상품 거래 확인서 안내
      </h2>
      <div className="flex gap-2">
        <PropensityStatusCard propensityType={PropensityTypeEnum.STABLE} />
        <div className="flex w-full flex-col items-center justify-center rounded-lg border border-gray-300 bg-gray-50 py-10">
          <h4 className="text-sm font-semibold">투자성 상품의 위험등급</h4>
          <h3 className="text-20">매우높은위험 1등급</h3>
        </div>
      </div>
      <PropensityRiskGuide />
      <SubscriptionPropensityAgreementCheckbox />
      <div className="mt-[20px] flex gap-2 pb-40">
        <SecondaryButton text="취소" className="!h-[60px] w-full" />
        <PrimaryButton text="확인" className="!h-[60px] w-full" />
      </div>
    </div>
  );
};
