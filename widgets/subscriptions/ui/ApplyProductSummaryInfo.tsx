import { Subscription } from '@/entities/subscriptions/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface ApplyProductSummaryInfoProps {
  subscription?: Subscription;
  className?: string;
}

export const ApplyProductSummaryInfo = ({
  subscription,
  className,
}: ApplyProductSummaryInfoProps) => {
  return (
    <div
      className={`flex w-full items-center gap-3 rounded-lg border-gray-300 sm:h-[100px] sm:gap-6 sm:border sm:px-8 ${className}`}
    >
      <FallbackImage
        src="/images/subscription_image.png"
        alt="arrow"
        width={80}
        height={48}
        className="h-12 w-20 sm:h-[72px] sm:w-[120px]"
      />
      <div>
        <p className="text-xs text-gray-600">{subscription?.issuer.name}</p>
        <p className="text-base font-semibold sm:text-lg">
          {subscription?.securities.securitiesName}
        </p>
      </div>
    </div>
  );
};
