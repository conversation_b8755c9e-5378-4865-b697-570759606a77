import { ChevronLeftIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { Separator } from '@/shared/ui/shadcn/separator';

export interface OnboardingStep {
  label: string;
  step: any;
}

interface OnboardingDetailProgressProps {
  steps: OnboardingStep[];
  currentStep: any;
  className?: string;
}

export const OnboardingDesktopDetailProgress = ({
  steps,
  currentStep,
  className = '',
}: OnboardingDetailProgressProps) => {
  const stepNumbers = steps.map((step) => step.step);

  const isCompleted = (step: string) => {
    const currentStepIndex = stepNumbers.findIndex((s) => s === currentStep);
    const stepIndex = stepNumbers.findIndex((s) => s === step);
    return stepIndex <= currentStepIndex && stepIndex !== -1;
  };

  return (
    <div className={`hidden w-full max-w-[170px] py-6 ml:block ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={step.label}>
          <div className="flex h-8 items-center gap-3">
            <div
              className={`h-2 w-2 rounded-full ${isCompleted(step.step) ? 'bg-black' : 'bg-gray-300'}`}
            />
            <p className={`text-sm font-semibold ${isCompleted(step.step) ? '' : 'text-gray-400'}`}>
              {step.label}
            </p>
          </div>
          {index < steps.length - 1 && (
            <Separator
              orientation="vertical"
              className={`ml-[3px] mr-auto h-16 w-[1.5px] ${isCompleted(step.step + 1) ? 'bg-black' : 'bg-gray-300'}`}
            />
          )}
        </React.Fragment>
      ))}
      <Separator orientation="horizontal" className="my-14 w-full bg-gray-300" />
      <button className="flex items-center text-xs font-semibold text-gray-500 underline">
        <ChevronLeftIcon className="h-4 w-4" />
        그만하고 나가기
      </button>
    </div>
  );
};
