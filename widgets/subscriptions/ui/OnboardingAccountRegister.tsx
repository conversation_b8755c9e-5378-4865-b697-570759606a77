import { UseFormReturn } from 'react-hook-form';

import { DepositAccountFormData } from '@/features/deposit/lib/depositAccountSchema';
import { AccountRegisterComplete } from '@/features/deposit/ui/AccountRegisterComplete';
import { DepositAccountRegisterTerms } from '@/features/deposit/ui/DepositAccountRegisterTerms';
import { WithdrawAccountConfirm } from '@/features/deposit/ui/WithdrawAccountConfirm';
import { WithdrawAccountRegisterForm } from '@/features/deposit/ui/WithdrawAccountRegisterForm';
import { SubscriptionStartOnRectangleButton } from '@/features/subscription/ui/SubscriptionStartOnRectangleButton';

import { DepositSteps } from '@/entities/assets/types';

import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

interface OnboardingAccountRegisterProps {
  accountStep: DepositSteps;
  handleAccountNextStep: (step: DepositSteps) => void;
  accountForm: UseFormReturn<DepositAccountFormData>;
  handleVerifyAccount: () => void;
  handleCreateVirtualAccount: () => void;
  virtualAccount: {
    depositAccountInstCode: string;
    depositAccountNumberCode: string;
    depositorName: string;
  };
  handleNextStep: () => void;
  isAgree: boolean;
  toggleAgree: () => void;
  isLoading?: boolean;
  isIdentityVerification: boolean;
  toggleIdentityVerification: () => void;
  handleIdentityVerification: () => void;
  toggleExitDialog: () => void;
}

export const OnboardingAccountRegister = ({
  accountStep,
  handleAccountNextStep,
  accountForm,
  handleVerifyAccount,
  handleCreateVirtualAccount,
  virtualAccount,
  handleNextStep,
  isAgree,
  toggleAgree,
  isLoading,
  isIdentityVerification,
  toggleIdentityVerification,
  handleIdentityVerification,
  toggleExitDialog,
}: OnboardingAccountRegisterProps) => {
  const nextButtonByApp = () => {
    if (accountStep === DepositSteps.TERMS) {
      handleAccountNextStep(DepositSteps.FORM);
    } else if (accountStep === DepositSteps.FORM) {
      handleVerifyAccount();
    } else if (accountStep === DepositSteps.CONFIRM) {
      handleCreateVirtualAccount();
    } else if (accountStep === DepositSteps.COMPLETE) {
      handleNextStep();
    }
  };

  const nextButtonDisabled = () => {
    if (accountStep === DepositSteps.TERMS) {
      return !isAgree;
    } else if (accountStep === DepositSteps.FORM) {
      return !accountForm.formState.isValid;
    }
    return false;
  };

  const getHeaderConfig = () => {
    if (accountStep === DepositSteps.FORM) {
      return { mode: HeaderMode.LIST, subTitle: '출금 계좌 등록' };
    } else if (accountStep === DepositSteps.CONFIRM) {
      return { mode: HeaderMode.LIST, subTitle: '출금 계좌 인증 완료' };
    } else if (accountStep === DepositSteps.COMPLETE) {
      return {
        mode: HeaderMode.DETAIL,
      };
    }
    return { mode: HeaderMode.LIST, subTitle: '예치금 가상계좌 개설 발급 동의' };
  };

  usePageHeader(getHeaderConfig());

  return (
    <>
      {accountStep === DepositSteps.TERMS && (
        <DepositAccountRegisterTerms
          isAgree={isAgree}
          toggleAgree={toggleAgree}
          isOnboarding
          handleStep={() => handleAccountNextStep(DepositSteps.FORM)}
        />
      )}
      {accountStep === DepositSteps.FORM && (
        <WithdrawAccountRegisterForm
          isOnboarding
          form={accountForm}
          handleVerifyAccount={handleVerifyAccount}
          isLoading={isLoading}
        />
      )}
      {accountStep === DepositSteps.CONFIRM && (
        <WithdrawAccountConfirm
          isOnboarding
          form={accountForm}
          handleCreateVirtualAccount={handleCreateVirtualAccount}
          isLoading={isLoading}
        />
      )}
      {accountStep === DepositSteps.COMPLETE && (
        <AccountRegisterComplete
          isOnboarding
          virtualAccount={virtualAccount}
          goToCallbackUrl={handleNextStep}
        />
      )}

      <div className="fixed bottom-0 left-0 flex w-full items-center gap-2 bg-white px-6 py-2 sm:hidden">
        <PrimaryButton
          onClick={nextButtonByApp}
          disabled={nextButtonDisabled()}
          text="다음"
          className="h-12 w-full text-base"
        />
        <SubscriptionStartOnRectangleButton handleClick={toggleExitDialog} />
      </div>
      <ConfirmDialog
        isOpen={isIdentityVerification}
        handleAction={handleIdentityVerification}
        handleOpen={toggleIdentityVerification}
        title="휴대폰 본인인증 안내"
        isCancelButton
        description={`출금 계좌 등록을 위해\n휴대폰 본인인증을 진행해 주세요.`}
      />
    </>
  );
};
