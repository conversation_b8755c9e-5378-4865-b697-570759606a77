import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { SubscriptionApplyMobileVerifyForm } from '@/features/auth/ui/SubscriptionApplyMobileVerifyForm';
import { SubscriptionApplyFormData } from '@/features/subscription/lib/SubscriptionApplySchema';
import { SubscriptionApplyUserCard } from '@/features/users/ui/SubscriptionApplyUserCard';

import { Subscription } from '@/entities/subscriptions/types';
import { ApplyProgressIndicator } from '@/entities/subscriptions/ui/ApplyProgressIndicator';

import { utilFormats } from '@/shared/lib/utilformats';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { Separator } from '@/shared/ui/shadcn/separator';

import { ApplyProductSummaryInfo } from './ApplyProductSummaryInfo';
import { SubscriptionApplyInfo } from './SubscriptionApplyInfo';
import { SubscriptionApplyNotice } from './SubscriptionApplyNotice';

interface SubscriptionApplyConfirmProps {
  handleVerify: (transactionId: string) => void;
  subscription?: Subscription;
  form: UseFormReturn<SubscriptionApplyFormData>;
  mobileProgressStep: () => number;
}

export const SubscriptionApplyConfirm = ({
  handleVerify,
  subscription,
  form,
  mobileProgressStep,
}: SubscriptionApplyConfirmProps) => {
  const { CASHCOMMA } = utilFormats();

  usePageHeader({
    mode: HeaderMode.LIST,
    subTitle: '청약 내역 확인',
    rightComponent: <ApplyProgressIndicator current={mobileProgressStep()} total={4} />,
  });

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="mb-10 mt-8 space-y-6 pb-40 sm:mb-0 sm:mt-0 sm:space-y-12 sm:pb-0"
      >
        <ApplyProductSummaryInfo subscription={subscription} className="px-6" />

        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        <SubscriptionApplyUserCard />

        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        <SubscriptionApplyInfo
          items={[
            { label: '1회당 금액', value: `${CASHCOMMA(subscription?.offeringPrice || 0)}원` },
            { label: '청약 수량', value: `${form.watch('quantity')}좌` },
            { label: '청약증거금', value: `${CASHCOMMA(subscription?.offeringPrice || 0)}원` },
          ]}
          title="청약 내역"
          className="!space-y-3 sm:space-y-6"
        />
        <SubscriptionApplyNotice />
        <div className="px-6 sm:px-8 ml:px-0">
          <Separator orientation="horizontal" className="my-6 bg-gray-200 sm:my-[56px]" />
          <SubscriptionApplyMobileVerifyForm handleVerify={handleVerify} />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
