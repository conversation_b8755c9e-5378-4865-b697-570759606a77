import { Separator } from '@radix-ui/react-separator';
import React from 'react';

import { getHighestAllocationTypeWithLabel } from '@/features/subscription/lib/getHighestAllocationType';

import { SecuritiesType, SecuritiesTypeLabel, Subscription } from '@/entities/subscriptions/types';
import { SubscriptionDetailConditionItem } from '@/entities/subscriptions/ui/SubscriptionDetailConditionItem';
import { SubscriptionDetailItem } from '@/entities/subscriptions/ui/SubscriptionDetailItem';

import { utilFormats } from '@/shared/lib/utilformats';

interface SubscriptionDetailConditionProps {
  subscription?: Subscription;
}

export const SubscriptionDetailCondition = ({ subscription }: SubscriptionDetailConditionProps) => {
  const { CASHCOMMA, YYYYMMDD } = utilFormats();

  const allocationType = getHighestAllocationTypeWithLabel({
    fifo_percent: subscription?.firstcomeAllotRatio || 0,
    proportional_percent: subscription?.proportionalAllotRatio || 0,
    equal_percent: subscription?.equalAllotRatio || 0,
    issuer_percent: subscription?.priorityAllotRatio || 0,
  });

  return (
    <div className="mx-auto mb-16 max-w-[800px] space-y-2 px-6 sm:mb-[100px] sm:space-y-4 sm:px-8 ml:max-w-screen-contents ml:px-0">
      <h4 className="text-18 sm:text-20">증권 발행 조건</h4>
      <Separator className="my-4 h-[1px] bg-gray-200 sm:hidden" orientation="horizontal" />
      <div className="flex flex-col rounded-[20px] border-gray-300 sm:border">
        <div className="sm:p-8">
          <div className="grid w-full grid-cols-1 gap-y-[10px] rounded-t-[20px] sm:grid-cols-3 sm:gap-y-0 sm:!rounded-lg sm:bg-gray-50 sm:px-0 sm:py-[10px]">
            <SubscriptionDetailConditionItem label="증권종류" value="채권형" />
            <SubscriptionDetailConditionItem
              label="채권종류"
              value={
                SecuritiesTypeLabel[subscription?.securities.securitiesType as SecuritiesType] || ''
              }
            />
            <SubscriptionDetailConditionItem label="이자율" value="0%" />
          </div>
        </div>
        <div className="space-y-[10px] pt-[10px] sm:px-8 sm:pb-8 sm:pt-0">
          <div className="grid grid-cols-1 gap-x-4 gap-y-[10px] sm:grid-cols-2 ml:grid-cols-3">
            <SubscriptionDetailItem
              label="1좌당 금액"
              value={`${CASHCOMMA(subscription?.offeringPrice || 0)}원`}
              between
            />
            <SubscriptionDetailItem label="배정방법" value={allocationType.label} between />
            {/* securities expireD */}
            <SubscriptionDetailItem
              label="만기일"
              value={YYYYMMDD(subscription?.considerationAt) || ''}
              between
            />
            {/* 투자 최소 금액? x */}
            <SubscriptionDetailItem
              label="투자 최소 금액"
              value={`${CASHCOMMA(subscription?.offeringPrice || 0)}원`}
              between
            />
            <SubscriptionDetailItem
              label="배정일"
              value={YYYYMMDD(subscription?.allotAt) || ''}
              between
            />
            {/* 정산 정보 */}
            <SubscriptionDetailItem label="이자 지급 방식" value="상세 참고" between />
          </div>
          <div className="grid grid-cols-1 gap-x-4 gap-y-[10px] sm:grid-cols-2 ml:grid-cols-3">
            <SubscriptionDetailItem
              label="청약 시작일"
              value={YYYYMMDD(subscription?.beginAt) || ''}
              between
            />
            <SubscriptionDetailItem
              label="납입일"
              value={YYYYMMDD(subscription?.marginPayAt) || ''}
              between
            />
          </div>
          <div className="grid grid-cols-1 gap-x-4 gap-y-[10px] sm:grid-cols-2 ml:grid-cols-3">
            <SubscriptionDetailItem
              label="청약 종료일"
              value={YYYYMMDD(subscription?.endAt) || ''}
              between
            />

            <SubscriptionDetailItem
              label="발행일"
              value={YYYYMMDD(subscription?.securities.issueDate) || ''}
              between
            />
          </div>
        </div>
      </div>
    </div>
  );
};
