import {
  SubscriptionOnboardingSteps,
  SubscriptionOnboardingStepsLabel,
} from '@/entities/subscriptions/types';

interface OnboardingHeaderProps {
  step: SubscriptionOnboardingSteps;
}

export const OnboardingMobileHeader = ({ step }: OnboardingHeaderProps) => {
  const onboardingSteps = Object.values(SubscriptionOnboardingSteps);
  const currentIndex = onboardingSteps.indexOf(step);

  return (
    <div className="fixed top-[50px] z-10 w-full bg-white px-4 pb-2 pt-3 sm:hidden">
      <div className="flex h-[42px] items-center justify-between rounded-lg bg-primary-500 px-4 leading-[150%] text-white">
        <div className="flex items-center gap-2 text-sm">
          <h4 className="font-semibold">투자 서비스를 위한 절차</h4>
          <span>∙</span>
          <span>{SubscriptionOnboardingStepsLabel[step]}</span>
        </div>
        <div className="flex items-center gap-1 rounded-[20px] px-2 py-[2px] text-xs leading-[150%]">
          <span className="font-semibold">{currentIndex + 1}</span>
          <span>/</span>
          <span>{onboardingSteps.length}</span>
        </div>
      </div>
    </div>
  );
};
