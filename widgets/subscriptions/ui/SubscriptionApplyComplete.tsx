import { XMarkIcon } from '@heroicons/react/24/solid';
import { AnimatePresence, motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { fetchSubscriptionStatistics } from '@/features/subscription/api/fetchSubscriptionStatistics';
import { getHighestAllocationTypeWithLabel } from '@/features/subscription/lib/getHighestAllocationType';
import { SubscriptionApplyFormData } from '@/features/subscription/lib/SubscriptionApplySchema';

import { Subscription } from '@/entities/subscriptions/types';
import { SubscriptionApplyInfoItem } from '@/entities/subscriptions/ui/SubscriptionApplyItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { usePageHeader } from '@/shared/model/layouts/usePageHeader';

interface SubscriptionApplyCompleteProps {
  form: UseFormReturn<SubscriptionApplyFormData>;
  subscription?: Subscription;
  mobileProgressStep: () => number;
}

export const SubscriptionApplyComplete = ({
  form,
  subscription,
  mobileProgressStep,
}: SubscriptionApplyCompleteProps) => {
  const { watch } = form;
  const { YYYYMMDD, CASHCOMMA } = utilFormats();

  const { data: statistics } = fetchSubscriptionStatistics(
    subscription?.subscriptionInfoId.toString() ?? '',
  );

  const { applyStatistics } = statistics ?? {};

  const totalAmount = watch('quantity') * (subscription?.offeringPrice ?? 0);

  const allocationType = getHighestAllocationTypeWithLabel({
    fifo_percent: subscription?.firstcomeAllotRatio ?? 0,
    proportional_percent: subscription?.proportionalAllotRatio ?? 0,
    equal_percent: subscription?.equalAllotRatio ?? 0,
    issuer_percent: subscription?.priorityAllotRatio ?? 0,
  });

  const allocationTypeLabel = allocationType.label;

  const { id } = useParams();

  const router = useRouter();

  // 동적으로 rightComponent를 설정해야 하는 경우에만 usePageHeader 사용
  usePageHeader({
    subTitle: '청약 완료',
    rightComponent: (
      <XMarkIcon onClick={() => router.replace(`/subscriptions/${id}`)} className="h-5 w-5" />
    ),
  });

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="mt-12 space-y-12 px-6 sm:mt-0 sm:space-y-12 sm:px-8 ml:px-0"
      >
        <h2 className="text-center text-[26px] font-semibold">
          {/* N번째? 청약 신청했을때 응답으로 줘야함 */}
          <strong className="font-semibold text-primary-500">
            {applyStatistics?.applyCount}번째
          </strong>
          로 청약이 완료되었습니다.
        </h2>
        <div className="rounded-lg border border-gray-300">
          <div className="flex">
            <div className="flex flex-1 flex-col items-center justify-center space-y-1 border-b border-r border-gray-300 py-5 sm:space-y-[6px] sm:py-7">
              <h4 className="text-sm font-semibold text-gray-600 sm:text-base">청약 금액</h4>
              <h6 className="text-base font-semibold sm:text-lg">{CASHCOMMA(totalAmount)}원</h6>
            </div>
            <div className="flex flex-1 flex-col items-center justify-center space-y-1 border-b border-gray-300 py-5 sm:space-y-[6px] sm:py-7">
              <h4 className="text-sm font-semibold text-gray-600 sm:text-base">청약 수량</h4>
              <h6 className="text-base font-semibold sm:text-lg">
                {CASHCOMMA(watch('quantity'))}좌
              </h6>
            </div>
          </div>
          <div className="space-y-3 p-6 sm:space-y-6 sm:p-8">
            <SubscriptionApplyInfoItem
              label="투자 상품명"
              value={subscription?.securities.securitiesName || ''}
            />
            <SubscriptionApplyInfoItem label="기업명" value={subscription?.issuer.name || ''} />
            <SubscriptionApplyInfoItem label="청약 신청일" value={YYYYMMDD(new Date()) || ''} />
            <SubscriptionApplyInfoItem
              label="배정일"
              value={YYYYMMDD(subscription?.allotAt) || ''}
            />
            <SubscriptionApplyInfoItem label="배정 방법" value={allocationTypeLabel} />
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
