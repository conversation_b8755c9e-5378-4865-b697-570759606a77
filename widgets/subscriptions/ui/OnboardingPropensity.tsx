import React from 'react';

import { InvestorProgress } from '@/widgets/investor/ui/InvestorProgress';
import { InvestorPropensityNotice } from '@/widgets/investor/ui/InvestorPropensityNotice';
import { InvestorPropensityTerms } from '@/widgets/investor/ui/InvestorPropensityTerms';
import { PropensityQuestion } from '@/widgets/investor/ui/PropensityQuestion';
import { PropensityResult } from '@/widgets/investor/ui/PropensityResult';
import { PropensityResultHeader } from '@/widgets/investor/ui/PropensityResultHeader';
import { TestNotice } from '@/widgets/investor/ui/TestNotice';

import { propensityNotices } from '@/features/investor/config';
import { PropensityType } from '@/features/investor/ui/PropensityType';

import {
  PropensityAnswer,
  PropensityScore,
  PropensitySteps,
  PropensityTerms,
  PropensityTypeEnum,
} from '@/entities/investor/types';

import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

interface OnboardingPropensityProps {
  propensityScore: PropensityScore;
  propensityStep: PropensitySteps;
  selectedType: PropensityTypeEnum | undefined;
  onSelectedType: (type: PropensityTypeEnum) => void;
  terms: PropensityTerms;
  onTermsChange: (key: keyof PropensityTerms, value: boolean) => void;
  answers: PropensityAnswer;
  handleSingleAnswer: (questionNumber: number, key: string, value: string, score: number) => void;
  handleMultiAnswer: (questionNumber: number, key: string, isSelected: boolean) => void;
  handleMultiRatioAnswer: (questionNumber: number, key: string, value: string) => void;
  handlePropensityStep: (step: PropensitySteps) => void;
  handlePropensityNext: () => void;
  isPropensityDisabled: () => boolean;
  handlePrev: () => void;
  isPropensityStep: (step: PropensitySteps) => boolean;
  currentQuestion: number;
}

export const OnboardingPropensity = ({
  propensityScore,
  propensityStep,
  selectedType,
  onSelectedType,
  terms,
  onTermsChange,
  answers,
  handleSingleAnswer,
  handleMultiAnswer,
  handleMultiRatioAnswer,
  handlePropensityNext,
  isPropensityDisabled,
  handlePrev,
  currentQuestion,
  isPropensityStep,
  handlePropensityStep,
}: OnboardingPropensityProps) => {
  const isPropensityLastStep = isPropensityStep(PropensitySteps.RESULT);

  const isPropensityFirstStep = propensityStep === PropensitySteps.TYPE;

  const isPropensityQuestionStep = propensityStep === PropensitySteps.QUESTION;

  if (isPropensityStep(PropensitySteps.NOTICE)) {
    return (
      <TestNotice
        title="투자 성향 진단"
        description={`자본시장법에 따라 투자 서비스 전 투자 성향 진단이 필요합니다. \n 절차에 따라 진행해 주세요.`}
        notices={propensityNotices}
        onStartTest={() => handlePropensityStep(PropensitySteps.TYPE)}
      />
    );
  }

  return (
    <div className="mx-auto max-w-screen-test">
      {isPropensityLastStep && <PropensityResultHeader propensityScore={propensityScore} />}
      {!isPropensityLastStep && (
        <div className="mx-auto mt-[100px] max-w-screen-test space-y-5">
          <h2 className="text-40">투자 성향 진단</h2>
          <InvestorProgress isStep={isPropensityStep} currentQuestion={currentQuestion} />
        </div>
      )}
      {isPropensityFirstStep && (
        <PropensityType selectedType={selectedType} onSelectedType={onSelectedType} />
      )}
      {propensityStep === PropensitySteps.TERMS && (
        <InvestorPropensityTerms terms={terms} onTermsChange={onTermsChange} />
      )}

      {isPropensityQuestionStep && (
        <PropensityQuestion
          currentQuestion={currentQuestion}
          answers={answers}
          handleSingleAnswer={handleSingleAnswer}
          handleMultiRatioAnswer={handleMultiRatioAnswer}
          handleMultiAnswer={handleMultiAnswer}
        />
      )}

      {isPropensityLastStep && <PropensityResult answers={answers} terms={terms} />}
      {
        <div className="mx-auto mb-20 mt-5 flex max-w-screen-test gap-2">
          <SecondaryButton
            onClick={handlePrev}
            text={
              isPropensityFirstStep
                ? '진단 그만하기'
                : isPropensityLastStep
                  ? '재진단 하기'
                  : '이전'
            }
            className="!h-[60px] w-full"
          />
          <PrimaryButton
            onClick={handlePropensityNext}
            disabled={isPropensityDisabled()}
            text={
              isPropensityFirstStep ? '진단 시작하기' : isPropensityLastStep ? '완료하기' : '다음'
            }
            className="!h-[60px] w-full"
          />
        </div>
      }
      {isPropensityFirstStep && <InvestorPropensityNotice className="!mb-0" />}
    </div>
  );
};
