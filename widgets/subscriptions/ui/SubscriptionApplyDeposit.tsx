import { AnimatePresence, motion } from 'framer-motion';
import { UseFormReturn } from 'react-hook-form';

import { SubscriptionApplyDepositCard } from '@/features/deposit/ui/SubscriptionApplyDepositCard';
import { SubscriptionApplyFormData } from '@/features/subscription/lib/SubscriptionApplySchema';

import { Account } from '@/entities/assets/types';
import { Subscription } from '@/entities/subscriptions/types';
import { ApplyProgressIndicator } from '@/entities/subscriptions/ui/ApplyProgressIndicator';

import { usePageHeader } from '@/shared/model/layouts/usePageHeader';
import { HeaderMode } from '@/shared/types';
import { Separator } from '@/shared/ui/shadcn/separator';

import { SubscriptionApplyAmountSummary } from './SubscriptionApplyAmountSummary';

interface SubscriptionApplyDepositProps {
  form: UseFormReturn<SubscriptionApplyFormData>;
  subscription?: Subscription;
  account?: Account;
  isLoadingMyAccount: boolean;
  refetchMyAccount: () => void;
  mobileProgressStep: () => number;
}

export const SubscriptionApplyDeposit = ({
  subscription,
  form,
  account,
  isLoadingMyAccount,
  refetchMyAccount,
  mobileProgressStep,
}: SubscriptionApplyDepositProps) => {
  usePageHeader({
    mode: HeaderMode.LIST,
    subTitle: '예치금 입금',
    rightComponent: <ApplyProgressIndicator current={mobileProgressStep()} total={4} />,
  });

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="mt-8 space-y-6 sm:mt-0 sm:space-y-12"
      >
        <SubscriptionApplyDepositCard
          account={account}
          isLoadingMyAccount={isLoadingMyAccount}
          refetchMyAccount={refetchMyAccount}
        />

        <Separator orientation="horizontal" className="h-3 bg-blue-gray-00 sm:hidden" />

        <SubscriptionApplyAmountSummary account={account} subscription={subscription} form={form} />
      </motion.div>
    </AnimatePresence>
  );
};
