import { ChevronRightIcon } from '@heroicons/react/24/solid';
import { useRouter } from 'next/navigation';

import { fetchMainSubscriptions } from '@/features/subscription/api/fetchMainSubscriptions';

import { SubscriptionsResponse } from '@/entities/subscriptions/interface';
import { MainSubscriptionCard } from '@/entities/subscriptions/ui/MainSubscriptionCard';

import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';
import { FallbackImage } from '@/shared/ui/FallbackImage';

interface MainSubscriptionsProps {
  subscriptions: SubscriptionsResponse;
}

export const MainSubscriptions = ({ subscriptions }: MainSubscriptionsProps) => {
  const router = useRouter();

  const { data: subscriptionList } = fetchMainSubscriptions(subscriptions);
  const { screenSize } = useScreenStore();

  // 최대 2개의 항목만 사용
  const displayedSubscriptions = () => {
    if (screenSize === ScreenSize.DESKTOP || screenSize === ScreenSize.MOBILE) {
      return subscriptionList?.list.slice(0, 1) || [];
    }

    return subscriptionList?.list.slice(0, 2) || [];
  };

  const goToSubscriptions = () => {
    if (screenSize === ScreenSize.MOBILE) {
      router.replace('/subscriptions');
    } else {
      router.push('/subscriptions');
    }
  };

  return (
    <section className="mx-auto max-w-screen-lg px-5 pb-[60px] pt-[60px] sm:px-8 sm:pb-20 tb:pb-[160px] ml:px-0 ml:pt-[120px]">
      <div className="flex items-center justify-between">
        <h2 className="text-20 sm:text-28 ml:text-32">뉴밋 공모중개 상품</h2>
        <div className="flex items-center font-semibold text-gray-500">
          <button onClick={goToSubscriptions} className="flex items-center text-sm sm:text-base">
            전체보기
            <ChevronRightIcon className="h-4 w-4" strokeWidth={2} />
          </button>
        </div>
      </div>
      <div className={`mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 tb:grid-cols-1 ml:mt-12`}>
        {displayedSubscriptions().map((subscription) => (
          <MainSubscriptionCard key={subscription.subscriptionInfoId} subscription={subscription} />
        ))}

        {displayedSubscriptions().length === 1 && screenSize === ScreenSize.TABLET && (
          <div className="flex flex-col rounded-[20px] border border-gray-300">
            <div className="flex h-[215px] items-center justify-center rounded-t-[20px] bg-blue-gray-00">
              <FallbackImage src="/logo/opacity_logo.png" alt="logo" width={118} height={28} />
            </div>
            <div className="flex h-[230px] items-center justify-center text-lg text-blue-gray-100 tb:h-[316px]">
              더 많은 상품을 준비중입니다.
            </div>
          </div>
        )}
      </div>
    </section>
  );
};
