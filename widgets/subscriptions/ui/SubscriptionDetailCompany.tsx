import React from 'react';

import { SubscriptionIssuer } from '@/entities/subscriptions/types';
import { SubscriptionDetailItem } from '@/entities/subscriptions/ui/SubscriptionDetailItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionDetailCompanyProps {
  issuer: SubscriptionIssuer;
}

export const SubscriptionDetailCompany = ({ issuer }: SubscriptionDetailCompanyProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="mx-auto max-w-[800px] space-y-2 px-6 sm:mb-14 sm:space-y-4 sm:px-8 ml:max-w-screen-contents ml:px-0">
      <h4 className="text-18 sm:text-20">기업 정보</h4>
      <Separator className="my-4 bg-gray-200 sm:hidden" orientation="horizontal" />
      <div className="flex flex-col rounded-[20px] border-gray-300 sm:border sm:px-8 sm:py-4 ml:flex-row ml:gap-10">
        <div className="flex items-center gap-4">
          <div className="flex h-10 w-10 items-center justify-center bg-white sm:h-40 sm:w-40">
            <FallbackImage
              src="/logo/numit_logo.png"
              alt="company"
              width={30}
              height={8}
              className="h-2 w-[30px] sm:h-[30px] sm:w-[120px]"
            />
          </div>

          <h4 className="text-lg font-bold ml:hidden">{issuer.name}</h4>
        </div>
        <div className="flex w-full flex-col space-y-2 py-4 sm:space-y-4">
          <h4 className="hidden text-lg font-bold ml:block">{issuer.name}</h4>
          <div className="space-y-2 sm:space-y-2">
            <div className="flex flex-col gap-[10px] sm:flex-row sm:gap-2">
              <SubscriptionDetailItem label="기업명" value={issuer.name} />
              <SubscriptionDetailItem label="주소" value="서울시 뉴밋구 뉴밋동 뉴밋빌딩 101호" />
            </div>
            <div className="flex flex-col gap-[10px] sm:flex-row sm:gap-2">
              <SubscriptionDetailItem label="대표명" value={issuer.name} />
              <SubscriptionDetailItem label="이메일" value={issuer.email} />
            </div>
            <div className="flex flex-col gap-[10px] sm:flex-row sm:gap-2">
              <SubscriptionDetailItem label="설립일" value={YYYYMMDD(issuer.createdAt) || ''} />
              <SubscriptionDetailItem label="홈페이지" value="https://www.numit.kr" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
