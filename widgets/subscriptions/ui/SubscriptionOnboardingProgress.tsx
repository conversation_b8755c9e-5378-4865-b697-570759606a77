import { SubscriptionOnboardingSteps } from '@/entities/subscriptions/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

interface SubscriptionOnboardingProgressProps {
  currentStep: SubscriptionOnboardingSteps;
}

export const SubscriptionOnboardingProgress = ({
  currentStep,
}: SubscriptionOnboardingProgressProps) => {
  const steps = Object.values(SubscriptionOnboardingSteps);

  const labels = ['본인 실명인증', '예치금 가상계좌 계설', '투자성향 진단', '투자 적합성 테스트'];

  const getStepAlignment = (index: number) => {
    switch (index) {
      case 1:
        return 'left-[-36px]';
      case 2:
        return 'left-[-16px]';
      case 3:
        return 'left-[-28px]';
      default:
        return 'sm:left-0 left-[-16px]';
    }
  };

  return (
    <div className="w-full max-w-[700px]">
      <div className="flex">
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const isFirst = index === 0;

          const isCurrent = step === currentStep;
          const isDone = steps.indexOf(step) < steps.indexOf(currentStep);
          const isUpcoming = steps.indexOf(step) > steps.indexOf(currentStep);

          return (
            <div key={step} className={`relative flex flex-col ${!isLast && 'flex-1'}`}>
              <div
                className={`flex w-full items-center ${isFirst ? 'sm:pl-5' : isLast ? 'sm:pr-5' : ''}`}
              >
                {isCurrent && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-500 font-bold text-white ring-8 ring-primary-200/20">
                    {index + 1}
                  </div>
                )}
                {isDone && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-500 font-bold text-white">
                    <FallbackImage
                      src="/icons/check_white.png"
                      alt="check"
                      width={16}
                      height={16}
                    />
                  </div>
                )}
                {isUpcoming && (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full border-2 border-gray-200 bg-gray-50 text-lg font-bold text-gray-300">
                    {index + 1}
                  </div>
                )}

                {!isLast && (
                  <div
                    className={`h-0.5 flex-1 border-t-2 ${isDone ? 'border-primary-500' : 'border-dashed border-gray-200'}`}
                  />
                )}
              </div>
              <div
                className={`absolute top-12 flex whitespace-nowrap text-xs font-semibold text-gray-400 sm:text-sm ${getStepAlignment(index)}`}
              >
                {labels[index]}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
