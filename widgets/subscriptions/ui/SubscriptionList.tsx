import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react';

import { fetchSubscriptions } from '@/features/subscription/api/fetchSubscriptions';

import { SubscriptionsResponse } from '@/entities/subscriptions/interface';
import { SubscriptionBizStatus, SubscriptionSortType } from '@/entities/subscriptions/types';
import { SubscriptionCard } from '@/entities/subscriptions/ui/SubscriptionCard';
import { SubscriptionCardSkeleton } from '@/entities/subscriptions/ui/SubscriptionCardSkeleton';

import { SecondaryButton } from '@/shared/ui/SecondaryButton';

interface SubscriptionListProps {
  title: string;
  status?: SubscriptionBizStatus;
  placeholder?: string;
  subscriptions?: SubscriptionsResponse;
  isSort?: boolean;
  isMore?: boolean;
}

export const SubscriptionList = ({
  title,
  status,
  placeholder,
  subscriptions,
  isSort = false,
  isMore = false,
}: SubscriptionListProps) => {
  const searchParams = useSearchParams();
  const sortType = searchParams.get('sortType');
  const [size, setSize] = useState(3);

  const filterSortType =
    isSort && sortType && sortType !== 'LATEST' ? (sortType as SubscriptionSortType) : undefined;

  const {
    data: subscriptionList,
    isLoading,
    isFetching,
  } = fetchSubscriptions({
    filters: {
      page: 0,
      size,
      subscriptionInfoStatusList: status,
      sortType: filterSortType,
    },
    subscriptions:
      (isSort && sortType && sortType !== 'LATEST') || size > 3 ? undefined : subscriptions,
  });

  const isShowMore = Boolean(
    subscriptions?.totalCount && subscriptions?.totalCount > size && isMore,
  );

  return (
    <div className="container space-y-6 sm:space-y-8">
      <h3 className="text-20 sm:text-28">{title}</h3>
      <div className="grid grid-cols-1 gap-12 sm:grid-cols-2 ml:grid-cols-3">
        {isLoading
          ? Array.from({ length: 3 }).map((_, index) => <SubscriptionCardSkeleton key={index} />)
          : subscriptionList?.list?.map((subscription) => (
              <SubscriptionCard key={subscription.subscriptionInfoId} subscription={subscription} />
            ))}
        {subscriptionList?.list?.length === 0 && (
          <div className="col-span-full flex h-[230px] items-center justify-center rounded-[20px] border border-gray-50 bg-gray-50">
            <p className="break-keep text-center text-lg text-gray-400">{placeholder}</p>
          </div>
        )}
        {isFetching &&
          Array.from({ length: 6 }).map((_, index) => <SubscriptionCardSkeleton key={index} />)}
      </div>
      {isShowMore && (
        <div className="mt-10 flex justify-center sm:mt-[100px]">
          <SecondaryButton
            text="더보기"
            onClick={() => setSize(size + 6)}
            className="h-[44px] w-20 border-gray-300 sm:!h-[60px] sm:w-40"
          />
        </div>
      )}
    </div>
  );
};
