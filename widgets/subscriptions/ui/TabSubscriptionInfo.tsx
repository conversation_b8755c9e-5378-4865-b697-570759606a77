import { DocumentTextIcon } from '@heroicons/react/24/outline';
import { ArrowDownTrayIcon } from '@heroicons/react/24/solid';
import React from 'react';

import { useSubscriptionMainImage } from '@/features/subscription/model/useSubscriptionImage';
import { DetailInfoNavigate } from '@/features/subscription/ui/DetailInfoNavigate';
import { DetailMobileInfoTabs } from '@/features/subscription/ui/DetailMobileInfoTabs';

import {
  DocsType,
  DocsTypeLabel,
  RequiredDocsDetail,
  SubscriptionCmsDetail,
} from '@/entities/subscriptions/types';
import { DetailInfoItem } from '@/entities/subscriptions/ui/DetailInfoItem';

import { ListResponse } from '@/shared/interface';
import { useFileDownload } from '@/shared/model/useFileDownload';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { Loading } from '@/shared/ui/Loading';
import { Separator } from '@/shared/ui/shadcn/separator';
import { TabsContent } from '@/shared/ui/shadcn/tabs';

interface TabSubscriptionInfoProps {
  attachFilesRef: React.RefObject<HTMLDivElement | null>;
  subscriptionCmsDetail?: ListResponse<SubscriptionCmsDetail>;
  isLoading: boolean;
  securitiesId: string;
  requiredDocs: RequiredDocsDetail[];
}

export const TabSubscriptionInfo = ({
  attachFilesRef,
  subscriptionCmsDetail,
  isLoading,
  securitiesId,
  requiredDocs,
}: TabSubscriptionInfoProps) => {
  const { handleFileDownload } = useFileDownload();

  if (isLoading) {
    return (
      <TabsContent value="info" className="my-10 sm:my-20">
        <Loading />
      </TabsContent>
    );
  }

  return (
    <TabsContent value="info" className="my-0 sm:my-20">
      <div className="relative mx-auto max-w-[840px] gap-[30px] px-6 sm:px-8 ml:px-0">
        <DetailMobileInfoTabs subscriptionCmsDetail={subscriptionCmsDetail} />
        {!!subscriptionCmsDetail?.meta.total && subscriptionCmsDetail?.meta.total > 0 && (
          <DetailInfoNavigate />
        )}

        {subscriptionCmsDetail?.data[0]?.summaryVisible && (
          <DetailInfoItem
            title="투자상품 요약"
            content={subscriptionCmsDetail?.data[0]?.summary}
            id="summary"
          />
        )}

        {subscriptionCmsDetail?.data[0]?.investmentPointVisible && (
          <>
            <Separator className="my-8 bg-gray-200 sm:my-20" orientation="horizontal" />

            <DetailInfoItem
              title="투자 포인트"
              content={subscriptionCmsDetail?.data[0]?.investmentPoint}
              id="points"
            />
          </>
        )}

        {subscriptionCmsDetail?.data[0]?.assetDescribeVisible && (
          <>
            <Separator className="my-8 bg-gray-200 sm:my-20" orientation="horizontal" />

            <DetailInfoItem
              title="기초자산 소개"
              content={subscriptionCmsDetail?.data[0]?.assetDescribe}
              id="assets"
            />
          </>
        )}

        {subscriptionCmsDetail?.data[0]?.profitStructureVisible && (
          <>
            <Separator className="my-8 bg-gray-200 sm:my-20" orientation="horizontal" />
            <DetailInfoItem
              title="수익구조"
              content={subscriptionCmsDetail.data[0]?.profitStructure}
              id="profit"
            />
          </>
        )}

        {subscriptionCmsDetail?.data[0]?.operatorCompetitivenessVisible && (
          <>
            <Separator className="my-8 bg-gray-200 sm:my-20" orientation="horizontal" />

            <DetailInfoItem
              title="운용사업자 경쟁력"
              content={subscriptionCmsDetail?.data[0]?.operatorCompetitiveness}
              id="competitiveness"
            />
          </>
        )}
        {subscriptionCmsDetail?.data[0]?.investmentConsiderationVisible && (
          <>
            <Separator className="my-8 bg-gray-200 sm:my-20" orientation="horizontal" />

            <DetailInfoItem
              title="투자 유의 사항"
              content={subscriptionCmsDetail?.data[0]?.investmentConsideration}
              id="caution"
            />
          </>
        )}
      </div>

      {requiredDocs.length > 0 && (
        <>
          <Separator className="my-10 h-4 bg-blue-gray-00 sm:my-20" />

          <div
            ref={attachFilesRef}
            className="attach-files mx-auto mb-20 mt-10 max-w-[840px] space-y-4 px-6 sm:my-20 sm:px-8 ml:max-w-screen-contents ml:px-0"
          >
            <h3 className="text-18 sm:text-20">첨부파일 다운로드</h3>
            <div className="space-y-2">
              {requiredDocs?.map((docs) => {
                if (!docs._id) return null;

                return (
                  <div
                    key={docs._id}
                    className="flex w-full items-center gap-[10px] rounded-lg border border-gray-300 p-4 text-sm sm:py-[22px] sm:text-base"
                  >
                    <DocumentTextIcon className="h-5 w-5" />
                    <div className="flex flex-1 gap-[10px]">
                      <p className="line-clamp-1">{docs.metadata?.title}</p>
                      <p className="line-clamp-1 text-gray-500">
                        {DocsTypeLabel[docs.metadata?.code as DocsType]}
                      </p>
                    </div>
                    <button
                      onClick={() => handleFileDownload(docs)}
                      className="flex items-center justify-center rounded p-2 transition-colors hover:bg-gray-100"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </>
      )}
    </TabsContent>
  );
};
