import { SubscriptionApplyInfoItem } from '@/entities/subscriptions/ui/SubscriptionApplyItem';

interface SubscriptionApplyInfoProps {
  title: string;
  items: { label: string; value: string }[];
  className?: string;
}

export const SubscriptionApplyInfo = ({ items, title, className }: SubscriptionApplyInfoProps) => {
  return (
    <div className="space-y-5 px-6 sm:space-y-3 sm:px-8 ml:px-0">
      <h4 className="sm:text-20 text-base font-semibold">{title}</h4>
      <div
        className={`space-y-3 rounded-lg border-gray-300 sm:space-y-6 sm:border sm:p-8 ${className}`}
      >
        {items.map((item) => (
          <SubscriptionApplyInfoItem key={item.label} label={item.label} value={item.value} />
        ))}
      </div>
    </div>
  );
};
