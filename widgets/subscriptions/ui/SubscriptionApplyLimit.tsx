import { ChevronRightIcon, InformationCircleIcon } from '@heroicons/react/24/solid';
import { usePathname } from 'next/navigation';
import React from 'react';

import { useFetchUser } from '@/features/users/model/useFetchUser';

import { InvestorLimit } from '@/entities/investor/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { useScreenStore } from '@/shared/store/useScreenStore';
import { ScreenSize } from '@/shared/types';

interface SubscriptionApplyLimitProps {
  investorLimit?: InvestorLimit;
}

export const SubscriptionApplyLimit = ({ investorLimit }: SubscriptionApplyLimitProps) => {
  const { CASHCOMMA } = utilFormats();
  const { user, isGeneral } = useFetchUser();
  const { routerPush } = useWebViewRouter();
  const { screenSize } = useScreenStore();
  const pathname = usePathname();

  const handleChangeInvestorType = () => {
    if (screenSize === ScreenSize.MOBILE) {
      routerPush(`/mobile/user/apply-investor?callbackUrl=${pathname}`);
    } else {
      routerPush(`/user/my/investment-settings?callbackUrl=${pathname}`);
    }
  };

  return (
    <div className="space-y-2 sm:space-y-3">
      <div className="hidden items-center justify-between sm:flex">
        <p className="flex items-center gap-1 text-sm">
          <InformationCircleIcon className="h-4 w-4 text-gray-900" />
          투자자 유형을 변경하여 <strong className="font-semibold">더 높은 한도로 투자</strong>해
          보세요.
        </p>
        <button
          onClick={handleChangeInvestorType}
          className="text-sm font-semibold text-primary-500"
        >
          변경하기
        </button>
      </div>
      <div className="flex h-[90px] w-full flex-col items-center justify-center gap-1 rounded-lg border border-gray-300 bg-primary-00 px-8 sm:h-[100px] sm:flex-row sm:justify-between sm:gap-0">
        <span className="text-sm font-semibold sm:text-base">
          {isGeneral ? user?.userProfile?.name : user?.userCorporateProfile?.representativeName}님의
          크라우드펀딩 투자 한도
        </span>
        <span className="sm:text-24 font-semibold text-primary-500">
          {CASHCOMMA(investorLimit?.issuerInvestLimitAmount || 0)}원
        </span>
      </div>
      <div className="flex items-center justify-between rounded-lg border border-black/[0.04] bg-blue-gray-00 px-[14px] py-2 text-xs sm:hidden">
        <p className="text-gray-600">
          투자자 유형을 변경하여 <strong className="font-semibold">더 높은 한도로 투자</strong>해
          보세요.
        </p>
        <button onClick={handleChangeInvestorType}>
          <ChevronRightIcon className="h-[14px] w-[14px]" strokeWidth={2.5} />
        </button>
      </div>
    </div>
  );
};
