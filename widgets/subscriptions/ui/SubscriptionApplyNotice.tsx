import React from 'react';

export const SubscriptionApplyNotice = () => {
  return (
    <div className="px-6 sm:px-8 ml:px-0">
      <div className="space-y-4 rounded-lg bg-gray-50 p-5 sm:p-8">
        <h5 className="text-sm font-semibold text-gray-700">꼭 알아두세요</h5>
        <ol className="list-outside list-disc space-y-4 pl-5 leading-[150%] text-gray-600 sm:leading-[170%]">
          <li>
            본 증권은 청약 완료순(선착순)으로 배정됩니다. 발행되는 증권의 주요내용은 첨부파일의
            '사채청약서'를 참고하시길 바랍니다.
          </li>
          <li>청약이 종료되면 청약을 취소할 수 없습니다.</li>
          <li>
            청약 페이지 내용은 청약 종료 7일 전까지 정정할 수 있으며, 정정 사항은 페이지 게시 및
            투자자에게 알림이 발송됩니다. 투자자는 정정된 사항을 반드시 확인하고 청약 유지 또는
            취소를 결정해야 합니다.
          </li>
        </ol>
      </div>
    </div>
  );
};
