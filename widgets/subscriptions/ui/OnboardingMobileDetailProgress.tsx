import { ChevronLeftIcon } from '@heroicons/react/24/solid';
import React from 'react';

export interface OnboardingStep {
  label: string;
  step: any;
}

interface OnboardingMobileDetailProgressProps {
  steps: OnboardingStep[];
  currentStep: any;
  className?: string;
}

export const OnboardingMobileDetailProgress = ({
  steps,
  currentStep,
  className = '',
}: OnboardingMobileDetailProgressProps) => {
  const stepNumbers = steps.map((step) => step.step);

  const isCompleted = (step: string) => {
    const currentStepIndex = stepNumbers.findIndex((s) => s === currentStep);
    const stepIndex = stepNumbers.findIndex((s) => s === step);
    return stepIndex <= currentStepIndex && stepIndex !== -1;
  };

  return (
    <div
      className={`w-full ${steps.length === 4 ? 'flex-col' : 'flex-row'} hidden justify-between sm:flex sm:px-4 ml:hidden ${className}`}
    >
      <div className="flex gap-4 sm:gap-8">
        {steps.map((step) => (
          <React.Fragment key={step.label}>
            <div className="flex h-8 items-center gap-3 text-xs sm:text-sm">
              <div
                className={`h-2 w-2 rounded-full ${isCompleted(step.step) ? 'bg-black' : 'bg-gray-300'}`}
              />
              <p className={`font-semibold ${isCompleted(step.step) ? '' : 'text-gray-400'}`}>
                {step.label}
              </p>
            </div>
          </React.Fragment>
        ))}
      </div>
      <div className={`${steps.length === 4 ? 'mt-4 flex justify-end' : ''}`}>
        <button className="flex items-center text-xs font-semibold text-gray-500 underline">
          <ChevronLeftIcon className="h-4 w-4" />
          그만하고 나가기
        </button>
      </div>
    </div>
  );
};
