import { ShareIcon } from '@heroicons/react/24/outline';
import { BellIcon } from '@heroicons/react/24/outline';

import { useSubscriptionMainImage } from '@/features/subscription/model/useSubscriptionImage';

import { Subscription, SubscriptionBizStatus } from '@/entities/subscriptions/types';
import { SubscriptionDetailHeaderItem } from '@/entities/subscriptions/ui/SubscriptionDetailHeaderItem';

import { utilFormats } from '@/shared/lib/utilformats';
import { useClipboard } from '@/shared/model/useClipboard';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Progress } from '@/shared/ui/shadcn/progress';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionDetailHeaderProps {
  subscription?: Subscription;
  goToApply: () => void;
  progress: number;
  applyTotalMargin?: number;
  isEnded: boolean;
  isWait: boolean;
  isLoading: boolean;
}

const SubscriptionDetailHeader = ({
  subscription,
  goToApply,
  progress,
  applyTotalMargin,
  isEnded,
  isWait,
  isLoading,
}: SubscriptionDetailHeaderProps) => {
  const { CASHCOMMA, YYYYMMDD, DAYFROMNOW } = utilFormats();
  const { handleCopy } = useClipboard();

  const { mainImage } = useSubscriptionMainImage(subscription?.securities.securitiesId as string);

  return (
    <>
      <div
        style={{
          backgroundImage: `url(${mainImage?.image.url || '/images/subscription_image.png'})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
        className={`flex h-[184px] flex-col justify-end bg-cover bg-center bg-no-repeat sm:h-[360px] ml:hidden`}
      >
        <div className="mx-auto hidden w-full max-w-[800px] px-6 pb-10 text-white sm:block sm:px-8">
          <div className="flex gap-6">
            <div className="flex h-[90px] w-[90px] items-center justify-center rounded-md bg-white">
              <FallbackImage
                src="/logo/numit_logo.png"
                alt="subscription_image"
                width={80}
                height={80}
              />
            </div>
            <div>
              <h6 className="font-semibold">{subscription?.issuer.name}</h6>
              <h2 className="text-32">{subscription?.securities.securitiesName}</h2>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white pb-0 sm:bg-gray-50 sm:pb-20 ml:pt-[60px]">
        <div className="mx-auto max-w-[800px] px-6 sm:px-8 ml:max-w-screen-contents ml:px-0">
          <div className="sm:hidden">
            <div className="flex items-center gap-[10px] pt-5">
              <div className="flex h-[32px] w-[32px] items-center justify-center rounded border border-gray-300 bg-white">
                <FallbackImage
                  src="/logo/numit_logo.png"
                  alt="subscription_image"
                  width={24}
                  height={24}
                  className="object-corver"
                />
              </div>
              <h6 className="text-sm font-semibold">{subscription?.issuer.name}</h6>
            </div>
            <h2 className="text-20">{subscription?.securities.securitiesName}</h2>
          </div>

          <div className="hidden gap-6 ml:flex">
            <div className="flex h-[90px] w-[90px] items-center justify-center bg-white">
              <FallbackImage
                src="/logo/numit_logo.png"
                alt="subscription_image"
                width={80}
                height={80}
              />
            </div>
            <div>
              <h6 className="font-semibold text-gray-700">{subscription?.issuer.name}</h6>
              <h2 className="text-40">{subscription?.securities.securitiesName}</h2>
            </div>
          </div>

          <div className="flex gap-12 pt-[10px] sm:pt-8">
            <FallbackImage
              src={mainImage?.image.url || '/images/subscription_image.png'}
              alt="subscription_image"
              width={590}
              className="hidden rounded-[20px] ml:block"
              height={380}
            />

            <div className="flex-1 space-y-6 sm:space-y-10">
              {/* Todo: 태그 임시 제거 */}
              {/* <div className="flex gap-[6px]">
                <span className="rounded bg-gray-100 px-3 py-1 text-sm font-semibold text-gray-500">
                  태그
                </span>
                <span className="rounded bg-gray-100 px-3 py-1 text-sm font-semibold text-gray-500">
                  태그
                </span>
              </div> */}

              <div className="space-y-2 sm:hidden sm:px-0">
                <div className="flex gap-[6px]">
                  <p>달성률</p>
                  <strong className="text-primary-500">{progress}%</strong>
                </div>
                <Progress value={progress} className="h-[10px] bg-blue-gray-50 !text-primary-500" />
              </div>
              <Separator className="bg-gray-200 sm:hidden" orientation="horizontal" />

              <div className="space-y-[10px] sm:px-0">
                <SubscriptionDetailHeaderItem
                  label="마감일"
                  value={`${YYYYMMDD(subscription?.endAt, 'YYYY년 MM월 DD일 hh시')}까지 (D${DAYFROMNOW(subscription?.endAt)})`}
                />
                <SubscriptionDetailHeaderItem
                  label="목표금액"
                  value={`총 ${CASHCOMMA(subscription?.offeringTotalAmount || 0)}원`}
                />
                <SubscriptionDetailHeaderItem
                  label="현재 총 모집 금액"
                  value={`${CASHCOMMA(applyTotalMargin || 0)}원`}
                />
              </div>
              <div className="hidden space-y-2 sm:block sm:px-0">
                <div className="flex gap-[6px]">
                  <p>달성률</p>
                  <strong className="text-primary-500">{progress}%</strong>
                </div>
                <Progress value={progress} className="h-[10px] bg-blue-gray-50 !text-primary-500" />
              </div>
              <div className="hidden items-center gap-2 px-6 sm:flex sm:px-0">
                {isWait ? (
                  <Button className="!h-[60px] w-full bg-gray-900 font-semibold text-white">
                    <BellIcon className="h-5 w-5 text-white" /> 알람 신청하기
                  </Button>
                ) : (
                  <PrimaryButton
                    onClick={goToApply}
                    disabled={isEnded || isLoading}
                    text={isEnded ? '투자종료' : '투자하기'}
                    className="!h-[60px] w-full font-semibold"
                  />
                )}

                <Button
                  onClick={() => handleCopy(window.location.href)}
                  className="!h-[60px] min-w-[60px] rounded-lg border border-gray-300"
                >
                  <ShareIcon />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SubscriptionDetailHeader;
