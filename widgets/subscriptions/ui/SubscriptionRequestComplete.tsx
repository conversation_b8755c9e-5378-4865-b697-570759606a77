'use client';

import React from 'react';

import { CommonMotionProvider } from '@/shared/ui/CommonMotionProvider';
import { FallbackImage } from '@/shared/ui/FallbackImage';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';

interface TestCompleteProps {
  goToCallbackUrl: () => void;
}

export const SubscriptionRequestComplete = ({ goToCallbackUrl }: TestCompleteProps) => {
  return (
    <CommonMotionProvider
      className={`mx-auto flex h-[calc(100vh-100px)] max-w-screen-test flex-col justify-center sm:mt-[200px] sm:h-auto sm:items-center sm:justify-start`}
    >
      <div className="flex flex-col items-center justify-center">
        <FallbackImage src="/icons/complete.png" alt="complete" width={80} height={80} />

        <h3 className="text-20 sm:text-24 ml:text-26 mb-5 mt-[34px]">
          투자 의뢰 신청이 <strong className="font-semibold text-primary-500">완료</strong>
          되었습니다.
        </h3>
        <h6 className="whitespace-pre-line break-keep text-center text-base sm:text-lg">
          뉴밋 담당자 확인 후 입력하신 이메일로 진행 절차를 안내해 드리며 필요한 경우 뉴밋 담당자가
          직접 연락을 드릴 수 있습니다. 추가 문의 사항이 있으신 경우, 뉴밋 고객센터(1600-8625)로
          문의해 주시기 바랍니다.
        </h6>
      </div>

      <div className="fixed bottom-0 left-0 w-full px-6 py-2 sm:hidden">
        <PrimaryButton text="확인" className="!h-12 w-full text-base" onClick={goToCallbackUrl} />
      </div>

      <PrimaryButton
        text="확인"
        className="hidden w-full sm:mb-[280px] sm:mt-16 sm:block"
        onClick={goToCallbackUrl}
      />
    </CommonMotionProvider>
  );
};
