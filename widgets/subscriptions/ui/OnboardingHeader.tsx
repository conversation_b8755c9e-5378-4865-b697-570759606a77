import { SubscriptionOnboardingSteps } from '@/entities/subscriptions/types';

import { FallbackImage } from '@/shared/ui/FallbackImage';

import { SubscriptionOnboardingProgress } from './SubscriptionOnboardingProgress';

interface OnboardingHeaderProps {
  step: SubscriptionOnboardingSteps;
  text?: string;
}

export const OnboardingHeader = ({
  step,
  text = '투자 서비스를 위해 절차를 진행해 주세요.',
}: OnboardingHeaderProps) => {
  return (
    <header className="hidden h-[264px] w-full rounded-t-lg border-b border-[#E0E0E0] bg-white px-8 sm:flex sm:px-10 ml:h-[140px] ml:items-center">
      <div className="mx-auto flex w-full max-w-screen-lg flex-col items-center gap-6 ml:flex-row ml:gap-12">
        <div className="relative mt-6 ml:hidden">
          <div className="relative h-[84px] w-[360px]">
            <FallbackImage
              src="/images/onboarding_message_md.png"
              alt="onboarding message"
              fill
              className="hidden object-cover sm:block"
              sizes="360px"
            />
            <FallbackImage
              src="/images/onboarding_message_mobile.png"
              alt="onboarding message"
              fill
              className="block object-cover sm:hidden"
              sizes="360px"
            />
          </div>
          <p className="absolute left-[34px] top-1/2 -translate-y-1/2 text-lg font-semibold">
            {text}
          </p>
        </div>
        <div className="relative hidden ml:block">
          <div className="relative h-[84px] w-[376px]">
            <FallbackImage
              src="/images/onboarding_message.png"
              alt="onboarding message"
              fill
              className="object-cover"
              sizes="376px"
            />
          </div>
          <p className="absolute left-[34px] top-1/2 -translate-y-1/2 text-lg font-semibold">
            {text}
          </p>
        </div>
        <SubscriptionOnboardingProgress currentStep={step} />
      </div>
    </header>
  );
};
