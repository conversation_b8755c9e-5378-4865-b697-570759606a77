import { useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';

import { queries } from '@/features/lib/queries';
import { fetchTerms } from '@/features/terms/api/fetchTerms';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { Subscription } from '@/entities/subscriptions/types';
import { TermsCategory } from '@/entities/terms/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { Loading } from '@/shared/ui/Loading';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Input } from '@/shared/ui/shadcn/input';
import { Separator } from '@/shared/ui/shadcn/separator';

interface SubscriptionRiskNoticeProps {
  handleAgree: () => void;
  subscription?: Subscription;
}

export const SubscriptionRiskNotice = ({
  handleAgree,
  subscription,
}: SubscriptionRiskNoticeProps) => {
  const { YYYYMMDD } = utilFormats();
  const [name, setName] = useState('');
  const { username } = useFetchUser();
  const queryClient = useQueryClient();
  const { routerBack } = useWebViewRouter();

  const { data: investmentRiskDisclosure, isLoading: isInvestmentRiskDisclosureLoading } =
    fetchTerms({
      category: TermsCategory.INVESTMENT_RISK_DISCLOSURE,
    });

  const prefetchInvestorLimit = async () => {
    if (!subscription?.issuer.id) return;

    try {
      await queryClient.prefetchQuery({
        queryKey: queries.investor.investorLimit(subscription?.issuer.id.toString()).queryKey,
        queryFn: queries.investor.investorLimit(subscription?.issuer.id.toString()).queryFn,
      });
    } catch (error) {
      // 에러 처리 로직 추가
      console.error('Failed to prefetch investor limit:', error);
    }
  };

  useEffect(() => {
    if (subscription) {
      prefetchInvestorLimit();
    }
  }, [subscription]);

  const disabled = name !== username;

  if (isInvestmentRiskDisclosureLoading) {
    return <Loading />;
  }

  return (
    <div className="bg-blue-gray-00 px-6 pb-32 pt-6 sm:px-8 sm:py-[100px] sm:pb-20 ml:px-0">
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          exit={{ opacity: 0 }}
          className="mx-auto max-w-screen-contents bg-white p-6 shadow-md sm:px-10 sm:py-20 ml:p-20"
        >
          <div
            dangerouslySetInnerHTML={{ __html: investmentRiskDisclosure?.data[0].content ?? '' }}
            className="ck-content"
          />

          <Separator className="my-8 h-[1px] bg-gray-300 sm:my-0" />
          <div className="space-y-4 text-center sm:my-14 sm:space-y-6">
            <h4 className="text-base font-semibold sm:text-lg">
              본인은 본 투자에 대하여 청약을 함에 있어 위와 같은 내용을 확인합니다.
            </h4>

            <h5 className="text-sm font-semibold sm:text-base">
              {YYYYMMDD(new Date(), 'YYYY년 M월 DD일 (ddd)')}
            </h5>
          </div>
          <div className="mt-8 flex sm:mt-0 sm:justify-end">
            <div className="flex items-center gap-3 px-5 sm:px-0">
              <label htmlFor="" className="block min-w-9">
                성명:
              </label>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="이름을 입력해주세요."
                className="w-full text-center sm:w-[210px] sm:px-8"
              />
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
      <div className="mt-20 hidden justify-center gap-2 sm:flex">
        <SecondaryButton onClick={routerBack} text="이전" className="!h-[60px] w-40" />
        <PrimaryButton
          disabled={disabled}
          onClick={handleAgree}
          text="다음"
          className="!h-[60px] w-40"
        />
      </div>
      <div className="fixed bottom-0 left-0 right-0 flex w-full justify-center gap-2 bg-white px-6 py-2 sm:hidden">
        <SecondaryButton text="이전" className="h-12 w-full text-base" />
        <PrimaryButton
          disabled={disabled}
          onClick={handleAgree}
          text="다음"
          className="h-12 w-full"
        />
      </div>
    </div>
  );
};
