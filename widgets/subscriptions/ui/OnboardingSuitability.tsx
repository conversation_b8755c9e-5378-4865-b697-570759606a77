import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

import { InvestorSuitabilityTestStatusBar } from '@/widgets/investor/ui/InvestorSuitabilityTestStatusBar';
import { TestNotice } from '@/widgets/investor/ui/TestNotice';

import { suitabilityNotices } from '@/features/investor/config';
import { SuitabilityTestForm } from '@/features/investor/ui/SuitabilityTestForm';
import { SubscriptionStartOnRectangleButton } from '@/features/subscription/ui/SubscriptionStartOnRectangleButton';

import { InvestorSuitabilityTestStep, Question } from '@/entities/investor/types';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { PrimaryButton } from '@/shared/ui/PrimaryButton';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { TestComplete } from '@/shared/ui/TestComplete';

interface OnboardingSuitabilityProps {
  testIndex: number;
  questions: Question[];
  handleSelectTest: ({
    questionNumber,
    answer,
  }: {
    questionNumber: number;
    answer: 'A' | 'B';
  }) => void;
  isSelected: (questionNumber: number) => 'A' | 'B' | null;
  selectedTest?: { questionNumber: number; answer: 'A' | 'B' }[];
  handleNextTest: () => void;
  isSuitabilityDisabled: boolean;
  isWrongAnswerVisible: boolean;
  toggleWrongAnswerVisibility: () => void;
  handleRetakeTest: () => void;
  handlePreviousTest: () => void;
  handleSuitabilityStep: (step: InvestorSuitabilityTestStep) => void;
  isSuitabilityStep: (step: InvestorSuitabilityTestStep) => boolean;
  goToCallbackUrl: () => void;
}

export const OnboardingSuitability = ({
  testIndex,
  questions,
  handleSelectTest,
  isSelected,
  selectedTest,
  handlePreviousTest,
  handleNextTest,
  isSuitabilityDisabled,
  isWrongAnswerVisible,
  toggleWrongAnswerVisibility,
  handleRetakeTest,
  isSuitabilityStep,
  handleSuitabilityStep,
  goToCallbackUrl,
}: OnboardingSuitabilityProps) => {
  if (isSuitabilityStep(InvestorSuitabilityTestStep.NOTICE)) {
    return (
      <TestNotice
        title="투자 적합성 테스트"
        description={`자본시장법에 따라 투자 서비스 전 투자 적합성 테스트가 필요합니다. \n 절차에 따라 진행해 주세요.`}
        onStartTest={() => handleSuitabilityStep(InvestorSuitabilityTestStep.QUESTION)}
        notices={suitabilityNotices}
        isOnboarding
      />
    );
  }

  if (isSuitabilityStep(InvestorSuitabilityTestStep.RESULT)) {
    return (
      <TestComplete
        title="투자 적합성 테스트 결과"
        description={`투자 적합성 테스트를 완료했습니다. \n 지금 바로 투자 서비스를 진행해 보세요.`}
        goToCallbackUrl={goToCallbackUrl}
        isOnboarding
      />
    );
  }

  return (
    <>
      <h2 className="text-40 mx-auto mt-[100px] hidden max-w-screen-test sm:block">
        투자 적합성 테스트
      </h2>
      <InvestorSuitabilityTestStatusBar testIndex={testIndex} totalQuestions={questions.length} />
      <AnimatePresence mode="wait">
        <motion.div
          key={testIndex}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="mx-auto w-full max-w-screen-test"
        >
          <SuitabilityTestForm
            question={questions[testIndex]?.questionText}
            options={questions[testIndex]?.options}
            onSelect={(answer) =>
              handleSelectTest({
                questionNumber: questions[testIndex]?.questionNumber,
                answer,
              })
            }
            isSelected={isSelected}
            selectedTest={selectedTest?.find(
              (test) => test.questionNumber === questions[testIndex]?.questionNumber,
            )}
          />
        </motion.div>
      </AnimatePresence>
      <div className="mx-auto mb-40 mt-5 hidden w-full max-w-screen-test gap-2 sm:flex">
        <SecondaryButton
          onClick={handlePreviousTest}
          text={testIndex === 0 ? '테스트 그만하기' : '이전 문제'}
          className="!h-[60px] w-full"
        />
        <PrimaryButton
          onClick={handleNextTest}
          disabled={isSuitabilityDisabled}
          text={testIndex === questions.length - 1 ? '결과 확인하기' : '다음 문제'}
          className="!h-[60px] w-full"
        />
      </div>
      <ConfirmDialog
        isOpen={isWrongAnswerVisible}
        handleOpen={toggleWrongAnswerVisibility}
        title={`${questions.length}개의 오답이 있습니다. \n 정답을 다시 확인해 주세요.`}
        handleAction={handleRetakeTest}
        text="확인"
        isCancelButton={false}
      />

      <div className="fixed bottom-0 left-0 flex w-full items-center gap-2 bg-white px-6 pb-9 pt-2 sm:hidden">
        <PrimaryButton
          onClick={handleNextTest}
          disabled={isSuitabilityDisabled}
          text={testIndex === questions.length - 1 ? '결과 확인하기' : '다음 문제'}
          className="h-12 w-full text-base"
        />
        <SubscriptionStartOnRectangleButton />
      </div>
    </>
  );
};
