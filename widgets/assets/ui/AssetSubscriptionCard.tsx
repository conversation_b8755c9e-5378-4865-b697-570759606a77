import React, { useState } from 'react';

import { useWithdrawSubscription } from '@/features/subscription/model/useWithdrawSubscription';
import { MyApplySubscriptionDialog } from '@/features/subscription/ui/MyApplySubscriptionDialog';

import { AssetCardItem } from '@/entities/assets/ui/AssetCardItem';
import {
  MySubscription,
  SecuritiesType,
  SecuritiesTypeLabel,
  SubscriptionApplyStatusList,
} from '@/entities/subscriptions/types';
import { ApplyStatusTag } from '@/entities/subscriptions/ui/ApplyStatusTag';

import { utilFormats } from '@/shared/lib/utilformats';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';
import { Button } from '@/shared/ui/shadcn/button';
import { Separator } from '@/shared/ui/shadcn/separator';

interface AssetSubscriptionCardProps {
  subscription: MySubscription;
  isAsset?: boolean;
}

export const AssetSubscriptionCard = ({ subscription, isAsset }: AssetSubscriptionCardProps) => {
  const { isVisible, toggleVisibility } = useVisibility();
  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState<number | null>(null);
  const { handleWithdraw, isWithdrawLoading } = useWithdrawSubscription();
  const { routerPush } = useWebViewRouter();

  const { CASHCOMMA, YYYYMMDD } = utilFormats();

  const withdrawSubscription = async () => {
    if (!selectedSubscriptionId) return;

    await handleWithdraw(selectedSubscriptionId);
    onCloseDialog();
  };

  const onCloseDialog = () => {
    toggleVisibility();
    setSelectedSubscriptionId(null);
  };

  const isApply =
    subscription.subscriptionApplyStatus === SubscriptionApplyStatusList.APPLY ||
    subscription.subscriptionApplyStatus === SubscriptionApplyStatusList.WAIT;

  const isFailed =
    subscription.subscriptionApplyStatus === SubscriptionApplyStatusList.NOT_ALLOT ||
    subscription.subscriptionApplyStatus === SubscriptionApplyStatusList.REFUND_WAIT ||
    subscription.subscriptionApplyStatus === SubscriptionApplyStatusList.REFUND_DONE;

  return (
    <div
      onClick={() =>
        routerPush(`/subscriptions/${subscription.subscriptionInfo.subscriptionInfoId}`)
      }
      className="flex cursor-pointer flex-col items-center rounded-[20px] border border-gray-300 p-5 sm:h-[253px] sm:flex-row sm:p-8"
    >
      <div className="flex h-full w-full flex-1 flex-col justify-between">
        <div className="space-y-4">
          <div className="flex items-center gap-[6px]">
            <span className="inline-block rounded bg-gray-200 px-2 py-[3px] text-xs font-semibold leading-[150%]">
              {
                SecuritiesTypeLabel[
                  subscription.subscriptionInfo.securities.securitiesType as SecuritiesType
                ]
              }
            </span>
            <ApplyStatusTag status={subscription.subscriptionApplyStatus} />
            <h5 className="text-xs font-semibold text-gray-500">주식회사 한울</h5>
          </div>
          <div className="space-y-2">
            <p className="line-clamp-2 break-keep text-lg font-bold">
              {subscription?.subscriptionInfo.securities.securitiesName}
            </p>
            <div className="mt-1 flex gap-[6px]">
              <span className="rounded bg-gray-100 px-[6px] py-[3px] text-xs font-semibold text-gray-500">
                태그
              </span>
              <span className="bg-gray-100 px-[6px] py-[3px] text-xs font-semibold text-gray-500">
                태그
              </span>
            </div>
          </div>
        </div>
        {isAsset && (
          <p className="mt-4 text-lg sm:mt-0">
            총 발행 금액{' '}
            <strong className="font-semibold">
              {CASHCOMMA(subscription?.subscriptionInfo.offeringTotalAmount)}원
            </strong>
          </p>
        )}
      </div>
      <Separator orientation="vertical" className="hidden h-[189px] bg-gray-200 sm:mx-8 sm:block" />
      <Separator
        orientation="horizontal"
        className="my-[18px] bg-gray-200 sm:my-6 sm:hidden ml:mx-8"
      />
      <div className="h-full w-full flex-1 space-y-4">
        <div className="flex items-center justify-between ml:items-start">
          <p className="text-sm font-semibold">나의 자산 내역</p>
          {!isAsset && (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                setSelectedSubscriptionId(subscription.subscriptionApplyId);
                toggleVisibility();
              }}
              className="border-300 hidden !h-9 border text-xs sm:block"
            >
              상세보기
            </Button>
          )}
        </div>
        <div className="space-y-2">
          <AssetCardItem
            label={isApply ? '청약 증거금' : isFailed ? '반환된 증거금' : '청약 완료 금액'}
            value={`${CASHCOMMA(subscription.applyQuantity * subscription.subscriptionInfo.offeringPrice)}원 (${subscription.applyQuantity}좌)`}
          />
          <AssetCardItem
            label="청약 신청일"
            value={YYYYMMDD(subscription.subscriptionInfo.allotAt) || ''}
          />
          <AssetCardItem
            label={isApply ? '배정 예정일' : '배정일'}
            value={YYYYMMDD(subscription.subscriptionInfo.securities.issueDate) || ''}
          />
        </div>
        {!isAsset && (
          <SecondaryButton
            onClick={() => {
              setSelectedSubscriptionId(subscription.subscriptionApplyId);
              toggleVisibility();
            }}
            text="상세 보기"
            className="w-full border-gray-300 sm:hidden"
          />
        )}
      </div>
      {selectedSubscriptionId && (
        <MyApplySubscriptionDialog
          isOpen={isVisible}
          isLoading={isWithdrawLoading}
          handleOpen={onCloseDialog}
          handleAction={withdrawSubscription}
          subscriptionId={selectedSubscriptionId}
        />
      )}
    </div>
  );
};
