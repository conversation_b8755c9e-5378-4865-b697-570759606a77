import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import { fetchSubscriptionInquiries } from '@/features/inquiries/api/fetchSubscriptionInquiries';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { deleteIssuerInquiry } from '@/entities/inquiries/api/deleteIssuerInquiry';

import { useToast } from '@/shared/model/useToast';
import { useVisibility } from '@/shared/model/useVisibility';

export const useSubscriptionInquiryList = (securitiesId: string) => {
  const { userId } = useFetchUser();
  const { isVisible: isDeleteVisible, toggleVisibility: toggleDeleteVisibility } = useVisibility();
  const [inquiryId, setInquiryId] = useState<string | null>(null);
  const { successToast, errorToast } = useToast();
  const queryClient = useQueryClient();
  const [perPage, setPerPage] = useState(5);

  const { data: subscriptionInquiries, isLoading } = fetchSubscriptionInquiries(
    securitiesId,
    userId as string,
    {
      page: 1,
      perPage,
    },
  );

  const handleDeleteInquiry = async () => {
    if (!inquiryId) return;

    try {
      await deleteIssuerInquiry(inquiryId);
      successToast({
        title: '문의 삭제 성공',
      });
      await queryClient.invalidateQueries({ queryKey: queries.inquiries._def });
      setInquiryId(null);
      toggleDeleteVisibility();
    } catch (error) {
      errorToast({
        title: '문의 삭제 실패',
      });
    }
  };

  const handleOpenDeleteDialog = (inquiryId: string) => {
    setInquiryId(inquiryId);
    toggleDeleteVisibility();
  };

  return {
    subscriptionInquiries,
    isLoading,
    handleDeleteInquiry,
    handleOpenDeleteDialog,
    isDeleteVisible,
    toggleDeleteVisibility,
    perPage,
    setPerPage,
  };
};
