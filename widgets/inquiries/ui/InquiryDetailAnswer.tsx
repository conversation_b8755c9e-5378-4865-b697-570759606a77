import React from 'react';

import { InquiryHistory } from '@/entities/inquiries/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { Separator } from '@/shared/ui/shadcn/separator';

interface InquiryDetailAnswerProps {
  inquiryDetail: InquiryHistory;
}

export const InquiryDetailAnswer = ({ inquiryDetail }: InquiryDetailAnswerProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <>
      <Separator className="my-12 h-[1px] bg-gray-700" />
      <div className="space-y-6">
        <div className="flex items-center gap-3 sm:justify-between sm:gap-0">
          <p className="rounded bg-gray-900 px-3 py-[3px] text-[10px] font-bold text-white sm:text-xs">
            답변
          </p>
          <p className="text-sm text-gray-700 sm:text-base">
            {YYYYMMDD(inquiryDetail.updatedAt, 'YYYY-MM-DD hh:mm')}
          </p>
        </div>
        <p className="text-sm text-gray-700 sm:text-base">{inquiryDetail?.answer}</p>
      </div>
    </>
  );
};
