import React from 'react';

import { InquiryHistory } from '@/entities/inquiries/types';
import { InquiryAnswerStatusBadge } from '@/entities/inquiries/ui/InquiryAnswerStatusBadge';
import { InquiryCategoryBadge } from '@/entities/inquiries/ui/InquiryCategoryBadge';

import { utilFormats } from '@/shared/lib/utilformats';
import { Separator } from '@/shared/ui/shadcn/separator';

interface InquiryDetailHeaderProps {
  inquiryDetail: InquiryHistory;
}

export const InquiryDetailHeader = ({ inquiryDetail }: InquiryDetailHeaderProps) => {
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="space-y-2 sm:space-y-6">
      <div className="flex items-center gap-3 sm:justify-between sm:gap-0">
        <div className="flex items-center space-x-1 sm:space-x-4">
          <InquiryAnswerStatusBadge answerStatus={inquiryDetail?.answerStatus} />
          <Separator className="hidden h-4 w-[1px] bg-gray-300 sm:block" />
          <InquiryCategoryBadge category={inquiryDetail?.category} />
        </div>
        <p className="text-gray-700">{YYYYMMDD(inquiryDetail?.createdAt)}</p>
      </div>
      <h3 className="text-xl font-bold sm:text-2xl">{inquiryDetail?.title}</h3>
    </div>
  );
};
