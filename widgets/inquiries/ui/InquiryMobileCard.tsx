'use client';

import { TrashIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import React from 'react';

import { InquiryAnswerStatus, InquiryHistory } from '@/entities/inquiries/types';
import { InquiryAnswerStatusBadge } from '@/entities/inquiries/ui/InquiryAnswerStatusBadge';
import { InquiryCategoryBadge } from '@/entities/inquiries/ui/InquiryCategoryBadge';

import { utilFormats } from '@/shared/lib/utilformats';
import { useVisibility } from '@/shared/model/useVisibility';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { Separator } from '@/shared/ui/shadcn/separator';

interface InquiryMobileCardProps {
  inquiry: InquiryHistory;
  handleOpenDeleteDialog: (inquiryId: string) => void;
  isSubscription?: boolean;
}

export const InquiryMobileCard = ({
  inquiry,
  handleOpenDeleteDialog,
  isSubscription = false,
}: InquiryMobileCardProps) => {
  const { YYYYMMDD } = utilFormats();
  const { answerStatus, category, title, createdAt, answer } = inquiry;
  const { isVisible: isAnswerVisible, toggleVisibility: toggleAnswerVisibility } = useVisibility();

  const { routerPush } = useWebViewRouter();

  return (
    <div
      onClick={() => {
        if (isSubscription) {
          toggleAnswerVisibility();
        } else {
          routerPush(`/user/support/inquiry/${inquiry.id}`);
        }
      }}
      className="cursor-pointer"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-[10px] text-xs">
            <p className="text-gray-700">{YYYYMMDD(createdAt)}</p>
            <div className="flex space-x-1">
              <InquiryAnswerStatusBadge answerStatus={answerStatus} />
              <InquiryCategoryBadge category={category} />
            </div>
          </div>
          <h4 className="text-sm font-semibold">{title}</h4>
        </div>
        {answerStatus === InquiryAnswerStatus.REQUEST && (
          <button
            onClick={(e) => {
              e.preventDefault();
              handleOpenDeleteDialog(inquiry.id);
            }}
          >
            <TrashIcon className="h-5 w-5" />
          </button>
        )}
      </div>
      {isAnswerVisible && inquiry.answerStatus === InquiryAnswerStatus.ANSWER && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <div className="mt-6 flex flex-col bg-blue-gray-00">
            <p className="px-5 py-6 text-sm leading-[170%] text-gray-700">{inquiry.content}</p>
            <Separator orientation="horizontal" className="bg-gray-200" />
            <p className="px-5 py-6 text-sm leading-[170%] text-gray-700">{answer}</p>
          </div>
        </motion.div>
      )}
      <Separator className="my-6 h-[1px] bg-gray-200" />
    </div>
  );
};
