import { InquiryListColumn } from '@/widgets/inquiries/ui/InquiryListColumn';
import { InquiryMobileCard } from '@/widgets/inquiries/ui/InquiryMobileCard';

import { SubscriptionInquiryCard } from '@/features/subscription/ui/SubscriptionInquiryCard';

import { ConfirmDialog } from '@/shared/ui/ConfirmDialog';
import { EmptyList } from '@/shared/ui/EmptyList';
import { Loading } from '@/shared/ui/Loading';
import { SecondaryButton } from '@/shared/ui/SecondaryButton';

import { useSubscriptionInquiryList } from '../model/useSubscriptionInquiryList';

export const SubscriptionInquiryList = ({ securitiesId }: { securitiesId: string }) => {
  const {
    subscriptionInquiries,
    isLoading,
    handleDeleteInquiry,
    handleOpenDeleteDialog,
    isDeleteVisible,
    toggleDeleteVisibility,
    perPage,
    setPerPage,
  } = useSubscriptionInquiryList(securitiesId);

  if (isLoading) return <Loading />;

  return (
    <div className="mx-auto my-7 max-w-[800px] space-y-8 px-6 sm:my-20 sm:px-8 ml:max-w-screen-contents ml:px-0">
      <h3 className="text-18 sm:text-24">문의 내역</h3>
      <div>
        <section className="hidden sm:block">
          <InquiryListColumn />
          {subscriptionInquiries?.data.map((inquiry) => (
            <SubscriptionInquiryCard
              key={inquiry.id}
              inquiry={inquiry}
              handleOpenDeleteDialog={handleOpenDeleteDialog}
            />
          ))}
          {subscriptionInquiries?.meta.total === 0 && (
            <EmptyList title="문의하신 내역이 없습니다." className="mt-40" />
          )}
        </section>
        <section className="block sm:hidden">
          {subscriptionInquiries?.data.map((inquiry) => (
            <InquiryMobileCard
              key={inquiry.id}
              inquiry={inquiry}
              handleOpenDeleteDialog={handleOpenDeleteDialog}
              isSubscription
            />
          ))}
          {subscriptionInquiries?.meta.total === 0 && (
            <EmptyList title="문의하신 내역이 없습니다." />
          )}
        </section>
      </div>
      {subscriptionInquiries?.meta?.total !== undefined &&
        subscriptionInquiries.meta.total > 0 &&
        subscriptionInquiries.meta.total > perPage && (
          <div className="mt-[100px] flex justify-center">
            <SecondaryButton
              onClick={() => {
                setPerPage(perPage + 5);
              }}
              text="더 보기"
              className="w-20 border-2 border-gray-300 font-semibold text-gray-900 sm:!h-[60px] sm:w-40"
            />
          </div>
        )}
      <ConfirmDialog
        isOpen={isDeleteVisible}
        handleAction={handleDeleteInquiry}
        handleOpen={toggleDeleteVisibility}
        title="해당 문의를 삭제하시겠습니까?"
        description="삭제하신 내용은 복구하실 수 없습니다."
      />
    </div>
  );
};
