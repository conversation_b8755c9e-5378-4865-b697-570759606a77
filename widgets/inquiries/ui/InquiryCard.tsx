'use client';

import { TrashIcon } from '@heroicons/react/24/outline';
import React from 'react';

import { InquiryHistory } from '@/entities/inquiries/types';
import { InquiryAnswerStatusBadge } from '@/entities/inquiries/ui/InquiryAnswerStatusBadge';
import { InquiryCategoryBadge } from '@/entities/inquiries/ui/InquiryCategoryBadge';

import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';

interface InquiryCardProps {
  inquiry: InquiryHistory;
  handleOpenDeleteDialog: (inquiryId: string) => void;
}

export const InquiryCard = ({ inquiry, handleOpenDeleteDialog }: InquiryCardProps) => {
  const { YYYYMMDD } = utilFormats();
  const { answerStatus, category, title, createdAt } = inquiry;
  const { routerPush } = useWebViewRouter();

  return (
    <div
      className="flex cursor-pointer items-center gap-4 p-6 text-sm"
      onClick={() => routerPush(`/user/support/inquiry-history/${inquiry.id}`)}
    >
      <InquiryAnswerStatusBadge className="min-w-[90px] basis-1/12" answerStatus={answerStatus} />
      <InquiryCategoryBadge className="min-w-[60px] basis-1/12" category={category} />
      <p className="flex-1 font-semibold">{title}</p>
      <p className="min-w-[100px] basis-1/12 text-center">{YYYYMMDD(createdAt, 'YYYY-MM-DD')}</p>
      <div className="flex min-w-[60px] basis-1/12 justify-end">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleOpenDeleteDialog(inquiry.id);
          }}
        >
          {answerStatus === 'REQUEST' && <TrashIcon className="h-5 w-5" />}
        </button>
      </div>
    </div>
  );
};
