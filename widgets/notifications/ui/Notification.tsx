import { AnimatePresence } from 'framer-motion';
import { motion } from 'framer-motion';

import { NotificationButton } from '@/features/notifications/ui/NotificationButton';
import { NotificationList } from '@/features/notifications/ui/NotificationList';

import { useNotification } from '../../../features/notifications/model/useNotification';

export const Notification = ({ userId }: { userId: string }) => {
  const {
    notificationRef,
    notificationButtonRef,
    isVisible,
    toggleVisibility,
    unreadCount,
    notifications,
    ref,
  } = useNotification(userId);

  return (
    <>
      <NotificationButton
        notificationButtonRef={notificationButtonRef}
        toggleVisibility={toggleVisibility}
        isVisible={isVisible}
        unreadCount={unreadCount}
      />
      <AnimatePresence mode="wait">
        {isVisible && (
          <motion.div
            ref={notificationRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="absolute left-[-275px] top-14 z-40 flex h-[640px] w-[320px] flex-col justify-between rounded-[20px] border border-gray-400 bg-white sm:left-[-330px] sm:w-[400px]"
          >
            <NotificationList notifications={notifications} ref={ref} />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
