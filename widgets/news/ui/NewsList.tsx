'use client';

import { useSearchParams } from 'next/navigation';
import React from 'react';

import { fetchNews } from '@/features/news/api/fetchNews';

import { NewsCard } from '@/entities/news/ui/NewsCard';

import { CommonPagination } from '@/shared/ui/CommonPagination';

export const NewsList = () => {
  const searchParams = useSearchParams();
  const page = Number(searchParams.get('page')) || 1;
  const searchType = searchParams.get('searchType') || 'title';
  const keyword = searchParams.get('keyword') || '';
  const createdAt = searchParams.get('createdAt') || '';

  const { data } = fetchNews({
    page,
    title: searchType === 'title' ? keyword : undefined,
    createdAt: createdAt ? createdAt : undefined,
  });

  return (
    <>
      <div className="grid grid-cols-1 gap-y-12 sm:grid-cols-2 sm:gap-x-5 sm:gap-y-16 ml:grid-cols-3 ml:gap-12 ml:gap-x-12 ml:gap-y-20">
        {data?.data.map((news) => <NewsCard key={news.id} news={news} />)}
      </div>
      <div className="my-16 sm:my-[72px] ml:my-[120px]">
        <CommonPagination pageSize={10} page={Number(page)} totalCount={data?.meta.total} />
      </div>
    </>
  );
};
