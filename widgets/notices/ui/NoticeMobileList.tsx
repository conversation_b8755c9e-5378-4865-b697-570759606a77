import { Notice } from '@/entities/notices/types';
import { NoticeMobileCard } from '@/entities/notices/ui/NoticeMobileCard';

import { CommonPagination } from '@/shared/ui/CommonPagination';
import { EmptyList } from '@/shared/ui/EmptyList';

interface NoticeMobileListProps {
  data?: {
    data: Notice[];
    meta: {
      total: number;
    };
  };
  page: number;
}

export const NoticeMobileList = ({ data, page }: NoticeMobileListProps) => {
  return (
    <div className="sm:hidden">
      {data?.data && data?.data.length > 0 ? (
        <>
          <p className="mb-10">
            전체 <span className="font-bold text-primary-500"> {data?.meta.total || 0}</span> 건
          </p>
          {data?.data.map((notice, index) => (
            <NoticeMobileCard
              key={notice.id}
              notice={notice}
              isLast={data.data.length - 1 === index}
            />
          ))}
        </>
      ) : (
        <EmptyList
          title="검색 결과가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
      {data?.data && data?.data.length > 0 && (
        <div className="mt-16">
          <CommonPagination page={Number(page)} pageSize={10} totalCount={data?.meta.total || 0} />
        </div>
      )}
    </div>
  );
};
