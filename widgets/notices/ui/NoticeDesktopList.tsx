import React from 'react';

import { NoticeColumn } from '@/features/notices/ui/NoticeColumn';

import { Notice } from '@/entities/notices/types';

import { DataTable } from '@/shared/ui/DataTable';
import { EmptyList } from '@/shared/ui/EmptyList';

interface NoticeDesktopListProps {
  data?: {
    data: Notice[];
    meta: {
      total: number;
    };
  };
}

export const NoticeDesktopList = ({ data }: NoticeDesktopListProps) => {
  return (
    <div className="hidden sm:block">
      {data?.data && data?.data.length > 0 ? (
        <DataTable<Notice, string> columns={NoticeColumn} data={data} />
      ) : (
        <EmptyList
          title="검색 결과가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
    </div>
  );
};
