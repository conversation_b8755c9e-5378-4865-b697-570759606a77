import React from 'react';

import { Notice } from '@/entities/notices/types';

import { ListResponse } from '@/shared/interface';
import { utilFormats } from '@/shared/lib/utilformats';
import { useWebViewRouter } from '@/shared/model/useWebViewRouter';
import { ContentsMobileTitle } from '@/shared/ui/ContentsMobileTitle';

interface MobileContentsNoticeSwiperProps {
  noticesData?: ListResponse<Notice>;
}

export const MobileContentsNoticeSwiper = ({ noticesData }: MobileContentsNoticeSwiperProps) => {
  const { routerPush } = useWebViewRouter();
  const { YYYYMMDD } = utilFormats();

  return (
    <div className="space-y-3">
      <ContentsMobileTitle title="공지사항" href="/contents/notices" />
      <div>
        {noticesData?.data.map((notice) => (
          <div
            key={notice.id}
            onClick={() => routerPush(`/contents/notices/${notice.id}`)}
            className="flex cursor-pointer items-center justify-between border-b border-gray-200 py-3"
          >
            <p className="line-clamp-1 flex-1 text-sm">{notice.title}</p>
            <span className="w-[68px] text-xs text-gray-500">{YYYYMMDD(notice.createdAt)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
