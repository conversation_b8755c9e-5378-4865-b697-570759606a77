import { Notice } from '@/entities/notices/types';

import { utilFormats } from '@/shared/lib/utilformats';
import { Separator } from '@/shared/ui/shadcn/separator';

export const NoticeDetailHeader = ({ noticeDetail }: { noticeDetail: Notice }) => {
  const { YYYYMMDD } = utilFormats();

  const { createdAt, title } = noticeDetail;

  return (
    <div className="flex flex-col gap-2 sm:gap-6">
      <div className="flex items-center gap-3 text-sm sm:gap-6 sm:text-base">
        <p className="font-semibold text-primary-500">공지사항</p>
        <Separator className="h-4 bg-gray-300" orientation="vertical" />
        <p className="text-sm text-gray-500">{YYYYMMDD(createdAt, 'YYYY-MM-DD hh:mm')}</p>
      </div>
      <h3 className="text-xl font-semibold sm:text-2xl">{title}</h3>
    </div>
  );
};
