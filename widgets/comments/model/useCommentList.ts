import { useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useState } from 'react';

import { fetchComments } from '@/features/comments/api/fetchComments';
import { queries } from '@/features/lib/queries';
import { useFetchUser } from '@/features/users/model/useFetchUser';

import { createComment } from '@/entities/comments/api/createComment';
import { deleteComment } from '@/entities/comments/api/deleteComment';
import { updateComment } from '@/entities/comments/api/updateComment';

import { useToast } from '@/shared/model/useToast';

export const useCommentList = () => {
  const [page, setPage] = useState(1);
  const handlePage = (newPage: number) => setPage(newPage);
  const params = useParams();
  const { user, isGeneral, isCorporate, userId } = useFetchUser();
  const [isLoading, setIsLoading] = useState(false);
  const { successToast, errorToast } = useToast();
  const queryClient = useQueryClient();

  const { data: eventComments } = fetchComments(params.id as string, {
    page,
    perPage: 10,
  });

  const handleCreate = async (comment: string) => {
    if (!user) return;

    setIsLoading(true);
    try {
      if (isGeneral) {
        await createComment({
          contentId: params.id as string,
          content: comment,
          userId: userId as string,
          email: user.userProfile?.email as string,
          mobileNo: user.userProfile?.mobileNumber as string,
        });
      } else if (isCorporate) {
        await createComment({
          contentId: params.id as string,
          content: comment,
          userId: userId as string,
          email: user.userCorporateProfile?.email as string,
          mobileNo: user.userCorporateProfile?.managerMobileNumber as string,
        });
      }

      await queryClient.invalidateQueries({
        queryKey: queries.comments._def,
      });

      successToast({
        title: '댓글이 등록되었습니다.',
      });
    } catch (error: any) {
      errorToast({
        title: error.response.data.message ?? '댓글 등록에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdate = async (commentId: string, comment: string) => {
    if (!user) return;

    setIsLoading(true);

    try {
      await updateComment(commentId, {
        content: comment,
        userId: userId as string,
      });

      await queryClient.invalidateQueries({
        queryKey: queries.comments._def,
      });

      successToast({
        title: '댓글이 수정되었습니다.',
      });
    } catch (error) {
      console.error(error);
      errorToast({
        title: '댓글 수정에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!user) return;

    setIsLoading(true);

    try {
      await deleteComment(commentId, userId as string);

      await queryClient.invalidateQueries({
        queryKey: queries.comments._def,
      });

      successToast({
        title: '댓글이 삭제되었습니다.',
      });
    } catch (error) {
      console.error(error);
      errorToast({
        title: '댓글 삭제에 실패했습니다.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    eventComments,
    handleCreate,
    handleUpdate,
    handleDelete,
    isLoading,
    page,
    handlePage,
  };
};
