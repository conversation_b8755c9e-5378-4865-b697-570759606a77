import { CommentCard } from '@/features/comments/ui/CommentCard';
import { CommentForm } from '@/features/comments/ui/CommentForm';

import { CommonPagination } from '@/shared/ui/CommonPagination';

import { useCommentList } from '../model/useCommentList';

interface CommentListProps {
  isDone?: boolean;
  className?: string;
  isEnableCommentCount?: boolean;
}

export const CommentList = ({ className, isDone, isEnableCommentCount }: CommentListProps) => {
  const { eventComments, handleCreate, handleUpdate, handleDelete, isLoading, page, handlePage } =
    useCommentList();

  const isEmpty = eventComments?.data.length === 0;

  return (
    <div className={className}>
      {!isDone && <CommentForm onSubmit={handleCreate} isLoading={isLoading} />}
      <div className="space-y-6 sm:space-y-7">
        {isEnableCommentCount && (
          <p className="sm:text-24 text-base font-bold">
            댓글 <strong className="text-primary-500">{eventComments?.meta.total ?? 0}</strong>개
          </p>
        )}
        {!isEmpty ? (
          <div>
            {eventComments?.data.map((comment, index) => (
              <CommentCard
                key={comment._id}
                isFirst={index === 0}
                comment={comment}
                isDone={isDone}
                handleUpdate={handleUpdate}
                handleDelete={handleDelete}
                isLoading={isLoading}
              />
            ))}
          </div>
        ) : (
          <div className="border-b border-t border-gray-300 py-6 sm:py-7">
            <p className="my-10 text-center text-base text-gray-400 sm:text-lg">
              등록된 댓글이 없습니다.
            </p>
          </div>
        )}
      </div>
      {!isEmpty && (
        <CommonPagination
          params={false}
          page={page}
          pageSize={10}
          totalCount={eventComments?.meta.total ?? 0}
          setPage={handlePage}
        />
      )}
    </div>
  );
};
