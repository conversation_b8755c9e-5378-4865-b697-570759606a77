'use client';

import { FaqCard } from '@/features/faqs/ui/FaqCard';

import { CommonPagination } from '@/shared/ui/CommonPagination';
import { EmptyList } from '@/shared/ui/EmptyList';

import { useFaqList } from '../model/useFaqList';

export const FaqList = () => {
  const { faqs, selectedFaq, handleFaqClick, page } = useFaqList();

  return (
    <div className="mt-40 space-y-16 sm:mt-32 ml:mt-0">
      <div>
        {faqs.data.map((faq, index) => (
          <FaqCard
            key={faq._id}
            faq={faq}
            isLast={index === faqs.data.length - 1}
            handleFaqClick={handleFaqClick}
            selectedFaq={selectedFaq}
          />
        ))}
      </div>
      {faqs.meta.total > 0 ? (
        <CommonPagination totalCount={faqs.meta.total} pageSize={10} page={page} />
      ) : (
        <EmptyList
          title="검색 결과가 없습니다"
          buttonText="돌아가기"
          className="mb-60 mt-[136px]"
        />
      )}
    </div>
  );
};
