import { useSearchParams } from 'next/navigation';
import { useState } from 'react';

import { fetchFaqs } from '@/features/faqs/api/fetchFaqs';

import { FaqCategory } from '@/entities/faqs/types';

export const useFaqList = () => {
  const searchParams = useSearchParams();
  const page = searchParams.get('page') || 1;
  const searchType = searchParams.get('type') as 'title' | 'content';
  const keyword = searchParams.get('keyword') || '';

  const [selectedFaq, setSelectedFaq] = useState<string | null>(null);

  const handleFaqClick = (faqId?: string) => {
    setSelectedFaq(faqId ?? null);
  };

  const { data: faqs } = fetchFaqs({
    category: searchParams.get('category') as FaqCategory,
    title: searchType === 'title' ? keyword : undefined,
    content: searchType === 'content' ? keyword : undefined,
    page: Number(page),
    perPage: 10,
  });

  return {
    faqs,
    selectedFaq,
    handleFaqClick,
    page: Number(page),
  };
};
