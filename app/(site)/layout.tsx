import { dehydrate, HydrationBoundary } from '@tanstack/react-query';

import { queries } from '@/features/lib/queries';

import { checkAccountExistenceServer } from '@/entities/assets/api/checkAccountExistenceServer';
import { checkCorporateAccountExistenceServer } from '@/entities/assets/api/checkCorporateAccountExistenceServer';
import { getCorporateUserServer } from '@/entities/users/api/getCorporateUserServer';
import { getUserServer } from '@/entities/users/api/getUserServer';

import { GetQueryClient } from '@/shared/lib/getQueryClient';
import { UserCat, UserRole } from '@/shared/types';
import { KycVefiryProvider } from '@/shared/ui/KycVefiryProvider';
import { Footer } from '@/shared/ui/layouts/Footer';
import { Header } from '@/shared/ui/layouts/Header';

import { auth } from '../auth';

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const queryClient = GetQueryClient();
  const session = await auth();

  //로그인 여부 확인
  if (session?.user.accessToken) {
    await queryClient.setQueryData(['session'], session);

    // 일반 회원 확인
    if (session.user.userCat === UserCat.GENERAL) {
      await queryClient.prefetchQuery({
        queryKey: queries.user.profile().queryKey,
        queryFn: () => getUserServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });

      // 투자자 일반 회원 통장 개설 여부 조회
      if (session.user.role === UserRole.UIN) {
        try {
          await queryClient.prefetchQuery({
            queryKey: queries.assets.checkAccountExistence().queryKey,
            queryFn: () => checkAccountExistenceServer(session?.user.accessToken),
            staleTime: Infinity,
            gcTime: Infinity,
          });
        } catch (error) {
          console.error('❌ SSR Account prefetch failed:', error);
          // 실패해도 페이지 렌더링은 계속
        }
      }
    } else if (session.user.userCat === UserCat.CORPORATE) {
      await queryClient.prefetchQuery({
        queryKey: queries.user.corporateProfile().queryKey,
        queryFn: () => getCorporateUserServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });
      // 투자자 일반 회원 통장 개설 여부 조회
      if (session.user.role === UserRole.UIN) {
        await queryClient.prefetchQuery({
          queryKey: queries.assets.checkCorporateAccountExistence().queryKey,
          queryFn: () => checkCorporateAccountExistenceServer(session?.user.accessToken),
          staleTime: Infinity,
          gcTime: Infinity,
        });
      }
    }
  }
  const dehydratedState = dehydrate(queryClient);

  return (
    <HydrationBoundary state={dehydratedState}>
      <KycVefiryProvider>
        <Header />
        <main className="mt-[50px] flex-1 sm:mt-16 ml:mt-[98px]">{children}</main>

        <Footer />
      </KycVefiryProvider>
    </HydrationBoundary>
  );
}
