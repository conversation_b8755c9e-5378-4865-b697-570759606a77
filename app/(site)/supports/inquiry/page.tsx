import { <PERSON>ada<PERSON> } from 'next';
import React from 'react';

import { InquiryView } from '@/views/inquiry/ui/InquiryView';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.inquiry.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.inquiry.description,
    url: pageMetaData.inquiry.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.inquiry.description,
    images: ['/images/og_image.png'],
  },
};

const page = () => {
  return <InquiryView />;
};

export default page;
