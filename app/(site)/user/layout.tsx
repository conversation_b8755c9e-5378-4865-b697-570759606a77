import { UserMobileTab } from '@/shared/ui/layouts/UserMobileTab';
import { UserSidebar } from '@/shared/ui/layouts/UserSidebar';

const RootLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <div className="mx-auto flex min-h-[600px] max-w-screen-lg flex-col gap-4 pb-40 sm:gap-14 ml:my-[90px] ml:h-auto ml:flex-row ml:gap-5 ml:pb-20">
      <UserSidebar />
      <UserMobileTab />
      {children}
    </div>
  );
};

export default RootLayout;
