import { logServerError } from '@artbloc/next-js-logger';
import React from 'react';

import { SubscriptionView } from '@/views/subscriptions/ui/SubscriptionView';

import { getBannersServer } from '@/entities/banners/api/getBannersServer';
import { getSubscriptionsServer } from '@/entities/subscriptions/api/getSubscriptionsServer';
import { SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { emptyResponse, emptySubscriptionsResponse } from '@/shared/interface';

const page = async () => {
  try {
    const [mainBanners, subBanners, subscriptionsWIP, subscriptionsWait, subscriptionsDone] =
      await Promise.all([
        getBannersServer({ type: 'broker_main' }).catch(() => emptyResponse),
        getBannersServer({ type: 'broker_line' }).catch(() => emptyResponse),
        getSubscriptionsServer({
          size: 3,
          subscriptionInfoStatusList: SubscriptionBizStatus.SUB_WIP,
        }).catch(() => emptyResponse),
        getSubscriptionsServer({
          size: 3,
          subscriptionInfoStatusList: SubscriptionBizStatus.SUB_WAIT,
        }).catch(() => emptyResponse),
        getSubscriptionsServer({
          size: 3,
          subscriptionInfoStatusList: SubscriptionBizStatus.SUB_DONE,
        }).catch(() => emptyResponse),
      ]);

    return (
      <SubscriptionView
        mainBanners={mainBanners}
        subBanners={subBanners}
        subscriptionsWIP={subscriptionsWIP}
        subscriptionsWait={subscriptionsWait}
        subscriptionsDone={subscriptionsDone}
      />
    );
  } catch (error) {
    logServerError('subscriptions server error', {
      error,
    });
    return (
      <SubscriptionView
        mainBanners={emptyResponse}
        subBanners={emptyResponse}
        subscriptionsWIP={emptySubscriptionsResponse}
        subscriptionsWait={emptySubscriptionsResponse}
        subscriptionsDone={emptySubscriptionsResponse}
      />
    );
  }
};

export default page;
