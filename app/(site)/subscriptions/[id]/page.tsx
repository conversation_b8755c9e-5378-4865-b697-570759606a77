import { Metadata } from 'next';
import React from 'react';

import { SubscriptionDetailView } from '@/views/subscriptions/ui/SubscriptionDetailView';

import { getSubscriptionServer } from '@/entities/subscriptions/api/getSubscriptionServer';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> => {
  const { id } = await params;
  const subscription = await getSubscriptionServer(id);

  const pageTitle = subscription.securities.securitiesName;
  const pageDescription = pageMetaData.subscriptions.description || pageTitle;

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: `${pageMetaData.subscriptions.url}/${id}`,
      siteName: pageMetaData.default.title,
      locale: 'ko-KR',
      type: 'website',
      images: {
        url: '/images/og_image.png',
        alt: subscription.securities.securitiesName,
        width: 1200,
        height: 630,
      },
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: ['/images/og_image.png'],
    },
  };
};

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const subscription = await getSubscriptionServer(id);

  return <SubscriptionDetailView subscription={subscription} />;
};

export default page;
