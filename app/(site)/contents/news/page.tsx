import { Metadata } from 'next';
import React from 'react';

import { NewsView } from '@/views/news/ui/NewsView';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.news.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.news.description,
    url: pageMetaData.news.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.news.description,
    images: ['/images/og_image.png'],
  },
};

const page = () => {
  return <NewsView />;
};

export default page;
