import React from 'react';

import { EventDetailView } from '@/views/events/ui/EventDetailView';

import { getEventServer } from '@/entities/events/api/getEventServer';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const generateMetadata = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const event = await getEventServer(id);

  const pageTitle = event.title;
  const pageDescription = event.metaDescription || event.title;

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: `${pageMetaData.events.url}/${id}`,
      siteName: pageMetaData.default.title,
      locale: 'ko-KR',
      type: 'website',
      images: {
        url: event.thumbnail.url || '/images/og_image.png',
        alt: event.title,
        width: 1200,
        height: 630,
      },
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: [event.thumbnail.url || '/images/og_image.png'],
    },
  };
};

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const event = await getEventServer(id);

  return <EventDetailView event={event} />;
};

export default page;
