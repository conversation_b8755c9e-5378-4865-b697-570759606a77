import { Metadata } from 'next';
import React from 'react';

import EventsView from '@/views/events/ui/EventsView';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.events.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.events.description,
    url: pageMetaData.events.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.events.description,
    images: ['/images/og_image.png'],
  },
};

const EventsPage = () => {
  return <EventsView />;
};

export default EventsPage;
