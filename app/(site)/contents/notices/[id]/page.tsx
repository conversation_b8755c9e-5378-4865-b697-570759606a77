import 'ckeditor5/ckeditor5.css';
import React from 'react';

import { NoticeDetailView } from '@/views/notices/ui/NoticeDetailView';

import { getNoticeServer } from '@/entities/notices/api/getNoticeServer';

import { pageMetaData } from '@/shared/config/pageMetaData';
import { htmlConvertText } from '@/shared/lib/htmlConvertText';

export const generateMetadata = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const notice = await getNoticeServer(id);

  const pageTitle = notice.title;

  const contentText = htmlConvertText(notice.content);

  const pageDescription = contentText || notice.title;

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: `${pageMetaData.notices.url}/${id}`,
      siteName: pageMetaData.default.title,
      locale: 'ko-KR',
      type: 'website',
      images: {
        url: '/images/og_image.png',
        alt: pageMetaData.default.title,
        width: 1200,
        height: 630,
      },
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: ['/images/og_image.png'],
    },
  };
};

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const notice = await getNoticeServer(id);

  return <NoticeDetailView notice={notice} />;
};

export default page;
