import { Metadata } from 'next';

import { NoticesView } from '@/views/notices/ui/NoticesView';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.notices.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.notices.description,
    url: pageMetaData.notices.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.notices.description,
    images: ['/images/og_image.png'],
  },
};

const page = () => {
  return <NoticesView />;
};

export default page;
