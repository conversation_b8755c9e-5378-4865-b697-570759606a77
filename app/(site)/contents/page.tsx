import { logServerError } from '@artbloc/next-js-logger';
import React from 'react';

import { MobileContentsView } from '@/views/main/ui/MobileContentsView';

import { getCurationsServer } from '@/entities/curations/api/getCurationsServer';
import { getEventsServer } from '@/entities/events/api/getEventsServer';
import { getNewsServer } from '@/entities/news/api/getNewsServer';
import { getNoticesServer } from '@/entities/notices/api/getNoticesServer';

import { emptyResponse } from '@/shared/interface';

const page = async () => {
  try {
    const [events, notices, news, curations] = await Promise.all([
      getEventsServer().catch(() => emptyResponse),
      getNoticesServer().catch(() => emptyResponse),
      getNewsServer().catch(() => emptyResponse),
      getCurationsServer().catch(() => emptyResponse),
    ]);

    return (
      <MobileContentsView events={events} notices={notices} news={news} curations={curations} />
    );
  } catch (error) {
    logServerError('contents server error', {
      error,
    });
    return (
      <MobileContentsView
        events={emptyResponse}
        notices={emptyResponse}
        news={emptyResponse}
        curations={emptyResponse}
      />
    );
  }
};

export default page;
