import { Metadata } from 'next';
import React from 'react';

import { CurationsView } from '@/views/curations/ui/CurationsView';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.curations.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.curations.description,
    url: pageMetaData.curations.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.curations.description,
    images: ['/images/og_image.png'],
  },
};

const page = () => {
  return <CurationsView />;
};

export default page;
