import { Metadata } from 'next';
import React from 'react';

import { CurationDetailView } from '@/views/curations/ui/CurationDetailView';

import { getCurationServer } from '@/entities/curations/api/getCurationServer';

import { pageMetaData } from '@/shared/config/pageMetaData';

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> => {
  const { id } = await params;
  const curation = await getCurationServer(id);

  const pageTitle = curation.title;
  const pageDescription = curation.metaDescription || curation.title;

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: `${pageMetaData.curations.url}/${id}`,
      siteName: pageMetaData.default.title,
      locale: 'ko-KR',
      type: 'website',
      images: {
        url: curation.thumbnail.url || '/images/og_image.png',
        alt: curation.title,
        width: 1200,
        height: 630,
      },
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: [curation.thumbnail.url || '/images/og_image.png'],
    },
  };
};

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const curation = await getCurationServer(id);

  return <CurationDetailView curation={curation} />;
};

export default page;
