import { logServerError } from '@artbloc/next-js-logger/server';
import React from 'react';

import { MainView } from '@/views/main/ui/MainVIew';

import { getBannersServer } from '@/entities/banners/api/getBannersServer';
import { getCurationsServer } from '@/entities/curations/api/getCurationsServer';
import { getSubscriptionsServer } from '@/entities/subscriptions/api/getSubscriptionsServer';
import { SubscriptionBizStatus } from '@/entities/subscriptions/types';

import { emptyResponse, emptySubscriptionsResponse } from '@/shared/interface';

const HomePage = async () => {
  try {
    const [banners, curations, subscriptions] = await Promise.all([
      getBannersServer({ type: 'main_top' }).catch(() => emptyResponse),
      getCurationsServer().catch(() => emptyResponse),
      getSubscriptionsServer({
        size: 2,
        subscriptionInfoStatusList: SubscriptionBizStatus.SUB_WIP,
      }).catch(() => emptyResponse),
    ]);

    return <MainView banners={banners} curations={curations} subscriptions={subscriptions} />;
  } catch (error) {
    logServerError('main server error', {
      error,
    });
    return (
      <MainView
        banners={emptyResponse}
        curations={emptyResponse}
        subscriptions={emptySubscriptionsResponse}
      />
    );
  }
};

export default HomePage;
