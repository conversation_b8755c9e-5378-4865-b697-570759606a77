@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;

  /* iOS 브라우저에서 안전 영역 고려 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.safe-area {
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

@layer base {
  :root {
    --radius: 0.5rem;
  }

  body {
    @apply text-[#212121];
  }
}

@layer components {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;

    @apply px-5 sm:px-8 ml:px-10;
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 모바일 환경에서 스크롤 애니메이션 */

.mobile-marquee {
  overflow: hidden;
  height: 100%;
  width: 100%;
  /* flex: 1; */
}

.mobile-marquee-left {
  display: flex;
  flex-direction: column;
  animation: marquee-left 20s linear infinite;
  gap: 10px;
}

.mobile-marquee-right {
  display: flex;
  flex-direction: column;
  animation: marquee-right 20s linear infinite;
  gap: 10px;
}

/* 새로운 애니메이션 추가 */
@keyframes marquee-left {
  0% {
    transform: translateY(0); /* 원래 위치로 */
  }
  100% {
    transform: translateY(calc(-50%)); /* 아래에서 위로 */
  }
}

@keyframes marquee-right {
  0% {
    transform: translateY(calc(-50%)); /* 아래로 이동 */
  }
  100% {
    transform: translateY(0); /* 위에서 아래로 */
  }
}

/* 데스크톱 환경에서 스크롤 애니메이션 */

.marquee {
  overflow: hidden;
  height: 100%;
  max-width: 387px;
  width: 100%;
  min-width: 387px;
}

.marquee-content {
  display: flex;
  flex-direction: column;
  animation: marquee 20s linear infinite;
  gap: 20px;
}

@keyframes marquee {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(calc(-50% - 10px));
  }
}

input[type='date']::-webkit-datetime-edit,
input[type='date']::-webkit-datetime-edit-fields-wrapper,
input[type='date']::-webkit-datetime-edit-text,
input[type='date']::-webkit-datetime-edit-month-field,
input[type='date']::-webkit-datetime-edit-day-field,
input[type='date']::-webkit-datetime-edit-year-field {
  color: transparent;
}

ol {
  list-style-position: inside;
}

.loader {
  border: 5px solid #eeebeb;
  border-bottom-color: #212121;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.blurred-bottom {
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 70%, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, #000000 70%, rgba(0, 0, 0, 0));
}

.blurred-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
  pointer-events: none;
}

.rdp-day:hover {
  background-color: #f0f0f0;
  border-radius: 12px;
}

input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}

.ck-content {
  display: flow-root; /* 모던한 clearfix 대체 */
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 1em;
  line-height: 1.6;
  word-wrap: break-word;
}

.ck-content .text-big {
  font-size: 1.5em;
  font-weight: 700;
}

.ck-content h1 {
  font-size: 2.38em;
  font-weight: 700;
}

.ck-content h2 {
  font-size: 2em;
  font-weight: 700;
}

.ck-content h3 {
  font-size: 1.72em;
  font-weight: 700;
}

.ck-content h4 {
  font-size: 1.45em;
  font-weight: 700;
}

.ck-content ul,

/* ck-content 내부의 ol, li 스타일 수정 */
.ck-content ol {
  list-style: none;
  counter-reset: list-counter;
  padding-left: 1.5em;
}

.ck-content ol {
  list-style-type: decimal;
}

.ck-content ul {
  list-style-type: disc;
}

.ck-content figure {
  display: table;
  margin: 1em auto;
  text-align: center;
}

.ck-content figure.table {
  min-width: 100% !important;
}

.ck-content figure img {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  min-width: 50px;
}

.ck-content figure.image {
  display: block;
  margin: 0 auto;
  clear: both;
}

.ck-content figure.image img {
  display: block;
  width: 100%;
  height: auto;
}

.ck-content figure.image figcaption {
  text-align: center;
  margin-top: 0.5em;
  font-size: 0.9em;
  color: #666;
}

.ck-content figure.image-style-side {
  display: block;
  float: right;
  width: 50%;
  margin-left: 2em;
  margin-bottom: 1em;
}

.ck-content figure.image-style-align-left {
  display: block;
  float: left;
  width: 50%;
  margin-right: 2em;
  margin-bottom: 1em;
}

.ck-content figure.image-style-align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.ck-content figure.image-style-align-right {
  display: block;
  float: right;
  width: 50%;
  margin-left: 2em;
  margin-bottom: 1em;
}

/* 이미지 크기 스타일 */
.ck-content .image-style-block-quad {
  width: 100%;
  max-width: 100%;
}

.ck-content .image-style-block-wide {
  max-width: 75%;
}

/* 반응형 이미지 처리 */
@media screen and (max-width: 768px) {
  .ck-content figure.image-style-side,
  .ck-content figure.image-style-align-left,
  .ck-content figure.image-style-align-right {
    float: none;
    width: 100%;
    margin: 1em 0;
  }
}

.all-padding {
  @apply px-6 sm:px-8 ml:px-0;
}

@layer utilities {
  .text-60 {
    @apply text-[60px] font-bold leading-[84px];
  }
  .text-50 {
    @apply text-[50px] font-bold leading-[70px];
  }
  .text-44 {
    @apply text-[44px] font-bold leading-[62px];
  }
  .text-40 {
    @apply text-[40px] font-bold leading-[56px];
  }
  .text-32 {
    @apply text-[32px] font-bold leading-[48px];
  }
  .text-28 {
    @apply text-[28px] font-bold leading-[42px];
  }
  .text-26 {
    @apply text-[26px] font-bold leading-[40px];
  }
  .text-24 {
    @apply text-[24px] font-bold leading-[36px];
  }
  .text-20 {
    @apply text-[20px] font-bold leading-[30px];
  }
  .text-18 {
    @apply text-[18px] font-bold leading-[27px];
  }
}
