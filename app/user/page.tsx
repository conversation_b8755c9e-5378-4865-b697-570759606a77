import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import React from 'react';

import { MobileUserView } from '@/views/users/ui/MobileUserView';

import { queries } from '@/features/lib/queries';

import { checkAccountExistenceServer } from '@/entities/assets/api/checkAccountExistenceServer';
import { checkCorporateAccountExistenceServer } from '@/entities/assets/api/checkCorporateAccountExistenceServer';
import { getCorporateUserServer } from '@/entities/users/api/getCorporateUserServer';
import { getUserServer } from '@/entities/users/api/getUserServer';

import { GetQueryClient } from '@/shared/lib/getQueryClient';
import { UserCat } from '@/shared/types';

import { auth } from '../auth';

const page = async () => {
  const session = await auth();
  const queryClient = GetQueryClient();

  //로그인 여부 확인
  if (session?.user.accessToken) {
    // 일반 회원 확인
    if (session.user.userCat === UserCat.GENERAL) {
      await queryClient.prefetchQuery({
        queryKey: queries.user.profile().queryKey,
        queryFn: () => getUserServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });

      await queryClient.prefetchQuery({
        queryKey: queries.assets.checkAccountExistence().queryKey,
        queryFn: () => checkAccountExistenceServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });
    } else if (session.user.userCat === UserCat.CORPORATE) {
      await queryClient.prefetchQuery({
        queryKey: queries.user.corporateProfile().queryKey,
        queryFn: () => getCorporateUserServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });
      await queryClient.prefetchQuery({
        queryKey: queries.assets.checkCorporateAccountExistence().queryKey,
        queryFn: () => checkCorporateAccountExistenceServer(session?.user.accessToken),
        staleTime: Infinity,
        gcTime: Infinity,
      });
    }
  }
  const dehydratedState = dehydrate(queryClient);

  return (
    <HydrationBoundary state={dehydratedState}>
      <MobileUserView />
    </HydrationBoundary>
  );
};

export default page;
