import 'ckeditor5/ckeditor5.css';
import 'ckeditor5/ckeditor5.css';
import React from 'react';

import { getTermsServer } from '@/entities/terms/api/getTermsServer';
import { TermsCategory } from '@/entities/terms/types';

import { TermHeader } from '@/shared/ui/layouts/TermHeader';

const page = async () => {
  const terms = await getTermsServer({ category: TermsCategory.INVESTMENT_RISK_DISCLOSURE });

  const content = terms?.data[0]?.content;
  return (
    <main>
      <TermHeader title="투자 위험 고지" />
      <section className="mx-auto my-20 max-w-screen-contents px-6 sm:my-40 sm:px-8 ml:px-10">
        <div className="ck-content" dangerouslySetInnerHTML={{ __html: content }} />
      </section>
    </main>
  );
};

export default page;
