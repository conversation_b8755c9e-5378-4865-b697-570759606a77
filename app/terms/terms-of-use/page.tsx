import 'ckeditor5/ckeditor5.css';
import { Metadata } from 'next';
import React from 'react';

import { getTermsServer } from '@/entities/terms/api/getTermsServer';
import { TermsCategory } from '@/entities/terms/types';

import { pageMetaData } from '@/shared/config/pageMetaData';
import { TermHeader } from '@/shared/ui/layouts/TermHeader';

// 1. 페이지를 동적으로 설정
export const dynamic = 'force-dynamic';

// 3. 또는 revalidate 설정 추가
export const revalidate = 0;

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.terms.description,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.terms.description,
    url: pageMetaData.terms.termsOfUse.url,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.terms.description,
    images: ['/images/og_image.png'],
  },
};

const TermsOfUsePage = async () => {
  const terms = await getTermsServer({ category: TermsCategory.TERMS_OF_USE });

  const content = terms?.data[0]?.content;
  return (
    <main>
      <TermHeader title="이용약관" />
      <section className="mx-auto my-20 max-w-screen-contents px-6 sm:my-40 sm:px-8 ml:px-10">
        <div className="ck-content" dangerouslySetInnerHTML={{ __html: content }} />
      </section>
    </main>
  );
};

export default TermsOfUsePage;
