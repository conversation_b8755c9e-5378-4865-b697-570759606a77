import 'ckeditor5/ckeditor5.css';
import 'ckeditor5/ckeditor5.css';
import React from 'react';

import { getTermsServer } from '@/entities/terms/api/getTermsServer';
import { TermsCategory } from '@/entities/terms/types';

import { TermHeader } from '@/shared/ui/layouts/TermHeader';

// 1. 페이지를 동적으로 설정
export const dynamic = 'force-dynamic';

// 3. 또는 revalidate 설정 추가
export const revalidate = 0;

const MarketingUsagePage = async () => {
  const terms = await getTermsServer({ category: TermsCategory.MARKETING_USAGE });

  const content = terms?.data[0]?.content;
  return (
    <main>
      <TermHeader title="마케팅 이용약관" />
      <section className="mx-auto my-20 max-w-screen-contents px-6 sm:my-40 sm:px-8 ml:px-10">
        <div className="ck-content" dangerouslySetInnerHTML={{ __html: content }} />
      </section>
    </main>
  );
};

export default MarketingUsagePage;
