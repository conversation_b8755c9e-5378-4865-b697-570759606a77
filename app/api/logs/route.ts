export async function POST(req: Request) {
  const { createWinstonLogger } = await import('@artbloc/next-js-logger/server');

  const clientFileLogger = createWinstonLogger({
    prefix: '[CLIENT]',
    filename: 'client-%DATE%.log',
  });

  const headers = new Headers(req.headers);
  const clientIp = headers.get('x-forwarded-for') || '';
  const userAgent = headers.get('user-agent') || '';

  // 요청 본문 가져오기
  const body = await req.json();

  clientFileLogger.log({
    level: body.level || 'info',
    message: body.message,
    timestamp: body.timestamp,
    ...body.metadata,
    clientIp,
    userAgent,
  });

  return Response.json({ success: true }, { status: 200 });
}
