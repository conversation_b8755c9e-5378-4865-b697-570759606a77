import { logServerError } from '@artbloc/next-js-logger';

import { identityVerify } from '@/features/identity/api/identityVerify';

export async function POST(req: Request) {
  try {
    const { identityVerificationId } = await req.json();
    const verification = await identityVerify(identityVerificationId);

    if (verification.status !== 'VERIFIED') {
      return Response.json(
        {
          success: false,
          message: '본인확인이 완료되지 않았습니다.',
        },
        { status: 400 },
      );
    }

    return Response.json({
      success: true,
      data: verification,
    });
  } catch (error) {
    logServerError('identity-verification', {
      message: error instanceof Error ? error.message : 'Unknown error',
      error,
    });

    return Response.json(
      {
        success: false,
        message: '본인확인 처리 중 오류가 발생했습니다.',
      },
      { status: 500 },
    );
  }
}
