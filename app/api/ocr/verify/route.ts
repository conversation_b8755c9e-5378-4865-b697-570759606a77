import { logServerError } from '@artbloc/next-js-logger';

import { ocrVerify } from '@/features/ocr/api/ocrVerify';

export async function POST(req: Request) {
  try {
    const body = await req.json();

    const verifyResult = await ocrVerify(body);

    return Response.json({
      success: true,
      message: '인증에 성공하였습니다.',
      verifyResult,
    });
  } catch (error) {
    logServerError('OCR 인증 오류', {
      message: error instanceof Error ? error.message : '인증에 실패하였습니다.',
      error,
    });
    return Response.json(
      { success: false, error: error instanceof Error ? error.message : '인증에 실패하였습니다.' },
      { status: 500 },
    );
  }
}
