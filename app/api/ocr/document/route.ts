import { logServerError } from '@artbloc/next-js-logger';

import { ocrDocumentRecognition } from '@/features/ocr/api/ocrDocumentRecognition';

export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    const documentResult = await ocrDocumentRecognition(file);

    return Response.json(documentResult);
  } catch (error) {
    logServerError('EKYC Error:', {
      message: error instanceof Error ? error.message : 'OCR 처리 중 오류가 발생했습니다.',
      error,
    });
    return Response.json(
      { success: false, error: 'OCR 처리 중 오류가 발생했습니다.' },
      { status: 500 },
    );
  }
}
