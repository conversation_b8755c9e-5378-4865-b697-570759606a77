import { NextRequest, NextResponse } from 'next/server';

import { generateAppleClientSecret } from '@/features/auth/lib/generateAppleClientSecret';
import { parseAppleJwt } from '@/features/auth/lib/parseAppleJwt';

import env from '@/shared/lib/env.schema';

const REDIRECT_URI = `${env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback`;

export async function POST(req: NextRequest) {
  const { code, type } = await req.json();

  if (!code) {
    return NextResponse.json({ error: 'No code provided' }, { status: 400 });
  }

  const tokenRes = await fetch('https://appleid.apple.com/auth/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      client_id: type === 'app' ? env.APPLE_BUNDLE_ID : env.NEXT_PUBLIC_APPLE_CLIENT_ID,
      client_secret: generateAppleClientSecret(type), // 아래 참고
      redirect_uri: REDIRECT_URI,
    }),
  });

  const tokenJson = await tokenRes.json();

  if (!tokenJson.id_token) {
    console.error('Apple token response:', tokenJson);
    return NextResponse.json({ error: 'Failed to get id_token' }, { status: 400 });
  }

  const idToken = tokenJson.id_token;

  // 사용자 정보 디코딩
  const userInfo = parseAppleJwt(idToken);

  return NextResponse.json({ response: userInfo }, { status: 200 });
}
