import { NextRequest, NextResponse } from 'next/server';

import env from '@/shared/lib/env.schema';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const mapId = searchParams.get('mapId');
    const service = searchParams.get('service');
    const code = searchParams.get('code');
    const token = req.headers.get('Authorization')?.split(' ')[1];
    const fileName = searchParams.get('fileName') || 'download';

    if (!mapId || !service || !code) {
      return NextResponse.json({ error: '필수 파라미터가 누락되었습니다.' }, { status: 400 });
    }

    // 파일 정보 조회 (JSON 응답)
    const fileInfoResponse = await fetch(
      `${env.NEXT_PUBLIC_NUMIT_LB_URL}/bos/file/service/latest?mapId=${mapId}&service=${service}&code=${code}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!fileInfoResponse.ok) {
      return NextResponse.json({ error: '파일을 찾을 수 없습니다.' }, { status: 404 });
    }

    // JSON 응답 파싱
    const fileInfo = await fileInfoResponse.json();
    // Todo: file URL 확인 필요

    // 실제 파일 URL이 있는지 확인
    if (!fileInfo.downloadUrl && !fileInfo.url && !fileInfo.fileUrl) {
      return NextResponse.json({ error: '파일 다운로드 URL이 없습니다.' }, { status: 404 });
    }

    // 실제 파일 다운로드 URL
    const actualFileUrl = fileInfo.downloadUrl || fileInfo.url || fileInfo.fileUrl;

    // 실제 파일 다운로드
    const fileResponse = await fetch(actualFileUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!fileResponse.ok) {
      return NextResponse.json({ error: '실제 파일 다운로드에 실패했습니다.' }, { status: 500 });
    }

    // 응답 헤더 확인
    const contentType = fileResponse.headers.get('content-type');

    // 파일 데이터를 ArrayBuffer로 가져오기
    const arrayBuffer = await fileResponse.arrayBuffer();

    // 파일 확장자 결정
    let fileExtension = 'pdf'; // 기본값
    let finalContentType = 'application/pdf'; // 기본값

    if (contentType) {
      if (contentType.includes('pdf') || contentType.includes('application/pdf')) {
        fileExtension = 'pdf';
        finalContentType = 'application/pdf';
      } else if (contentType.includes('excel') || contentType.includes('spreadsheet')) {
        fileExtension = 'xlsx';
        finalContentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      } else if (contentType.includes('word')) {
        fileExtension = 'docx';
        finalContentType =
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      } else if (contentType.includes('image')) {
        fileExtension = 'jpg';
        finalContentType = 'image/jpeg';
      }
    }

    const fileNameWithExtension = `${fileName}.${fileExtension}`;

    return new NextResponse(arrayBuffer, {
      headers: {
        'Content-Type': finalContentType,
        'Content-Disposition': `attachment; filename="${fileNameWithExtension}"`,
        'Cache-Control': 'no-cache',
        'Content-Length': arrayBuffer.byteLength.toString(),
      },
    });
  } catch (error) {
    console.error('파일 다운로드 오류:', error);
    return NextResponse.json({ error: '파일 다운로드 중 오류가 발생했습니다.' }, { status: 500 });
  }
}
