'use client';

import * as Sentry from '@sentry/nextjs';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { SessionProvider } from 'next-auth/react';
import React, { useState } from 'react';
import { Toaster } from 'react-hot-toast';

import { NotFoundView } from '@/views/main/ui/NotFoundView';

import { useResponsive } from '@/shared/model/useResponsive';
import { LayoutManager } from '@/shared/ui/layouts/LayoutManager';
import { MobileBottomGnb } from '@/shared/ui/layouts/MobileBottomGnb';

const Provider = ({ children }: { children: React.ReactNode }) => {
  const [client] = useState(
    new QueryClient({
      defaultOptions: {
        queries: {
          retry: 1,
          staleTime: 60 * 1000 * 60 * 24,
        },
      },
    }),
  );

  useResponsive();

  return (
    <Sentry.ErrorBoundary fallback={<NotFoundView />}>
      <QueryClientProvider client={client}>
        <SessionProvider>
          <LayoutManager />
          {children}
          <ReactQueryDevtools />
          <MobileBottomGnb />
          <Toaster />
        </SessionProvider>
      </QueryClientProvider>
    </Sentry.ErrorBoundary>
  );
};

export default Provider;
