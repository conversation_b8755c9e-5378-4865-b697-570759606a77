import type { Metadata, Viewport } from 'next';
import localFont from 'next/font/local';
import Script from 'next/script';

import * as gtag from '@/shared/lib/gtag';
import { pageMetaData } from '@/shared/config/pageMetaData';

import './globals.css';
import Provider from './provider';

const pretendard = localFont({
  src: '../public/fonts/PretendardVariable.woff2',
  display: 'swap',
  weight: '45 920',
  variable: '--font-pretendard',
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  viewportFit: 'cover',
  userScalable: false,
};

export const metadata: Metadata = {
  title: pageMetaData.default.title,
  description: pageMetaData.default.description,
  metadataBase: pageMetaData.default.metadataBase,
  openGraph: {
    title: pageMetaData.default.title,
    description: pageMetaData.default.description,
    url: pageMetaData.default.metadataBase,
    siteName: pageMetaData.default.title,
    locale: 'ko-KR',
    type: 'website',
    images: [
      {
        url: '/images/og_image.png',
        width: 1200,
        height: 630,
        alt: pageMetaData.default.title,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: pageMetaData.default.title,
    description: pageMetaData.default.description,
    images: ['/images/og_image.png'],
  },
  other: {
    'naver-site-verification': '508705d782912981275b2a668edb2802b7a2840c',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${pretendard.variable} flex min-h-screen-mobile touch-manipulation flex-col font-pretendard antialiased sm:min-h-screen`}
      >
        <Script src={`https://www.googletagmanager.com/gtag/js?id=${gtag.GA_TRACKING_ID}`} />
        <Script
          id="gtag-init"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${gtag.GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
          `,
          }}
        />

        <Script
          src="https://developers.kakao.com/sdk/js/kakao.js"
          strategy="beforeInteractive" // 또는 "afterInteractive"
        />
        <Script
          src="https://static.nid.naver.com/js/naveridlogin_js_sdk_2.0.2.js"
          strategy="beforeInteractive"
        />

        <Provider>{children}</Provider>
      </body>
    </html>
  );
}
