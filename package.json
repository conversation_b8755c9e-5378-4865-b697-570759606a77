{"name": "numit_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3020", "build": "next build", "start": "next start ", "lint": "next lint", "test": "vitest"}, "dependencies": {"@artbloc/next-js-logger": "^1.3.7", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.10.0", "@lukemorales/query-key-factory": "^1.3.4", "@next/bundle-analyzer": "^15.2.0", "@portone/browser-sdk": "^0.0.13", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/nextjs": "^8.48.0", "@tanstack/react-query": "^5.62.16", "@tanstack/react-query-devtools": "^5.62.16", "@tanstack/react-table": "^8.20.6", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/html-to-text": "^9.0.4", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.15", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@use-funnel/next": "^0.0.13", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.7.9", "ckeditor5": "^44.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "framer-motion": "^12.0.3", "html-to-text": "^9.0.5", "import-in-the-middle": "^1.12.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "next": "15.2.3", "next-auth": "5.0.0-beta.25", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "react": "^19.0.0", "react-daum-postcode": "^3.2.0", "react-day-picker": "^9.5.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-intersection-observer": "^9.15.0", "require-in-the-middle": "^7.4.0", "sharp": "^0.34.2", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9.17.0", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^3.0.9"}}