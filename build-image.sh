# build-image.sh

#!/bin/bash
set -e

# 🔧 사용자 정의 변수
HARBOR_DOMAIN="reg.gcp.dev.ixo.tessa.art"
HARBOR_PROJECT="numit"
IMAGE_NAME="numit-web"
DOCKERFILE="Dockerfile-dev"
IMAGE="$HARBOR_DOMAIN/$HARBOR_PROJECT/$IMAGE_NAME"

# 🔐 Harbor 로그인 (필요 시)
echo "👉 Docker login 필요 시 다음 명령 실행:"
echo "docker login $HARBOR_DOMAIN"

# 🛠️ buildx 빌더 초기화 (존재하지 않으면 생성)
docker buildx inspect multi-builder &>/dev/null || docker buildx create --use --name multi-builder
docker buildx use multi-builder
docker buildx inspect --bootstrap

# 🚀 멀티플랫폼 빌드 및 Push
echo "🛠️ Building and pushing multi-platform image to $IMAGE:latest..."
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t "$IMAGE:latest" \
  -f "$DOCKERFILE" \
  --push .

echo "✅ Done: Multi-arch image pushed to $IMAGE:latest"