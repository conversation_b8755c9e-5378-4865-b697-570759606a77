import type { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';

export default {
  darkMode: ['class'],
  content: [
    './views/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './widgets/**/*.{js,ts,jsx,tsx,mdx}',
    './features/**/*.{js,ts,jsx,tsx,mdx}',
    './entities/**/*.{js,ts,jsx,tsx,mdx}',
    './shared/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      height: {
        'auth-screen-mobile': 'calc(100dvh - (128px))',
        'auth-screen-desktop': 'calc(100vh - (98px))',
        'screen-mobile': 'calc(100vh - (64px + 408px))',
        'screen-desktop': 'calc(100vh - (98px + 408px))',
        'mobile-non-gnb': 'calc(100dvh - 50px)',
      },
      minHeight: {
        'screen-desktop': 'calc(100vh - (98px + 408px))',
        'screen-mobile': 'calc(100dvh - 64px)',
        'auth-screen-mobile': 'calc(100dvh - (64px))',
        'mobile-non-gnb': 'calc(100dvh - 90px)',
      },
      padding: {
        sm: '20px',
        md: '32px',
        lg: '360px',
      },
      margin: {
        desktop: '360px',
      },
      maxWidth: {
        'screen-md': '794px',
        'screen-contents': '996px',
        'screen-full': '1400px',
        'screen-lg': '1200px',
        'screen-test': '590px',
      },

      fontFamily: {
        pretendard: ['var(--font-pretendard)'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'custom-gradient':
          'linear-gradient(91.91deg, rgba(189, 236, 252, 1) 0%, rgba(160, 222, 250, 1) 100%)',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        'light-gray': '#EDEFF0',
        'blue-gray-00': '#f5f7f8',
        'blue-gray-50': '#ECEFF1',
        'blue-gray-100': '#e6ebf5',
        'blue-gray-200': '#c5cee0',
        'blue-gray-300': '#a5b2cc',
        'blue-gray-400': '#8897b5',
        'blue-gray-500': '#607d8b',
        'blue-gray-600': '#4f6080',
        'blue-gray-700': '#374663',
        'blue-gray-800': '#212b40',
        'blue-gray-900': '#1a202b',
        'green-00': '#F3FAF4',
        'green-50': '#E8F5E9',
        'green-100': '#C8E6C9',
        'green-500': '#4caf50',
        'green-600': '#43a047',
        'gray-50': '#fafafa',
        'gray-150': '#ECEEED',
        'gray-200': '#EEEEEE',
        'gray-300': '#E0E0E0',
        'gray-400': '#BDBDBD',
        'gray-500': '#9e9e9e',
        'gray-600': '#757575',
        'gray-700': '#616161',
        'gray-900': '#212121',
        'primary-00': '#f0faff',
        'primary-100': '#E1F5FE',
        'primary-200': '#03A9F4',
        'primary-500': '#2196f3',
        'primary-600': '#039BE5',
        'blue-50': '#E3F2FD',
        'blue-500': '#2196f3',
        'red-500': '#F44336',
        'amber-700': '#ffa000',
        'purple-500': '#9C27B0',
        'pink-00': '#FFF6F8',
        'pink-50': '#FED4E0',
        'pink-400': '#FF2864',
        'pink-500': '#EE0749',
        'purple-00': '#F9F6FF',
        'deep-purple-00': '#F9F6FF',
        'deep-purple-50': '#E6D6FD',
        'deep-purple-500': '#6C14F6',
        'cyan-00': '#EFFBFD',
        'cyan-100': '#B2EBF2',
        'cyan-500': '#00BCD4',
        'sky-blue': 'rgba(225, 245, 254, 0.7)',
        kakao: '#FEE501',
        naver: '#03C731',
        apple: '#050708d9',
        'lime-500': '#DAF734',
      },
      backdropBlur: {
        '24': '24px',
      },
      boxShadow: {
        custom: '0px 1px 2px -1px rgba(0, 0, 0, 0.1), 0px 1px 3px 0px rgba(0, 0, 0, 0.1)',
      },
      fontSize: {
        'display-1': ['60px', '84px'],
        'display-2': ['50px', '70px'],
        'display-3': ['44px', '62px'],
        'display-4': ['40px', '56px'],
        'display-5': ['28px', '56px'],
        'display-6': ['24px', '36px'],
        'title-1': ['32px', '48px'],
        'title-2': ['28px', '42px'],
        'title-3': ['20px', '30px'],
        'title-4': ['18px', '27px'],
        'subtitle-1': ['18px', '27px'],
        'body-1': ['18px', '27px'],
        'body-2': ['16px', '24px'],
        'body-3': ['14px', '21px'],
      },
      screens: {
        sm: '744px',
        md: '996px',
        tb: '1040px',
        ml: '1280px',
        lg: '1440px',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
        fadeOut: 'fadeOut 0.3s ease-in-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;
