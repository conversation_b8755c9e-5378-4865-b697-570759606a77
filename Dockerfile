# Builder stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Define build-time environment variable
ARG NODE_ENV=dev
ENV NODE_ENV=${NODE_ENV}

# pnpm 전역 설치
RUN npm i -g pnpm@^9.0.0

# Copy .npmrc first for authentication
COPY .npmrc ./

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Copy the appropriate environment file based on NODE_ENV
# Default to `.env` if `.env.${NODE_ENV}` does not exist
RUN if [ -f .env.${NODE_ENV} ]; then \
      cp .env.${NODE_ENV} .env; \
    else \
      echo "Warning: .env.${NODE_ENV} not found, using default .env"; \
      cp .env .env; \
    fi

# Build the Next.js application
RUN pnpm build

# Production stage
FROM node:20-alpine AS production

# Set working directory
WORKDIR /app

# Define runtime environment variable
ARG NODE_ENV=dev
ENV NODE_ENV=${NODE_ENV}

# pnpm 전역 설치
RUN npm i -g pnpm@^9.0.0

# Copy .npmrc first for authentication
COPY .npmrc ./

# Copy necessary files from builder stage
COPY --from=builder /app/package.json /app/pnpm-lock.yaml ./
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.env ./.env

# Expose port and set entrypoint
EXPOSE 3000
ENTRYPOINT ["pnpm", "start"]