import { auth } from '@/app/auth';
import { logMiddlewareRequest, logMiddlewareResponse } from '@artbloc/next-js-logger/edge';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const start = Date.now();

  logMiddlewareRequest(request);

  if (request.nextUrl.pathname === '/health') {
    return NextResponse.json({ status: 'ok' }, { status: 200 });
  }

  const response = NextResponse.next();
  const authInfo = await auth();
  const path = request.nextUrl.pathname;

  // Public routes
  if (path === '/sign-in' || path === '/sign-up') {
    if (authInfo) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    return response;
  }

  if (path.includes('/user/') && !authInfo) {
    return NextResponse.redirect(new URL('/sign-in?invalid=true', request.url));
  }

  const duration = Date.now() - start;
  logMiddlewareResponse(request, response, duration);

  return response;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
